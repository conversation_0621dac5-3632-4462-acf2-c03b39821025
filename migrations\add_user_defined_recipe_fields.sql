-- 添加用户自定义食谱字段的SQL脚本
-- 此脚本用于向recipes表添加is_user_defined和priority字段

-- 检查字段是否已存在，不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = 'is_user_defined' AND Object_ID = Object_ID('recipes'))
BEGIN
    ALTER TABLE recipes ADD is_user_defined BIT NOT NULL DEFAULT 0;
    PRINT '已添加字段: is_user_defined';
END
ELSE
BEGIN
    PRINT '字段已存在: is_user_defined';
END

-- 检查字段是否已存在，不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = 'priority' AND Object_ID = Object_ID('recipes'))
BEGIN
    ALTER TABLE recipes ADD priority INT NOT NULL DEFAULT 0;
    PRINT '已添加字段: priority';
END
ELSE
BEGIN
    PRINT '字段已存在: priority';
END
