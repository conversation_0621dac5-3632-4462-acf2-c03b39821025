#!/usr/bin/env python3
"""
测试食堂日常检查模块的员工扫码上传照片功能

这个脚本用于测试优化后的功能：
1. 按三个时段分类（晨检、午检、晚检）
2. 员工扫码上传照片
3. 管理员进行点评
4. 不分具体检查项目
"""

from datetime import datetime
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models_daily_management import DailyLog, Photo
from app.models import AdministrativeArea

# 配置
SCHOOL_ID = 37  # 城南小学
LOG_ID = 37     # 今天的日志ID

def test_database_setup():
    """测试数据库设置"""
    print("=== 测试数据库设置 ===")

    app = create_app()
    with app.app_context():
        try:
            # 检查学校是否存在
            school = AdministrativeArea.query.get(SCHOOL_ID)
            if school:
                print(f"✓ 学校存在: {school.name}")
            else:
                print(f"✗ 学校不存在 (ID: {SCHOOL_ID})")
                return False

            # 检查日志是否存在
            log = DailyLog.query.get(LOG_ID)
            if log:
                print(f"✓ 日志存在: {log.log_date}")
            else:
                print(f"✗ 日志不存在 (ID: {LOG_ID})")
                return False

            # 检查照片表
            photo_count = Photo.query.count()
            print(f"✓ 照片表记录数: {photo_count}")

            return True

        except Exception as e:
            print(f"✗ 数据库测试失败: {str(e)}")
            return False

    print()

def test_routes_with_flask_client():
    """使用Flask测试客户端测试路由"""
    print("=== 测试路由配置 ===")

    app = create_app()
    app.config['TESTING'] = True

    with app.test_client() as client:
        # 测试公开上传页面
        for inspection_type in ['morning', 'noon', 'evening']:
            url = f"/daily-management/public/inspections/upload/{SCHOOL_ID}/{LOG_ID}/{inspection_type}"

            try:
                response = client.get(url)
                print(f"{inspection_type}检查上传页面: {response.status_code}")

                if response.status_code == 200:
                    content = response.get_data(as_text=True)
                    if 'camera-zone' in content:
                        print(f"  ✓ 包含拍照区域")
                    if 'photo-preview-container' in content:
                        print(f"  ✓ 包含照片预览")
                    if 'upload-btn' in content:
                        print(f"  ✓ 包含上传按钮")
                    if inspection_type in content:
                        print(f"  ✓ 包含检查类型")
                else:
                    print(f"  ✗ 页面访问失败")

            except Exception as e:
                print(f"  ✗ 访问出错: {str(e)}")

        print()

        # 测试API端点
        print("=== 测试API端点 ===")

        # 测试上传API（应该返回400因为缺少数据）
        upload_url = f"/api/v2/inspections/public/upload-photos/{SCHOOL_ID}/{LOG_ID}/morning"
        try:
            response = client.post(upload_url, data={})
            print(f"公开上传API: {response.status_code}")
            if response.status_code == 400:
                print("  ✓ API端点存在（返回400是正常的）")
        except Exception as e:
            print(f"  ✗ 上传API测试失败: {str(e)}")

        # 测试评分API
        rating_url = "/api/v2/photos/public/rate"
        try:
            response = client.post(rating_url,
                                 json={'photo_id': 999, 'rating': 5},
                                 content_type='application/json')
            print(f"公开评分API: {response.status_code}")
            if response.status_code in [400, 404]:
                print("  ✓ API端点存在（返回错误是正常的）")
        except Exception as e:
            print(f"  ✗ 评分API测试失败: {str(e)}")

    print()

def test_template_files():
    """测试模板文件是否存在"""
    print("=== 测试模板文件 ===")

    template_files = [
        'app/templates/daily_management/public_upload_inspection_photo.html',
        'app/templates/daily_management/simplified_inspection.html',
        'app/templates/daily_management/scan_upload_entry.html'
    ]

    for template_file in template_files:
        if os.path.exists(template_file):
            print(f"✓ {template_file}")
        else:
            print(f"✗ {template_file}")

    print()

def main():
    """主测试函数"""
    print("食堂日常检查模块 - 员工扫码上传照片功能测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"学校ID: {SCHOOL_ID}")
    print(f"日志ID: {LOG_ID}")
    print()

    # 运行所有测试
    if test_database_setup():
        test_routes_with_flask_client()
        test_template_files()

    print("=" * 50)
    print("测试完成！")
    print()
    print("功能说明：")
    print("1. 员工可以通过扫描二维码访问公开上传页面")
    print("2. 上传页面支持拍照、选择照片、拖拽上传")
    print("3. 管理员可以在简化检查记录页面查看和评分")
    print("4. 系统按三个时段分类，不需要选择具体检查项目")
    print()
    print("优化特点：")
    print("- 简化的用户界面，移动端友好")
    print("- 实时照片预览和删除功能")
    print("- 进度条显示上传状态")
    print("- 管理员可以快速点击星级评分")
    print("- 支持拖拽上传和多种文件选择方式")

if __name__ == "__main__":
    main()
