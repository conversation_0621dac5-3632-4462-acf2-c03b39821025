from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, FileField, BooleanField, FloatField, IntegerField
from wtforms.validators import DataRequired, Optional, NumberRange
from flask_wtf.file import FileAllowed

class RecipeForm(FlaskForm):
    """食谱表单"""
    name = StringField('食谱名称', validators=[DataRequired(message='请输入食谱名称')])
    category_id = SelectField('食谱分类', coerce=int, validators=[Optional()])
    meal_type = SelectField('适用餐次', choices=[
        ('', '-- 请选择餐次 --'),
        ('早餐', '早餐'),
        ('午餐', '午餐'),
        ('晚餐', '晚餐'),
        ('早餐,午餐', '早餐,午餐'),
        ('午餐,晚餐', '午餐,晚餐'),
        ('早餐,午餐,晚餐', '早餐,午餐,晚餐')
    ], validators=[Optional()])
    description = TextAreaField('食谱描述', validators=[Optional()])
    main_image = FileField('食谱图片', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], '只允许上传图片!')
    ])
    is_user_defined = BooleanField('标记为用户自定义食谱')
    status = SelectField('状态', choices=[(1, '启用'), (0, '停用')], coerce=int, default=1)
