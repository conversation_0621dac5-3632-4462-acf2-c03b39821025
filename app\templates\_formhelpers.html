{% macro render_field(field) %}
  <div class="form-group">
    {{ field.label }}
    {{ field(**kwargs)|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_checkbox(field) %}
  <div class="form-group form-check">
    {{ field(class="form-check-input")|safe }}
    {{ field.label(class="form-check-label") }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_radio(field) %}
  <div class="form-group">
    {{ field.label }}
    <div>
      {% for subfield in field %}
        <div class="form-check form-check-inline">
          {{ subfield(class="form-check-input")|safe }}
          {{ subfield.label(class="form-check-label") }}
        </div>
      {% endfor %}
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_select(field) %}
  <div class="form-group">
    {{ field.label }}
    {{ field(class="form-control")|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_textarea(field) %}
  <div class="form-group">
    {{ field.label }}
    {{ field(class="form-control", rows=5)|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_date(field) %}
  <div class="form-group">
    {{ field.label }}
    <div class="input-group date" id="{{ field.id }}_datepicker">
      {{ field(class="form-control datepicker")|safe }}
      <div class="input-group-append">
        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
      </div>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_datetime(field) %}
  <div class="form-group">
    {{ field.label }}
    <div class="input-group date" id="{{ field.id }}_datetimepicker">
      {{ field(class="form-control datetimepicker")|safe }}
      <div class="input-group-append">
        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
      </div>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_file(field) %}
  <div class="form-group">
    {{ field.label }}
    <div class="custom-file">
      {{ field(class="custom-file-input")|safe }}
      <label class="custom-file-label" for="{{ field.id }}">选择文件</label>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}
