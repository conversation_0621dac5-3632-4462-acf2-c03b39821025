{% macro daily_management_header(title, school, log=None, active_tab=None) %}
<!-- 学校信息和标题 -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        {% if school %}
        <p class="text-primary mb-0">
            <i class="fas fa-school mr-1"></i> 当前学校: <strong>{{ school.name }}</strong>
        </p>
        {% endif %}
    </div>

    <!-- 打印按钮 -->
    {% if log %}
    <div>
        <a href="{{ url_for('daily_management.print_log', log_id=log.id) }}" target="_blank" class="btn btn-sm btn-outline-primary">
            <i class="fas fa-print mr-1"></i> 打印
        </a>
    </div>
    {% endif %}
</div>

<!-- 导航菜单 -->
{% if log %}
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-calendar-alt mr-1"></i> 日志日期: {{ log.log_date|safe_datetime('%Y-%m-%d') }}
        </h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">日志操作:</div>
                <a class="dropdown-item" href="{{ url_for('daily_management.edit_log', date_str=log.log_date|safe_datetime('%Y-%m-%d')) }}">
                    <i class="fas fa-edit fa-sm fa-fw mr-2 text-gray-400"></i> 编辑日志
                </a>
                <a class="dropdown-item" href="{{ url_for('daily_management.print_log', log_id=log.id) }}" target="_blank">
                    <i class="fas fa-print fa-sm fa-fw mr-2 text-gray-400"></i> 打印日志
                </a>
                <div class="dropdown-divider"></div>
                <div class="dropdown-header">检查记录操作:</div>
                <a class="dropdown-item" href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='morning') }}">
                    <i class="fas fa-qrcode fa-sm fa-fw mr-2 text-gray-400"></i> 晨检二维码
                </a>
                <a class="dropdown-item" href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='noon') }}">
                    <i class="fas fa-qrcode fa-sm fa-fw mr-2 text-gray-400"></i> 午检二维码
                </a>
                <a class="dropdown-item" href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='evening') }}">
                    <i class="fas fa-qrcode fa-sm fa-fw mr-2 text-gray-400"></i> 晚检二维码
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}">
                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i> 今日日志
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="nav-tabs-boxed">
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_tab == 'daily_log' else '' }}"
                       href="{{ url_for('daily_management.edit_log', date_str=log.log_date|safe_datetime('%Y-%m-%d')) }}">
                        <i class="fas fa-clipboard-list mr-1"></i> 日志详情
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_tab == 'inspections' else '' }}"
                       href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}">
                        <i class="fas fa-tasks mr-1"></i> 检查记录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_tab == 'companions' else '' }}"
                       href="{{ url_for('daily_management.companions', log_id=log.id) }}">
                        <i class="fas fa-utensils mr-1"></i> 陪餐记录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_tab == 'trainings' else '' }}"
                       href="{{ url_for('daily_management.trainings', log_id=log.id) }}">
                        <i class="fas fa-chalkboard-teacher mr-1"></i> 培训记录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_tab == 'events' else '' }}"
                       href="{{ url_for('daily_management.events', log_id=log.id) }}">
                        <i class="fas fa-calendar-day mr-1"></i> 特殊事件
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_tab == 'issues' else '' }}"
                       href="{{ url_for('daily_management.issues', log_id=log.id) }}">
                        <i class="fas fa-exclamation-triangle mr-1"></i> 问题记录
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
{% else %}
<!-- 没有日志ID时的导航 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-compass mr-1"></i> 食堂日常管理导航
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-xl-2 col-md-4 mb-4">
                <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="text-decoration-none">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">日志管理</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <i class="fas fa-clipboard-list fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <a href="{{ url_for('daily_management.auto_inspections') }}" class="text-decoration-none">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">检查记录</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <i class="fas fa-tasks fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <a href="{{ url_for('daily_management.scan_upload_entry') }}" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2" style="background: linear-gradient(135deg, #f8f9fc, #ffffff); border-left: 0.25rem solid #36b9cc !important;">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">扫码上传</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <i class="fas fa-qrcode fa-2x"></i>
                                    </div>
                                    <div class="mt-2">
                                        <span class="badge badge-info">新功能</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <a href="{{ url_for('daily_management.auto_companions') }}" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">陪餐记录</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <i class="fas fa-utensils fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <a href="{{ url_for('daily_management.auto_trainings') }}" class="text-decoration-none">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">培训记录</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <i class="fas fa-chalkboard-teacher fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <a href="{{ url_for('daily_management.auto_events') }}" class="text-decoration-none">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">特殊事件</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <i class="fas fa-calendar-day fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <a href="{{ url_for('daily_management.auto_issues') }}" class="text-decoration-none">
                    <div class="card border-left-secondary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">问题记录</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endmacro %}
