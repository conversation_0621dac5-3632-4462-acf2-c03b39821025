-- 检查 inspection_templates 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'inspection_templates')
BEGIN
    -- 创建 inspection_templates 表
    CREATE TABLE inspection_templates (
        id INT PRIMARY KEY IDENTITY(1,1),
        name NVARCHAR(100) NOT NULL,
        description NVARCHAR(255),
        category NVARCHAR(50) NOT NULL,
        items NVARCHAR(MAX) NOT NULL,
        is_default BIT DEFAULT 0,
        created_by INT,
        area_id INT,
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_inspection_template_created_by FOREIGN KEY (created_by) REFERENCES users(id),
        CONSTRAINT FK_inspection_template_area FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
    );

    -- 创建索引
    CREATE INDEX idx_inspection_templates_category ON inspection_templates(category);
    CREATE INDEX idx_inspection_templates_is_default ON inspection_templates(is_default);
    CREATE INDEX idx_inspection_templates_area_id ON inspection_templates(area_id);

    -- 插入默认模板
    INSERT INTO inspection_templates (name, description, category, items, is_default, created_at, updated_at)
    VALUES 
    (N'卫生检查标准模板', N'食堂日常卫生检查标准模板', N'卫生检查', 
     N'[
        {"name": "地面卫生", "description": "检查地面是否清洁、无垃圾、无积水", "required": true},
        {"name": "操作台卫生", "description": "检查操作台是否清洁、无油污、无杂物", "required": true},
        {"name": "设备卫生", "description": "检查设备是否清洁、无油污、无积尘", "required": true},
        {"name": "食材存储", "description": "检查食材存储是否规范、生熟分开、标签清晰", "required": true},
        {"name": "人员卫生", "description": "检查人员着装是否规范、个人卫生是否良好", "required": true},
        {"name": "餐具消毒", "description": "检查餐具是否清洁、消毒是否到位", "required": true}
     ]', 
     1, GETDATE(), GETDATE()),

    (N'食品安全检查模板', N'食堂食品安全检查标准模板', N'食品安全', 
     N'[
        {"name": "食材新鲜程度", "description": "检查食材是否新鲜、无变质", "required": true},
        {"name": "食材存储温度", "description": "检查冷藏、冷冻食材温度是否符合要求", "required": true},
        {"name": "食材保质期", "description": "检查食材是否在保质期内", "required": true},
        {"name": "留样管理", "description": "检查留样是否规范、标签是否清晰", "required": true},
        {"name": "从业人员卫生", "description": "检查从业人员健康证明、个人卫生", "required": true},
        {"name": "加工操作规范", "description": "检查加工操作是否符合规范", "required": true}
     ]', 
     1, GETDATE(), GETDATE()),

    (N'设备设施检查模板', N'食堂设备设施检查标准模板', N'设备设施', 
     N'[
        {"name": "冷藏设备运行", "description": "检查冷藏设备是否正常运行、温度是否达标", "required": true},
        {"name": "炊具设备状态", "description": "检查炊具设备是否正常运行、无安全隐患", "required": true},
        {"name": "消防设施状态", "description": "检查消防设施是否完好、无过期", "required": true},
        {"name": "给排水系统", "description": "检查给排水系统是否通畅、无堵塞", "required": true},
        {"name": "通风系统运行", "description": "检查通风系统是否正常运行、无异味", "required": true},
        {"name": "照明系统运行", "description": "检查照明系统是否正常运行、亮度是否足够", "required": true}
     ]', 
     1, GETDATE(), GETDATE()),

    (N'人员管理检查模板', N'食堂人员管理检查标准模板', N'人员管理', 
     N'[
        {"name": "工作服着装", "description": "检查工作服是否干净整洁、着装是否规范", "required": true},
        {"name": "个人卫生状况", "description": "检查个人卫生是否良好、无不良习惯", "required": true},
        {"name": "健康证明检查", "description": "检查健康证明是否有效、无过期", "required": true},
        {"name": "操作规范执行", "description": "检查操作规范是否严格执行", "required": true},
        {"name": "岗位培训记录", "description": "检查岗位培训记录是否完整", "required": true},
        {"name": "人员配备情况", "description": "检查人员配备是否合理、无缺岗", "required": true}
     ]', 
     1, GETDATE(), GETDATE());

    PRINT '成功创建 inspection_templates 表并插入默认数据';
END
ELSE
BEGIN
    PRINT 'inspection_templates 表已存在，无需创建';
END
