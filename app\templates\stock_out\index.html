{% extends 'base.html' %}

{% block title %}出库单管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 出库流程引导 -->
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-sign-out-alt"></i> 食材出库 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle" style="font-size: 1.2rem; margin-right: 10px;"></i> <strong>提示：</strong> 食材出库是食堂管理的最后环节，正确记录出库信息有助于库存管理和食材溯源。
            </div>

            <div class="d-flex justify-content-between mt-3" style="margin-top: 10px;">
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-clipboard-list"></i> 消耗计划</p>
                    <small>已制定食材消耗计划</small>
                    <div class="mt-2">
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回消耗计划
                        </a>
                    </div>
                </div>
                <div style="flex: 1; padding: 0 10px; border-left: 1px solid #dee2e6; border-right: 1px solid #dee2e6;" class="bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="font-weight-bold"><i class="fas fa-sign-out-alt"></i> 食材出库</p>
                    <small>执行食材出库操作</small>
                </div>
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-boxes"></i> 库存管理</p>
                    <small>查看和管理库存</small>
                    <div class="mt-2">
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往库存管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>执行消耗计划会自动生成出库单，也可以手动创建出库单</li>
                    <li>出库单创建后需要审核，审核通过后才能执行出库操作</li>
                    <li>执行出库操作会更新库存，减少相应食材的库存量</li>
                    <li>出库完成后可以查看库存情况，及时补充库存不足的食材</li>
                    <li>出库记录可用于食材溯源，追踪食材使用情况</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">出库单列表</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_out.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 创建出库单
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('stock_out.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>状态</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部</option>
                                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                                        <option value="已出库" {% if status == '已出库' %}selected{% endif %}>已出库</option>
                                        <option value="已取消" {% if status == '已取消' %}selected{% endif %}>已取消</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>出库类型</label>
                                    <select name="stock_out_type" class="form-control">
                                        <option value="">全部</option>
                                        <option value="消耗出库" {% if stock_out_type == '消耗出库' %}selected{% endif %}>消耗出库</option>
                                        <option value="调拨出库" {% if stock_out_type == '调拨出库' %}selected{% endif %}>调拨出库</option>
                                        <option value="报损出库" {% if stock_out_type == '报损出库' %}selected{% endif %}>报损出库</option>
                                        <option value="退货出库" {% if stock_out_type == '退货出库' %}selected{% endif %}>退货出库</option>
                                        <option value="其他出库" {% if stock_out_type == '其他出库' %}selected{% endif %}>其他出库</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>开始日期</label>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>结束日期</label>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 出库单列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>出库单号</th>
                                    <th>仓库</th>
                                    <th>出库类型</th>
                                    <th>出库日期</th>
                                    <th>操作人</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock_out in stock_outs %}
                                <tr>
                                    <td>{{ stock_out.stock_out_number }}</td>
                                    <td>{{ stock_out.warehouse.name }}</td>
                                    <td>{{ stock_out.stock_out_type }}</td>
                                    <td>{{  stock_out.stock_out_date|format_datetime('%Y-%m-%d')  }}</td>
                                    <td>{{ stock_out.operator.real_name or stock_out.operator.username }}</td>
                                    <td>
                                        {% if stock_out.status == '待审核' %}
                                        <span class="badge badge-warning">待审核</span>
                                        {% elif stock_out.status == '已审核' %}
                                        <span class="badge badge-info">已审核</span>
                                        {% elif stock_out.status == '已出库' %}
                                        <span class="badge badge-success">已出库</span>
                                        {% elif stock_out.status == '已取消' %}
                                        <span class="badge badge-danger">已取消</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('stock_out.view', id=stock_out.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        {% if stock_out.status == '待审核' %}
                                        <a href="{{ url_for('stock_out.edit', id=stock_out.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        {% endif %}
                                        <button type="button" class="btn btn-primary btn-sm" onclick="printStockOut({{ stock_out.id }})">
                                            <i class="fas fa-print"></i> 打印
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无出库单数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('stock_out.index', page=pagination.prev_num, status=status, start_date=start_date, end_date=end_date) if pagination.has_prev else '#' }}">上一页</a>
                            </li>

                            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('stock_out.index', page=page_num, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('stock_out.index', page=pagination.next_num, status=status, start_date=start_date, end_date=end_date) if pagination.has_next else '#' }}">下一页</a>
                            </li>
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    function printStockOut(stockOutId) {
        window.open("{{ url_for('stock_out.print_stock_out', id=0) }}".replace('0', stockOutId), '_blank');
    }
</script>
{% endblock %}
