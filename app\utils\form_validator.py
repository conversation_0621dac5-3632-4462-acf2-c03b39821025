"""
表单验证工具模块

提供用于验证表单数据的工具函数和装饰器。
"""

from flask import request, jsonify, current_app
from functools import wraps
import json

def validate_inspection_form(f):
    """
    检查记录表单验证装饰器
    
    验证检查记录表单的必要字段和字段类型
    
    用法示例:
    @app.route('/api/inspections', methods=['POST'])
    @login_required
    @validate_inspection_form
    def create_inspection():
        # 这里的代码只有当表单验证通过时才会执行
        ...
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查必要字段
        required_fields = ['daily_log_id', 'inspection_type', 'inspection_item']
        
        # 获取请求数据
        if request.is_json:
            data = request.json
        else:
            data = request.form
            
        # 验证必要字段
        missing_fields = [field for field in required_fields if field not in data or not data[field]]
        if missing_fields:
            current_app.logger.warning(f"表单验证失败: 缺少必要字段 {', '.join(missing_fields)}")
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {", ".join(missing_fields)}'
            }), 400
            
        # 验证字段类型
        if 'inspection_type' in data and data['inspection_type'] not in ['morning', 'noon', 'evening']:
            current_app.logger.warning(f"表单验证失败: 检查类型无效 {data['inspection_type']}")
            return jsonify({
                'success': False,
                'message': '检查类型无效，必须是 morning, noon 或 evening'
            }), 400
            
        # 验证状态字段
        if 'status' in data and data['status'] not in ['normal', 'abnormal']:
            current_app.logger.warning(f"表单验证失败: 状态值无效 {data['status']}")
            return jsonify({
                'success': False,
                'message': '状态值无效，必须是 normal 或 abnormal'
            }), 400
            
        return f(*args, **kwargs)
    return decorated_function

def validate_dining_companion_form(f):
    """
    陪餐记录表单验证装饰器
    
    验证陪餐记录表单的必要字段和字段类型
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查必要字段
        required_fields = ['daily_log_id', 'companion_name', 'companion_role', 'meal_type']
        
        # 获取请求数据
        if request.is_json:
            data = request.json
        else:
            data = request.form
            
        # 验证必要字段
        missing_fields = [field for field in required_fields if field not in data or not data[field]]
        if missing_fields:
            current_app.logger.warning(f"表单验证失败: 缺少必要字段 {', '.join(missing_fields)}")
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {", ".join(missing_fields)}'
            }), 400
            
        # 验证餐次类型
        if 'meal_type' in data and data['meal_type'] not in ['breakfast', 'lunch', 'dinner']:
            current_app.logger.warning(f"表单验证失败: 餐次类型无效 {data['meal_type']}")
            return jsonify({
                'success': False,
                'message': '餐次类型无效，必须是 breakfast, lunch 或 dinner'
            }), 400
            
        # 验证评分范围
        for rating_field in ['taste_rating', 'hygiene_rating', 'service_rating']:
            if rating_field in data and data[rating_field]:
                try:
                    rating = int(data[rating_field])
                    if rating < 1 or rating > 5:
                        current_app.logger.warning(f"表单验证失败: 评分范围无效 {rating_field}={rating}")
                        return jsonify({
                            'success': False,
                            'message': f'评分范围无效，{rating_field}必须在1-5之间'
                        }), 400
                except ValueError:
                    current_app.logger.warning(f"表单验证失败: 评分必须是整数 {rating_field}={data[rating_field]}")
                    return jsonify({
                        'success': False,
                        'message': f'评分必须是整数: {rating_field}'
                    }), 400
            
        return f(*args, **kwargs)
    return decorated_function

def validate_training_form(f):
    """
    培训记录表单验证装饰器
    
    验证培训记录表单的必要字段和字段类型
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查必要字段
        required_fields = ['daily_log_id', 'training_topic', 'trainer', 'training_time']
        
        # 获取请求数据
        if request.is_json:
            data = request.json
        else:
            data = request.form
            
        # 验证必要字段
        missing_fields = [field for field in required_fields if field not in data or not data[field]]
        if missing_fields:
            current_app.logger.warning(f"表单验证失败: 缺少必要字段 {', '.join(missing_fields)}")
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {", ".join(missing_fields)}'
            }), 400
            
        # 验证培训时长
        if 'duration' in data and data['duration']:
            try:
                duration = int(data['duration'])
                if duration <= 0:
                    current_app.logger.warning(f"表单验证失败: 培训时长必须大于0 duration={duration}")
                    return jsonify({
                        'success': False,
                        'message': '培训时长必须大于0'
                    }), 400
            except ValueError:
                current_app.logger.warning(f"表单验证失败: 培训时长必须是整数 duration={data['duration']}")
                return jsonify({
                    'success': False,
                    'message': '培训时长必须是整数'
                }), 400
                
        # 验证参训人数
        if 'attendees_count' in data and data['attendees_count']:
            try:
                count = int(data['attendees_count'])
                if count <= 0:
                    current_app.logger.warning(f"表单验证失败: 参训人数必须大于0 attendees_count={count}")
                    return jsonify({
                        'success': False,
                        'message': '参训人数必须大于0'
                    }), 400
            except ValueError:
                current_app.logger.warning(f"表单验证失败: 参训人数必须是整数 attendees_count={data['attendees_count']}")
                return jsonify({
                    'success': False,
                    'message': '参训人数必须是整数'
                }), 400
            
        return f(*args, **kwargs)
    return decorated_function

def validate_issue_form(f):
    """
    问题记录表单验证装饰器
    
    验证问题记录表单的必要字段和字段类型
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查必要字段
        required_fields = ['daily_log_id', 'issue_type', 'description', 'status']
        
        # 获取请求数据
        if request.is_json:
            data = request.json
        else:
            data = request.form
            
        # 验证必要字段
        missing_fields = [field for field in required_fields if field not in data or not data[field]]
        if missing_fields:
            current_app.logger.warning(f"表单验证失败: 缺少必要字段 {', '.join(missing_fields)}")
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {", ".join(missing_fields)}'
            }), 400
            
        # 验证状态字段
        if 'status' in data and data['status'] not in ['pending', 'fixing', 'fixed']:
            current_app.logger.warning(f"表单验证失败: 状态值无效 {data['status']}")
            return jsonify({
                'success': False,
                'message': '状态值无效，必须是 pending, fixing 或 fixed'
            }), 400
            
        return f(*args, **kwargs)
    return decorated_function

def validate_json_data(required_fields=None, field_types=None):
    """
    通用JSON数据验证装饰器
    
    Args:
        required_fields: 必要字段列表
        field_types: 字段类型字典，格式为 {'field_name': (type, [allowed_values])}
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查是否是JSON请求
            if not request.is_json:
                return jsonify({
                    'success': False,
                    'message': '请求必须是JSON格式'
                }), 400
                
            data = request.json
            
            # 验证必要字段
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data or data[field] is None]
                if missing_fields:
                    return jsonify({
                        'success': False,
                        'message': f'缺少必要字段: {", ".join(missing_fields)}'
                    }), 400
            
            # 验证字段类型和允许值
            if field_types:
                for field, (field_type, allowed_values) in field_types.items():
                    if field in data and data[field] is not None:
                        # 检查类型
                        if not isinstance(data[field], field_type):
                            return jsonify({
                                'success': False,
                                'message': f'字段 {field} 类型错误，应为 {field_type.__name__}'
                            }), 400
                        
                        # 检查允许值
                        if allowed_values and data[field] not in allowed_values:
                            return jsonify({
                                'success': False,
                                'message': f'字段 {field} 值无效，允许值: {", ".join(map(str, allowed_values))}'
                            }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
