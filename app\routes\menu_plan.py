from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import MenuPlan, MenuRecipe, Recipe, AdministrativeArea, User, FoodSample, WeeklyMenu, WeeklyMenuRecipe
from app import db, csrf
from datetime import datetime, date, timedelta
import json
import uuid
import os
from functools import lru_cache
from app.utils.permissions import check_permission
from app.utils.menu import get_week_dates
from app.utils.cache import MenuPlanCache
from app.services.menu_sync_service import MenuSyncService

menu_plan_bp = Blueprint('menu_plan', __name__)

# 注意：索引创建功能已移除，因为它可能在某些环境中导致问题
# 如果需要创建索引，请手动在数据库中执行以下SQL语句：
# CREATE INDEX IF NOT EXISTS ix_menu_plans_area_date ON menu_plans(area_id, plan_date);
# CREATE INDEX IF NOT EXISTS ix_menu_recipes_menu_plan_id ON menu_recipes(menu_plan_id);

@menu_plan_bp.route('/menu-plan')
@login_required
@check_permission('menu_plan', 'view')
def index():
    """菜单计划列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    meal_type = request.args.get('meal_type', '')

    # 构建查询
    query = MenuPlan.query.filter(MenuPlan.area_id.in_(area_ids))

    # 应用过滤条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    if status:
        query = query.filter_by(status=status)
    if meal_type:
        query = query.filter_by(meal_type=meal_type)
    if start_date:
        query = query.filter(MenuPlan.plan_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(MenuPlan.plan_date <= datetime.strptime(end_date, '%Y-%m-%d'))

    # 按计划日期降序排序
    query = query.order_by(MenuPlan.plan_date.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    menu_plans = pagination.items

    # 获取区域信息，用于过滤
    areas = current_user.get_accessible_areas()

    return render_template('menu_plan/index.html',
                          menu_plans=menu_plans,
                          pagination=pagination,
                          areas=areas,
                          area_id=area_id,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          meal_type=meal_type)

@menu_plan_bp.route('/menu-plan/create', methods=['GET', 'POST'])
@login_required
@check_permission('menu_plan', 'create')
def create():
    """创建菜单计划"""
    if request.method == 'POST':
        try:
            data = request.form
            menu_plan = MenuPlan(
                area_id=data['area_id'],
                plan_date=data['plan_date'],
                meal_type=data['meal_type'],
                status='计划中',
                created_by=current_user.id
            )
            db.session.add(menu_plan)
            db.session.commit()

            # 处理菜品
            recipe_ids = request.form.getlist('recipe_ids[]')
            quantities = request.form.getlist('quantities[]')

            for recipe_id, quantity in zip(recipe_ids, quantities):
                menu_recipe = MenuRecipe(
                    menu_plan_id=menu_plan.id,
                    recipe_id=recipe_id,
                    planned_quantity=quantity
                )
                db.session.add(menu_recipe)

            db.session.commit()
            flash('菜单计划创建成功', 'success')
            return redirect(url_for('menu_plan.view', id=menu_plan.id))

        except Exception as e:
            db.session.rollback()
            flash(f'创建失败: {str(e)}', 'error')

    # GET请求，显示创建表单
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取所有启用的食谱
    recipes = Recipe.query.filter_by(status=1).all()

    # 获取URL参数，用于从周视图跳转过来时预填充
    plan_date = request.args.get('plan_date', '')
    meal_type = request.args.get('meal_type', '')

    return render_template('menu_plan/create.html',
                          areas=accessible_areas,
                          recipes=recipes,
                          plan_date=plan_date,
                          meal_type=meal_type)

@menu_plan_bp.route('/menu-plan/<int:id>')
@login_required
@check_permission('menu_plan', 'view')
def view(id):
    """查看菜单计划详情"""
    menu_plan = MenuPlan.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限查看该菜单计划', 'danger')
        return redirect(url_for('menu_plan.index'))

    # 获取关联的食谱
    menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=id).all()

    # 获取关联的留样记录
    food_samples = FoodSample.query.filter_by(menu_plan_id=id).all()

    # 获取所有启用的食谱，用于添加菜品
    recipes = Recipe.query.filter_by(status=1).all()

    return render_template('menu_plan/view.html',
                          menu_plan=menu_plan,
                          menu_recipes=menu_recipes,
                          food_samples=food_samples,
                          recipes=recipes)

@menu_plan_bp.route('/menu-plan/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@check_permission('menu_plan', 'edit')
def edit(id):
    """编辑菜单计划"""
    menu_plan = MenuPlan.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限编辑该菜单计划', 'danger')
        return redirect(url_for('menu_plan.index'))

    # 只有计划中状态的菜单可以编辑
    if menu_plan.status != '计划中':
        flash('只有计划中状态的菜单可以编辑', 'danger')
        return redirect(url_for('menu_plan.view', id=id))

    if request.method == 'POST':
        try:
            data = request.form
            menu_plan.area_id = data['area_id']
            menu_plan.plan_date = data['plan_date']
            menu_plan.meal_type = data['meal_type']

            # 更新菜品
            MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).delete()
            recipe_ids = request.form.getlist('recipe_ids[]')
            quantities = request.form.getlist('quantities[]')

            for recipe_id, quantity in zip(recipe_ids, quantities):
                menu_recipe = MenuRecipe(
                    menu_plan_id=menu_plan.id,
                    recipe_id=recipe_id,
                    planned_quantity=quantity
                )
                db.session.add(menu_recipe)

            db.session.commit()
            flash('菜单计划更新成功', 'success')
            return redirect(url_for('menu_plan.view', id=menu_plan.id))

        except Exception as e:
            db.session.rollback()
            flash(f'更新失败: {str(e)}', 'error')

    # GET请求，显示编辑表单
    # 获取当前菜单计划的食谱关联
    menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=id).all()

    # 获取所有启用的食谱
    recipes = Recipe.query.filter_by(status=1).all()

    return render_template('menu_plan/edit.html',
                          menu_plan=menu_plan,
                          menu_recipes=menu_recipes,
                          recipes=recipes)

@menu_plan_bp.route('/menu-plan/<int:id>/approve', methods=['POST'])
@login_required
@check_permission('menu_plan', 'approve')
def approve(id):
    """审核菜单计划"""
    menu_plan = MenuPlan.query.get_or_404(id)

    # 检查用户是否有权限审核
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限审核该菜单计划', 'danger')
        return redirect(url_for('menu_plan.index'))

    # 只有计划中状态的菜单可以审核
    if menu_plan.status != '计划中':
        flash('只有计划中状态的菜单可以审核', 'danger')
        return redirect(url_for('menu_plan.view', id=id))

    # 更新状态
    menu_plan.status = '已发布'
    menu_plan.approved_by = current_user.id

    db.session.commit()

    # 同步菜单数据到工作日志
    try:
        sync_result = MenuSyncService.sync_menu_plan_to_daily_log(id)
        if sync_result['success']:
            current_app.logger.info(f"同步菜单到工作日志成功: {sync_result['message']}")
            flash(f"菜单计划审核通过，并已同步到工作日志: {sync_result['message']}", 'success')
        else:
            current_app.logger.error(f"同步菜单到工作日志失败: {sync_result['message']}")
            flash(f"菜单计划审核通过，但同步到工作日志失败: {sync_result['message']}", 'warning')
    except Exception as e:
        current_app.logger.error(f"同步菜单到工作日志时出错: {str(e)}")
        flash(f"菜单计划审核通过，但同步到工作日志时出错: {str(e)}", 'warning')

    return redirect(url_for('menu_plan.view', id=id))

@menu_plan_bp.route('/menu-plan/<int:id>/cancel', methods=['POST'])
@login_required
@check_permission('menu_plan', 'edit')
def cancel(id):
    """取消菜单计划"""
    menu_plan = MenuPlan.query.get_or_404(id)

    # 检查用户是否有权限取消
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限取消该菜单计划', 'danger')
        return redirect(url_for('menu_plan.index'))

    # 只有计划中或已发布状态的菜单可以取消
    if menu_plan.status not in ['计划中', '已发布']:
        flash('只有计划中或已发布状态的菜单可以取消', 'danger')
        return redirect(url_for('menu_plan.view', id=id))

    # 更新状态
    menu_plan.status = '已取消'

    db.session.commit()
    flash('菜单计划已取消', 'success')
    return redirect(url_for('menu_plan.view', id=id))

@menu_plan_bp.route('/menu-plan/<int:id>/execute', methods=['POST'])
@login_required
@check_permission('menu_plan', 'execute')
def execute(id):
    """执行菜单计划"""
    menu_plan = MenuPlan.query.get_or_404(id)

    # 检查用户是否有权限执行
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限执行该菜单计划', 'danger')
        return redirect(url_for('menu_plan.index'))

    # 只有已发布状态的菜单可以执行
    if menu_plan.status != '已发布':
        flash('只有已发布状态的菜单可以执行', 'danger')
        return redirect(url_for('menu_plan.view', id=id))

    # 更新状态
    menu_plan.status = '已执行'
    menu_plan.actual_diners = request.form.get('actual_diners', type=int)

    # 更新菜单食谱的实际数量
    for menu_recipe in menu_plan.menu_recipes:
        actual_quantity = request.form.get(f'actual_quantity_{menu_recipe.id}', type=float)
        if actual_quantity is not None:
            menu_recipe.actual_quantity = actual_quantity

    db.session.commit()
    flash('菜单计划已执行', 'success')
    return redirect(url_for('menu_plan.view', id=id))

@menu_plan_bp.route('/menu-plan/<int:id>/print')
@login_required
@check_permission('menu_plan', 'view')
def print_menu(id):
    """打印菜单计划"""
    menu_plan = MenuPlan.query.get_or_404(id)

    # 检查用户是否有权限打印
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限打印该菜单计划', 'danger')
        return redirect(url_for('menu_plan.index'))

    # 获取关联的食谱
    menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=id).all()

    # 获取项目名称
    from app.models_system import SystemSetting
    project_name = SystemSetting.get_value('project_name', '初中毕业生学生去向管理系统')

    return render_template('menu_plan/print.html',
                          menu_plan=menu_plan,
                          menu_recipes=menu_recipes,
                          project_name=project_name)

@menu_plan_bp.route('/menu-plan/week', methods=['GET'])
@login_required
@check_permission('menu_plan', 'view')
def week_view():
    """周菜单安排 - 重定向到新的实现方式"""
    # 获取查询参数
    area_id = request.args.get('area_id', type=int) or current_user.area_id
    week_start_str = request.args.get('week_start')

    # 重定向到新的实现方式
    return redirect(url_for('menu_plan.week_view_category', area_id=area_id, week_start=week_start_str))

@menu_plan_bp.route('/menu-plan/week-planner', methods=['GET'])
@login_required
@check_permission('menu_plan', 'view')
def week_planner():
    """周菜单安排工具 - 参考/employee/add的实现方式"""
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')

    # 获取一周的日期
    week_dates = {}
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()

    for i in range(7):
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        weekday = weekday_names[i]

        week_dates[date_str] = {
            'weekday': weekday,
            'date': current_date
        }

    # 获取现有菜单数据
    existing_menu = {}
    if area_id:
        # 查询指定区域和日期范围的菜单计划
        week_end = week_start + timedelta(days=6)
        menu_plans = MenuPlan.query.filter(
            MenuPlan.area_id == area_id,
            MenuPlan.plan_date >= week_start,
            MenuPlan.plan_date <= week_end
        ).all()

        # 组织数据
        for plan in menu_plans:
            date_str = plan.plan_date.strftime('%Y-%m-%d')
            meal_type = plan.meal_type

            if date_str not in existing_menu:
                existing_menu[date_str] = {'早餐': [], '午餐': [], '晚餐': []}

            # 获取关联的菜品
            menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=plan.id).all()
            recipes = []
            for mr in menu_recipes:
                recipe = Recipe.query.get(mr.recipe_id)
                if recipe:
                    recipes.append({
                        'id': recipe.id,
                        'name': recipe.name,
                        'quantity': mr.planned_quantity
                    })

            existing_menu[date_str][meal_type] = recipes

    # 获取所有可用的食谱
    recipes = Recipe.query.filter_by(status=1).all()
    recipe_list = [
        {
            'id': recipe.id,
            'name': recipe.name,
            'category': recipe.category.name if recipe.category else '未分类',
            'cooking_method': recipe.cooking_method
        }
        for recipe in recipes
    ]

    # 获取所有区域
    areas = current_user.get_accessible_areas()

    return render_template('menu_plan/week_planner.html',
                          week_dates=week_dates,
                          existing_menu=existing_menu,
                          recipes=recipe_list,
                          areas=areas,
                          area_id=area_id,
                          week_start=week_start_str)

@menu_plan_bp.route('/menu-plan/week/print')
@login_required
@check_permission('menu_plan', 'view')
def print_week_menu():
    """打印周菜单计划"""
    area_id = request.args.get('area_id', type=int)
    week_start = request.args.get('week_start')

    if not week_start:
        week_start = datetime.now().strftime('%Y-%m-%d')

    week_dates = get_week_dates(week_start)
    week_start_str = week_dates[0].strftime('%Y-%m-%d')
    week_end_str = week_dates[-1].strftime('%Y-%m-%d')

    # 获取周菜单数据
    week_data = {}
    for date in week_dates:
        weekday = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][date.weekday()]
        query = MenuPlan.query.filter_by(plan_date=date)
        if area_id:
            query = query.filter_by(area_id=area_id)
        menu_plans = query.all()
        meals = {
            '早餐': None,
            '午餐': None,
            '晚餐': None
        }
        for plan in menu_plans:
            meals[plan.meal_type] = plan
        week_data[date.strftime('%Y-%m-%d')] = {
            'weekday': weekday,
            'meals': meals
        }

    area = AdministrativeArea.query.get(area_id) if area_id else None
    return render_template('menu_plan/print_week.html',
                         week_data=week_data,
                         area=area,
                         week_start_str=week_start_str,
                         week_end_str=week_end_str)

@menu_plan_bp.route('/menu-plan/<int:menu_plan_id>/add-recipe', methods=['POST'])
@login_required
def add_recipe(menu_plan_id):
    """添加菜品到菜单计划"""
    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        return jsonify({'success': 0, 'message': '您没有权限操作该菜单计划'})

    # 获取表单数据
    recipe_id = request.form.get('recipe_id', type=int)
    planned_quantity = request.form.get('planned_quantity', type=float)
    actual_quantity = request.form.get('actual_quantity', type=float)
    notes = request.form.get('notes')

    # 验证数据
    if not recipe_id or not planned_quantity:
        return jsonify({'success': 0, 'message': '请填写所有必填字段'})

    # 检查食谱是否存在
    recipe = Recipe.query.get(recipe_id)
    if not recipe:
        return jsonify({'success': 0, 'message': '食谱不存在'})

    # 检查是否已经添加过该食谱
    existing_recipe = MenuRecipe.query.filter_by(menu_plan_id=menu_plan_id, recipe_id=recipe_id).first()
    if existing_recipe:
        return jsonify({'success': 0, 'message': '该食谱已经添加过了'})

    # 创建菜单食谱关联
    menu_recipe = MenuRecipe(
        menu_plan_id=menu_plan_id,
        recipe_id=recipe_id,
        planned_quantity=planned_quantity,
        actual_quantity=actual_quantity,
        notes=notes
    )

    db.session.add(menu_recipe)
    db.session.commit()

    return jsonify({'success': 1, 'message': '菜品添加成功'})

@menu_plan_bp.route('/menu-plan/<int:menu_plan_id>/update-recipe', methods=['POST'])
@login_required
def update_recipe(menu_plan_id):
    """更新菜单计划中的菜品"""
    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        return jsonify({'success': 0, 'message': '您没有权限操作该菜单计划'})

    # 获取表单数据
    recipe_id = request.form.get('recipe_id', type=int)
    planned_quantity = request.form.get('planned_quantity', type=float)
    actual_quantity = request.form.get('actual_quantity', type=float)
    notes = request.form.get('notes')

    # 验证数据
    if not recipe_id or not planned_quantity:
        return jsonify({'success': 0, 'message': '请填写所有必填字段'})

    # 查找菜单食谱关联
    menu_recipe = MenuRecipe.query.get(recipe_id)
    if not menu_recipe or menu_recipe.menu_plan_id != menu_plan_id:
        return jsonify({'success': 0, 'message': '菜品不存在'})

    # 更新数据
    menu_recipe.planned_quantity = planned_quantity
    if actual_quantity is not None:
        menu_recipe.actual_quantity = actual_quantity
    menu_recipe.notes = notes

    db.session.commit()

    return jsonify({'success': 1, 'message': '菜品更新成功'})

@menu_plan_bp.route('/menu-plan/<int:menu_plan_id>/delete-recipe', methods=['POST'])
@login_required
def delete_recipe(menu_plan_id):
    """从菜单计划中删除菜品"""
    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        return jsonify({'success': 0, 'message': '您没有权限操作该菜单计划'})

    # 获取表单数据
    recipe_id = request.form.get('recipe_id', type=int)

    # 验证数据
    if not recipe_id:
        return jsonify({'success': 0, 'message': '请提供菜品ID'})

    # 查找菜单食谱关联
    menu_recipe = MenuRecipe.query.get(recipe_id)
    if not menu_recipe or menu_recipe.menu_plan_id != menu_plan_id:
        return jsonify({'success': 0, 'message': '菜品不存在'})

    # 删除菜单食谱关联
    db.session.delete(menu_recipe)
    db.session.commit()

    return jsonify({'success': 1, 'message': '菜品删除成功'})

@menu_plan_bp.route('/menu-plan/<int:menu_plan_id>/update-recipes', methods=['POST'])
@login_required
def update_recipes(menu_plan_id):
    """批量更新菜单计划中的菜品"""
    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        return jsonify({'success': 0, 'message': '您没有权限操作该菜单计划'})

    # 获取JSON数据
    data = request.json
    if not data:
        return jsonify({'success': 0, 'message': '请提供菜品数据'})

    # 更新菜品数据
    for key, recipe_data in data.items():
        recipe_id = recipe_data.get('id')
        planned_quantity = recipe_data.get('planned_quantity')
        actual_quantity = recipe_data.get('actual_quantity')
        notes = recipe_data.get('notes')

        # 查找菜单食谱关联
        menu_recipe = MenuRecipe.query.get(recipe_id)
        if menu_recipe and menu_recipe.menu_plan_id == menu_plan_id:
            # 更新数据
            menu_recipe.planned_quantity = planned_quantity
            if actual_quantity:
                menu_recipe.actual_quantity = actual_quantity
            menu_recipe.notes = notes

    db.session.commit()

    return jsonify({'success': 1, 'message': '菜品批量更新成功'})

@menu_plan_bp.route('/menu-plan/move', methods=['POST'])
@login_required
def move_menu():
    """移动菜单计划"""
    # 从表单或JSON中获取数据
    if request.is_json:
        data = request.get_json()
        menu_id = data.get('menu_id')
        target_date = data.get('target_date')
        target_meal_type = data.get('target_meal_type')
    else:
        menu_id = request.form.get('menu_id', type=int)
        target_date = request.form.get('target_date')
        target_meal_type = request.form.get('target_meal_type')

    # 验证数据
    if not menu_id or not target_date or not target_meal_type:
        return jsonify({'success': 0, 'message': '参数不完整'})

    # 获取菜单计划
    menu_plan = MenuPlan.query.get_or_404(menu_id)

    # 检查权限
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        return jsonify({'success': 0, 'message': '您没有权限操作该菜单计划'})

    # 检查状态
    if menu_plan.status == '已执行':
        return jsonify({'success': 0, 'message': '已执行的菜单不能移动'})

    # 检查目标位置是否已有菜单
    target_date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()
    existing_menu = MenuPlan.query.filter_by(
        area_id=menu_plan.area_id,
        plan_date=target_date_obj,
        meal_type=target_meal_type
    ).first()

    if existing_menu and existing_menu.id != menu_plan.id:
        # 如果目标位置已有菜单且不是自己，则删除目标位置的菜单
        db.session.delete(existing_menu)

    # 更新菜单计划
    menu_plan.plan_date = target_date_obj
    menu_plan.meal_type = target_meal_type

    db.session.commit()

    return jsonify({'success': 1, 'message': '菜单计划移动成功'})

@menu_plan_bp.route('/api-test')
@login_required
def api_test():
    """API接口测试页面"""
    return render_template('menu_plan/api_test.html', now=datetime.now())

@menu_plan_bp.route('/api/menu-plan/test', methods=['POST'])
@login_required
@csrf.exempt
def test_api():
    """测试API端点，用于验证API请求是否能正常工作"""
    try:
        # 记录请求信息
        current_app.logger.info(f'测试API请求内容类型: {request.content_type}')
        current_app.logger.info(f'测试API请求方法: {request.method}')
        current_app.logger.info(f'测试API请求头: {dict(request.headers)}')

        # 尝试解析JSON数据
        try:
            data = request.get_json(force=True)
            current_app.logger.info(f'测试API成功解析JSON数据: {data}')
        except Exception as e:
            current_app.logger.error(f'测试API JSON解析失败: {str(e)}')
            current_app.logger.info(f'测试API请求数据: {request.data}')
            return jsonify({
                'success': False,
                'message': f'请求数据格式错误: {str(e)}'
            })

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '测试API请求成功',
            'received_data': data
        })

    except Exception as e:
        current_app.logger.error(f'测试API异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'测试API异常: {str(e)}'
        })

# 使用lru_cache缓存查询结果
@lru_cache(maxsize=128)
def get_cached_week_menu(area_id, week_start):
    """获取缓存的周菜单数据 - 基于餐次函数和数组的实现"""
    # 获取一周的日期
    week_start_date = datetime.strptime(week_start, '%Y-%m-%d').date()
    week_end_date = week_start_date + timedelta(days=6)
    week_dates = [week_start_date + timedelta(days=i) for i in range(7)]

    # 关键优化：一次性查询所有菜单计划及其关联的菜品
    query = db.session.query(
        MenuPlan, MenuRecipe, Recipe
    ).join(
        MenuRecipe, MenuPlan.id == MenuRecipe.menu_plan_id
    ).join(
        Recipe, MenuRecipe.recipe_id == Recipe.id
    ).filter(
        MenuPlan.plan_date >= week_start_date,
        MenuPlan.plan_date <= week_end_date
    )

    if area_id:
        query = query.filter(MenuPlan.area_id == area_id)

    # 按ID排序，确保菜品顺序一致
    query = query.order_by(MenuPlan.plan_date, MenuPlan.meal_type, MenuRecipe.id)

    results = query.all()

    # 在内存中组织数据 - 直接使用餐次作为键
    week_data = {}
    for date in week_dates:
        date_str = date.strftime('%Y-%m-%d')
        weekday = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][date.weekday()]
        week_data[date_str] = {
            'weekday': weekday,
            '早餐': [],
            '午餐': [],
            '晚餐': []
        }

    # 创建一个字典来跟踪已添加的菜品，避免重复
    added_recipes = {}

    # 填充菜单数据 - 直接将菜品添加到餐次数组
    for plan, menu_recipe, recipe in results:
        date_str = plan.plan_date.strftime('%Y-%m-%d')
        meal_type = plan.meal_type

        # 创建唯一键来检查是否已添加此菜品
        recipe_key = f"{date_str}_{meal_type}_{recipe.id}"

        if recipe_key not in added_recipes:
            recipe_data = {
                'id': recipe.id,
                'name': recipe.name,
                'quantity': menu_recipe.planned_quantity,
                'unit': recipe.unit
            }
            # 直接添加到餐次数组
            week_data[date_str][meal_type].append(recipe_data)
            added_recipes[recipe_key] = True

    return week_data

@menu_plan_bp.route('/api/menu-plan/week/get')
@login_required
@check_permission('menu_plan', 'view')
def get_week_menu():
    """获取周菜单数据 - 基于餐次函数和数组的实现"""
    area_id = request.args.get('area_id', type=int)
    week_start = request.args.get('week_start')

    if not week_start:
        week_start = datetime.now().strftime('%Y-%m-%d')

    # 使用缓存函数获取数据
    week_data = get_cached_week_menu(area_id, week_start)

    return jsonify({
        'success': True,
        'data': {
            'days': week_data
        }
    })

@menu_plan_bp.route('/menu-plan/simple-planner', methods=['GET'])
@login_required
@check_permission('menu_plan', 'view')
def simple_planner():
    """简易周菜单安排 - 使用系统自带的表单处理功能"""
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')

    # 获取一周的日期
    week_dates = {}
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()

    for i in range(7):
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        weekday = weekday_names[i]

        week_dates[date_str] = {
            'weekday': weekday,
            'date': current_date
        }

    # 获取现有菜单数据
    existing_menu = {}
    if area_id:
        # 查询指定区域和日期范围的菜单计划
        week_end = week_start + timedelta(days=6)
        menu_plans = MenuPlan.query.filter(
            MenuPlan.area_id == area_id,
            MenuPlan.plan_date >= week_start,
            MenuPlan.plan_date <= week_end
        ).all()

        # 组织数据
        for plan in menu_plans:
            date_str = plan.plan_date.strftime('%Y-%m-%d')
            meal_type = plan.meal_type

            if date_str not in existing_menu:
                existing_menu[date_str] = {'早餐': [], '午餐': [], '晚餐': []}

            # 获取关联的菜品
            menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=plan.id).all()
            recipes = []
            for mr in menu_recipes:
                recipe = Recipe.query.get(mr.recipe_id)
                if recipe:
                    recipes.append({
                        'id': recipe.id,
                        'name': recipe.name,
                        'quantity': mr.planned_quantity
                    })

            existing_menu[date_str][meal_type] = recipes

    # 获取所有可用的食谱
    recipes = Recipe.query.filter_by(status=1).all()
    recipe_list = [
        {
            'id': recipe.id,
            'name': recipe.name,
            'category': recipe.category.name if recipe.category else '未分类'
        }
        for recipe in recipes
    ]

    # 获取所有区域
    areas = current_user.get_accessible_areas()

    return render_template('menu_plan/simple_planner.html',
                          week_dates=week_dates,
                          existing_menu=existing_menu,
                          recipes=recipe_list,
                          areas=areas,
                          area_id=area_id,
                          week_start=week_start_str)

@menu_plan_bp.route('/menu-plan/save-simple-planner', methods=['POST'])
@login_required
@check_permission('menu_plan', 'edit')
def save_simple_planner():
    """保存简易周菜单数据 - 使用系统自带的表单处理功能"""
    # 获取表单数据
    area_id = request.form.get('area_id', type=int)
    week_start_str = request.form.get('week_start')
    menu_data = request.form.get('menu')

    # 解析菜单数据
    menu_data = {}
    for key, value in request.form.items():
        if key.startswith('menu['):
            # 解析键，格式为menu[日期][餐次][]
            parts = key.replace('menu[', '').replace('][]', '').split('][')
            if len(parts) == 2:
                date_str, meal_type = parts
                if date_str not in menu_data:
                    menu_data[date_str] = {}
                if meal_type not in menu_data[date_str]:
                    menu_data[date_str][meal_type] = []

                # 获取选中的菜品ID列表
                recipe_ids = request.form.getlist(key)
                for recipe_id in recipe_ids:
                    recipe = Recipe.query.get(recipe_id)
                    if recipe:
                        menu_data[date_str][meal_type].append({
                            'id': recipe.id,
                            'name': recipe.name,
                            'quantity': 1  # 默认数量为1
                        })

    try:
        # 计算周开始和结束日期
        week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
        week_end = week_start + timedelta(days=6)

        # 1. 先找出所有要删除的菜单计划ID
        menu_plans = MenuPlan.query.filter(
            MenuPlan.area_id == area_id,
            MenuPlan.plan_date >= week_start,
            MenuPlan.plan_date <= week_end
        ).all()

        menu_plan_ids = [plan.id for plan in menu_plans]

        # 2. 删除所有关联的菜品
        if menu_plan_ids:
            MenuRecipe.query.filter(MenuRecipe.menu_plan_id.in_(menu_plan_ids)).delete(synchronize_session=False)

            # 3. 删除所有菜单计划
            MenuPlan.query.filter(
                MenuPlan.area_id == area_id,
                MenuPlan.plan_date >= week_start,
                MenuPlan.plan_date <= week_end
            ).delete(synchronize_session=False)

            # 提交删除操作
            db.session.commit()

        # 4. 创建新的菜单计划和菜品关联
        new_plans_count = 0
        new_recipes_count = 0

        # 处理每一天的数据
        for date_str, day_data in menu_data.items():
            # 处理一天三餐
            for meal_type, recipes in day_data.items():
                if not recipes:  # 跳过空菜单
                    continue

                # 创建菜单计划 - 直接使用ORM模型
                menu_plan = MenuPlan(
                    area_id=area_id,
                    plan_date=date_str,
                    meal_type=meal_type,
                    status='计划中',
                    created_by=current_user.id
                )
                db.session.add(menu_plan)
                db.session.flush()  # 获取menu_plan.id
                new_plans_count += 1

                # 添加菜品关联
                for recipe in recipes:
                    recipe_id = recipe['id']
                    quantity = recipe.get('quantity', 1)

                    # 创建菜品关联 - 直接使用ORM模型
                    menu_recipe = MenuRecipe(
                        menu_plan_id=menu_plan.id,
                        recipe_id=recipe_id,
                        planned_quantity=quantity
                    )
                    db.session.add(menu_recipe)
                    new_recipes_count += 1

        # 提交创建操作
        db.session.commit()

        flash(f'菜单保存成功！创建了{new_plans_count}个菜单计划和{new_recipes_count}个菜品关联', 'success')
        return redirect(url_for('menu_plan.simple_planner', area_id=area_id, week_start=week_start_str))

    except Exception as e:
        db.session.rollback()
        flash(f'保存失败: {str(e)}', 'danger')
        return redirect(url_for('menu_plan.simple_planner', area_id=area_id, week_start=week_start_str))

@menu_plan_bp.route('/menu-plan/week-form', methods=['GET'])
@login_required
@check_permission('menu_plan', 'view')
def week_view_form():
    """周菜单安排 - 使用系统自带的表单处理功能"""
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')

    # 获取一周的日期
    week_dates = {}
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()

    for i in range(7):
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        weekday = weekday_names[i]

        week_dates[date_str] = {
            'weekday': weekday,
            'date': current_date
        }

    # 获取现有菜单数据
    menuData = {}
    if area_id:
        # 查询指定区域和日期范围的菜单计划
        week_end = week_start + timedelta(days=6)
        menu_plans = MenuPlan.query.filter(
            MenuPlan.area_id == area_id,
            MenuPlan.plan_date >= week_start,
            MenuPlan.plan_date <= week_end
        ).all()

        # 组织数据
        for plan in menu_plans:
            date_str = plan.plan_date.strftime('%Y-%m-%d')
            meal_type = plan.meal_type

            if date_str not in menuData:
                menuData[date_str] = {'早餐': [], '午餐': [], '晚餐': []}

            # 获取关联的菜品
            menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=plan.id).all()
            recipes = []
            for mr in menu_recipes:
                recipe = Recipe.query.get(mr.recipe_id)
                if recipe:
                    recipes.append({
                        'id': recipe.id,
                        'name': recipe.name,
                        'quantity': mr.planned_quantity
                    })

            menuData[date_str][meal_type] = recipes

    # 获取所有可选菜品，按分类分组，优先显示用户自定义食谱
    recipes = Recipe.query.filter_by(status=1).order_by(Recipe.priority.desc(), Recipe.id).all()
    recipes_by_category = {}

    # 首先添加用户自定义食谱分类
    recipes_by_category['用户自定义'] = []

    for recipe in recipes:
        # 如果是用户自定义食谱，添加到用户自定义分类
        if recipe.is_user_defined:
            recipes_by_category['用户自定义'].append(recipe.to_dict())

        # 同时也添加到原始分类
        category = recipe.category or '未分类'
        if category not in recipes_by_category:
            recipes_by_category[category] = []
        recipes_by_category[category].append(recipe.to_dict())

    # 如果没有用户自定义食谱，删除该分类
    if not recipes_by_category['用户自定义']:
        del recipes_by_category['用户自定义']

    # 获取当前用户收藏的食谱ID
    try:
        from app.models_recipe_advanced import UserRecipeFavorite
        favorite_recipe_ids = [f.recipe_id for f in UserRecipeFavorite.query.filter_by(user_id=current_user.id).all()]
    except Exception:
        favorite_recipe_ids = []

    # 获取所有区域
    areas = current_user.get_accessible_areas()

    return render_template('menu_plan/week_view_form.html',
                          week_dates=week_dates,
                          menuData=menuData,
                          recipes_by_category=recipes_by_category,
                          favorite_recipe_ids=favorite_recipe_ids,
                          areas=areas,
                          area_id=area_id,
                          week_start=week_start_str)

@menu_plan_bp.route('/menu-plan/save-week-form', methods=['POST'])
@login_required
@check_permission('menu_plan', 'edit')
def save_week_menu_form():
    """保存周菜单数据 - 使用系统自带的表单处理功能"""
    # 获取表单数据
    area_id = request.form.get('area_id', type=int)
    week_start_str = request.form.get('week_start')

    # 解析菜单数据
    menu_data = {}
    for key, value in request.form.items():
        if key.startswith('menu['):
            # 解析键，格式为menu[日期][餐次][]
            parts = key.replace('menu[', '').replace('][]', '').split('][')
            if len(parts) == 2:
                date_str, meal_type = parts
                if date_str not in menu_data:
                    menu_data[date_str] = {}
                if meal_type not in menu_data[date_str]:
                    menu_data[date_str][meal_type] = []

                # 获取选中的菜品ID列表
                recipe_ids = request.form.getlist(key)
                for recipe_id in recipe_ids:
                    recipe = Recipe.query.get(recipe_id)
                    if recipe:
                        menu_data[date_str][meal_type].append({
                            'id': recipe.id,
                            'name': recipe.name,
                            'quantity': 1  # 默认数量为1
                        })

    try:
        # 计算周开始和结束日期
        week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
        week_end = week_start + timedelta(days=6)

        # 1. 先找出所有要删除的菜单计划ID
        menu_plans = MenuPlan.query.filter(
            MenuPlan.area_id == area_id,
            MenuPlan.plan_date >= week_start,
            MenuPlan.plan_date <= week_end
        ).all()

        menu_plan_ids = [plan.id for plan in menu_plans]

        # 2. 删除所有关联的菜品
        if menu_plan_ids:
            MenuRecipe.query.filter(MenuRecipe.menu_plan_id.in_(menu_plan_ids)).delete(synchronize_session=False)

            # 3. 删除所有菜单计划
            MenuPlan.query.filter(
                MenuPlan.area_id == area_id,
                MenuPlan.plan_date >= week_start,
                MenuPlan.plan_date <= week_end
            ).delete(synchronize_session=False)

            # 提交删除操作
            db.session.commit()

        # 4. 创建新的菜单计划和菜品关联
        new_plans_count = 0
        new_recipes_count = 0

        # 处理每一天的数据
        for date_str, day_data in menu_data.items():
            # 处理一天三餐
            for meal_type, recipes in day_data.items():
                if not recipes:  # 跳过空菜单
                    continue

                # 创建菜单计划 - 直接使用ORM模型
                menu_plan = MenuPlan(
                    area_id=area_id,
                    plan_date=date_str,
                    meal_type=meal_type,
                    status='计划中',
                    created_by=current_user.id
                )
                db.session.add(menu_plan)
                db.session.flush()  # 获取menu_plan.id
                new_plans_count += 1

                # 添加菜品关联
                for recipe in recipes:
                    recipe_id = recipe['id']
                    quantity = recipe.get('quantity', 1)

                    # 创建菜品关联 - 直接使用ORM模型
                    menu_recipe = MenuRecipe(
                        menu_plan_id=menu_plan.id,
                        recipe_id=recipe_id,
                        planned_quantity=quantity
                    )
                    db.session.add(menu_recipe)
                    new_recipes_count += 1

        # 提交创建操作
        db.session.commit()

        flash(f'菜单保存成功！创建了{new_plans_count}个菜单计划和{new_recipes_count}个菜品关联', 'success')
        return redirect(url_for('menu_plan.week_view_form', area_id=area_id, week_start=week_start_str))

    except Exception as e:
        db.session.rollback()
        flash(f'保存失败: {str(e)}', 'danger')
        return redirect(url_for('menu_plan.week_view_form', area_id=area_id, week_start=week_start_str))

@menu_plan_bp.route('/menu-plan/week-category', methods=['GET'])
@login_required
@check_permission('menu_plan', 'view')
def week_view_category():
    """周菜单安排 - 使用分类选择食谱的方式"""
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')

    # 获取一周的日期
    week_dates = {}
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()

    for i in range(7):
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        weekday = weekday_names[i]

        week_dates[date_str] = {
            'weekday': weekday,
            'date': current_date
        }

    # 获取现有菜单数据
    menuData = {}
    if area_id:
        # 查询指定区域和日期范围的菜单计划
        week_end = week_start + timedelta(days=6)
        menu_plans = MenuPlan.query.filter(
            MenuPlan.area_id == area_id,
            MenuPlan.plan_date >= week_start,
            MenuPlan.plan_date <= week_end
        ).all()

        # 组织数据
        for plan in menu_plans:
            date_str = plan.plan_date.strftime('%Y-%m-%d')
            meal_type = plan.meal_type

            if date_str not in menuData:
                menuData[date_str] = {'早餐': [], '午餐': [], '晚餐': []}

            # 获取关联的菜品
            menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=plan.id).all()
            recipes = []
            for mr in menu_recipes:
                recipe = Recipe.query.get(mr.recipe_id)
                if recipe:
                    recipes.append({
                        'id': recipe.id,
                        'name': recipe.name,
                        'quantity': mr.planned_quantity
                    })

            menuData[date_str][meal_type] = recipes

    # 获取所有可选菜品，按分类分组，优先显示用户自定义食谱
    recipes = Recipe.query.filter_by(status=1).order_by(Recipe.priority.desc(), Recipe.id).all()
    recipes_by_category = {}

    # 首先添加用户自定义食谱分类
    recipes_by_category['用户自定义'] = []

    for recipe in recipes:
        # 如果是用户自定义食谱，添加到用户自定义分类
        if recipe.is_user_defined:
            recipes_by_category['用户自定义'].append(recipe.to_dict())

        # 同时也添加到原始分类
        category = recipe.category or '未分类'
        if category not in recipes_by_category:
            recipes_by_category[category] = []
        recipes_by_category[category].append(recipe.to_dict())

    # 如果没有用户自定义食谱，删除该分类
    if not recipes_by_category['用户自定义']:
        del recipes_by_category['用户自定义']

    # 获取当前用户收藏的食谱ID
    try:
        from app.models_recipe_advanced import UserRecipeFavorite
        favorite_recipe_ids = [f.recipe_id for f in UserRecipeFavorite.query.filter_by(user_id=current_user.id).all()]
    except Exception:
        favorite_recipe_ids = []

    # 获取所有区域
    areas = current_user.get_accessible_areas()

    return render_template('menu_plan/week_view_category.html',
                          week_dates=week_dates,
                          menuData=menuData,
                          recipes_by_category=recipes_by_category,
                          favorite_recipe_ids=favorite_recipe_ids,
                          areas=areas,
                          area_id=area_id,
                          week_start=week_start_str)

@menu_plan_bp.route('/menu-plan/save-week-category', methods=['POST'])
@login_required
@check_permission('menu_plan', 'edit')
def save_week_menu_category():
    """保存周菜单数据 - 使用分类选择食谱的方式"""
    # 获取表单数据
    area_id = request.form.get('area_id', type=int)
    week_start_str = request.form.get('week_start')

    # 验证区域ID是否有效
    if not area_id:
        flash('请选择有效的区域', 'danger')
        return redirect(url_for('menu_plan.week_view_category'))

    # 验证用户是否有权限访问该区域
    if not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('menu_plan.week_view_category'))

    # 获取区域信息，确认是否为学校级别
    area = AdministrativeArea.query.get(area_id)
    if not area:
        flash('区域不存在', 'danger')
        return redirect(url_for('menu_plan.week_view_category'))

    # 记录区域信息，用于调试
    current_app.logger.info(f"保存菜单计划 - 区域ID: {area_id}, 区域名称: {area.name}, 区域级别: {area.level}")

    # 解析菜单数据
    menu_data = {}
    for key, value in request.form.items():
        if key.startswith('menu['):
            # 解析键，格式为menu[日期][餐次][]
            parts = key.replace('menu[', '').replace('][]', '').split('][')
            if len(parts) == 2:
                date_str, meal_type = parts
                if date_str not in menu_data:
                    menu_data[date_str] = {}
                if meal_type not in menu_data[date_str]:
                    menu_data[date_str][meal_type] = []

                # 获取选中的菜品ID列表
                recipe_ids = request.form.getlist(key)
                for recipe_id in recipe_ids:
                    recipe = Recipe.query.get(recipe_id)
                    if recipe:
                        menu_data[date_str][meal_type].append({
                            'id': recipe.id,
                            'name': recipe.name,
                            'quantity': 1  # 默认数量为1
                        })

    try:
        # 计算周开始和结束日期
        week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
        week_end = week_start + timedelta(days=6)

        # 记录日期范围，用于调试
        current_app.logger.info(f"菜单计划日期范围: {week_start} 至 {week_end}")

        # 1. 先找出所有要删除的菜单计划ID
        menu_plans = MenuPlan.query.filter(
            MenuPlan.area_id == area_id,
            MenuPlan.plan_date >= week_start,
            MenuPlan.plan_date <= week_end
        ).all()

        menu_plan_ids = [plan.id for plan in menu_plans]

        # 记录要删除的菜单计划，用于调试
        current_app.logger.info(f"找到 {len(menu_plan_ids)} 个要删除的菜单计划")

        # 2. 删除所有关联的菜品
        if menu_plan_ids:
            deleted_recipes = MenuRecipe.query.filter(MenuRecipe.menu_plan_id.in_(menu_plan_ids)).delete(synchronize_session=False)
            current_app.logger.info(f"删除了 {deleted_recipes} 个菜品关联")

            # 3. 删除所有菜单计划
            deleted_plans = MenuPlan.query.filter(
                MenuPlan.area_id == area_id,
                MenuPlan.plan_date >= week_start,
                MenuPlan.plan_date <= week_end
            ).delete(synchronize_session=False)
            current_app.logger.info(f"删除了 {deleted_plans} 个菜单计划")

            # 提交删除操作
            db.session.commit()
            current_app.logger.info("删除操作已提交")

        # 4. 创建新的菜单计划和菜品关联
        new_plans_count = 0
        new_recipes_count = 0

        # 处理每一天的数据
        for date_str, day_data in menu_data.items():
            # 处理一天三餐
            for meal_type, recipes in day_data.items():
                if not recipes:  # 跳过空菜单
                    continue

                # 记录当前处理的日期和餐次，用于调试
                current_app.logger.info(f"处理 {date_str} 的 {meal_type}，包含 {len(recipes)} 个菜品")

                try:
                    # 创建菜单计划 - 直接使用ORM模型，确保datetime精度正确
                    menu_plan = MenuPlan(
                        area_id=area_id,
                        plan_date=date_str,
                        meal_type=meal_type,
                        status='计划中',
                        created_by=current_user.id
                    )
                    db.session.add(menu_plan)
                    db.session.flush()  # 获取menu_plan.id
                    new_plans_count += 1

                    current_app.logger.info(f"创建菜单计划成功，ID: {menu_plan.id}")

                    # 添加菜品关联
                    for recipe in recipes:
                        recipe_id = recipe['id']
                        quantity = recipe.get('quantity', 1)

                        # 创建菜品关联 - 直接使用ORM模型
                        menu_recipe = MenuRecipe(
                            menu_plan_id=menu_plan.id,
                            recipe_id=recipe_id,
                            planned_quantity=quantity
                        )
                        db.session.add(menu_recipe)
                        new_recipes_count += 1

                        current_app.logger.info(f"添加菜品关联成功，菜单ID: {menu_plan.id}，菜品ID: {recipe_id}")
                except Exception as e:
                    current_app.logger.error(f"创建菜单计划或菜品关联失败: {str(e)}")
                    raise

        # 提交创建操作
        db.session.commit()
        current_app.logger.info(f"创建操作已提交，共创建 {new_plans_count} 个菜单计划和 {new_recipes_count} 个菜品关联")

        # 清除缓存
        if hasattr(get_cached_week_menu, 'cache_clear'):
            get_cached_week_menu.cache_clear()
            current_app.logger.info("已清除菜单缓存")

        flash(f'菜单保存成功！创建了{new_plans_count}个菜单计划和{new_recipes_count}个菜品关联', 'success')
        return redirect(url_for('menu_plan.week_view_category', area_id=area_id, week_start=week_start_str))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"保存菜单计划失败: {str(e)}")
        flash(f'保存失败: {str(e)}', 'danger')
        return redirect(url_for('menu_plan.week_view_category', area_id=area_id, week_start=week_start_str))

@menu_plan_bp.route('/api/menu-plan/week/save', methods=['POST'])
@login_required
@check_permission('menu_plan', 'edit')
@csrf.exempt  # 完全豁免CSRF保护
def save_week_menu():
    """保存周菜单数据 - 参考employee/add的实现方式

    注意：MenuPlan模型的datetime字段使用了DATETIME2(precision=1)，
    但我们不需要特殊处理，因为模型定义中已经设置了默认值和onupdate处理函数。
    """
    try:
        # 记录请求开始时间，用于性能分析
        start_time = datetime.now()

        # 记录用户信息
        current_app.logger.info(f"保存菜单计划 - 用户ID: {current_user.id}, 用户名: {current_user.username}")
        current_app.logger.info(f"用户区域ID: {current_user.area_id}")
        current_app.logger.info(f"用户是否有menu_plan.edit权限: {current_user.has_permission('menu_plan', 'edit')}")

        # 记录请求信息
        current_app.logger.info(f"请求方法: {request.method}")
        current_app.logger.info(f"请求内容类型: {request.content_type}")
        current_app.logger.info(f"请求头: {dict(request.headers)}")

        # 获取并验证请求数据
        data = request.get_json()
        if not data:
            current_app.logger.error("请求数据为空")
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            })

        current_app.logger.info(f"请求数据: {data}")

        area_id = data.get('area_id')
        week_start = data.get('week_start')
        menu_data = data.get('menu_data', {})

        # 参数验证
        if not area_id:
            current_app.logger.error("缺少区域ID")
            return jsonify({'success': False, 'message': '缺少区域ID'})
        if not week_start:
            current_app.logger.error("缺少周开始日期")
            return jsonify({'success': False, 'message': '缺少周开始日期'})

        # 如果menu_data为空，初始化为空对象
        if not menu_data:
            current_app.logger.warning("菜单数据为空，将使用空对象")
            menu_data = {}

        current_app.logger.info(f"区域ID: {area_id}, 周开始日期: {week_start}")

        # 检查用户是否有权限访问该区域
        can_access = current_user.can_access_area_by_id(area_id)
        current_app.logger.info(f"用户是否有权限访问区域 {area_id}: {can_access}")
        if not can_access:
            current_app.logger.error(f"用户无权访问区域 {area_id}")
            return jsonify({
                'success': False,
                'message': f'您没有权限访问区域 {area_id}'
            })

        # 检查menu_data结构 - 统一处理两种可能的数据格式
        if 'days' in menu_data and isinstance(menu_data['days'], dict):
            # 旧格式：{days: {日期: {餐次: [菜品]}}}
            days_data = menu_data['days']
            current_app.logger.info("使用嵌套的days格式数据")
        else:
            # 新格式：{日期: {餐次: [菜品]}}
            days_data = menu_data
            current_app.logger.info("使用直接传递的menu_data数据")

        # 确保days_data是字典
        if not isinstance(days_data, dict):
            current_app.logger.warning(f"菜单数据不是字典，将初始化为空字典: {days_data}")
            days_data = {}

        # 获取一周的日期范围
        try:
            week_start_date = datetime.strptime(week_start, '%Y-%m-%d').date()
            week_end_date = week_start_date + timedelta(days=6)
            current_app.logger.info(f"周日期范围: {week_start_date} 至 {week_end_date}")
        except ValueError as e:
            current_app.logger.error(f"日期格式错误: {week_start}, 错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'日期格式错误: {week_start}'
            })

        # 使用简单事务
        try:
            # 1. 先找出所有要删除的菜单计划ID
            menu_plans = MenuPlan.query.filter(
                MenuPlan.area_id == area_id,
                MenuPlan.plan_date >= week_start_date,
                MenuPlan.plan_date <= week_end_date
            ).all()

            menu_plan_ids = [plan.id for plan in menu_plans]
            current_app.logger.info(f"找到 {len(menu_plan_ids)} 个要删除的菜单计划")

            # 记录要删除的菜单计划详情
            for plan in menu_plans:
                current_app.logger.info(f"要删除的菜单计划: ID={plan.id}, 日期={plan.plan_date}, 餐次={plan.meal_type}")

            # 2. 删除所有关联的菜品
            if menu_plan_ids:
                try:
                    deleted_recipes_count = MenuRecipe.query.filter(MenuRecipe.menu_plan_id.in_(menu_plan_ids)).delete(synchronize_session=False)
                    current_app.logger.info(f"删除了 {deleted_recipes_count} 个菜品关联")

                    # 3. 删除所有菜单计划
                    deleted_plans_count = MenuPlan.query.filter(
                        MenuPlan.area_id == area_id,
                        MenuPlan.plan_date >= week_start_date,
                        MenuPlan.plan_date <= week_end_date
                    ).delete(synchronize_session=False)
                    current_app.logger.info(f"删除了 {deleted_plans_count} 个菜单计划")

                    # 提交删除操作
                    db.session.commit()
                    current_app.logger.info("删除操作已提交")
                except Exception as e:
                    db.session.rollback()
                    current_app.logger.error(f"删除操作失败: {str(e)}")
                    raise

            # 4. 创建新的菜单计划和菜品关联 - 参考employee/add的实现方式
            new_plans_count = 0
            new_recipes_count = 0

            current_app.logger.info(f"开始创建新的菜单计划，days_data包含 {len(days_data)} 天的数据")

            # 处理每一天的数据
            for date_str, day_data in days_data.items():
                # 验证日期格式
                try:
                    plan_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    current_app.logger.info(f"处理日期: {date_str}, 转换后: {plan_date}")
                except ValueError as e:
                    current_app.logger.error(f"日期格式错误: {date_str}, 错误: {str(e)}")
                    continue

                # 确保day_data是字典
                if not isinstance(day_data, dict):
                    current_app.logger.error(f"日期 {date_str} 的数据不是字典: {type(day_data)}")
                    continue

                current_app.logger.info(f"日期 {date_str} 的数据: {day_data}")

                # 处理一天三餐
                for meal_type in ['早餐', '午餐', '晚餐']:
                    recipes = day_data.get(meal_type, [])

                    current_app.logger.info(f"处理餐次: {meal_type}, 菜品数量: {len(recipes) if isinstance(recipes, list) else '非列表'}")

                    # 确保recipes是列表
                    if not isinstance(recipes, list) or not recipes:
                        current_app.logger.info(f"跳过空菜单或非列表: {meal_type}, 数据: {recipes}")
                        continue

                    # 创建菜单计划 - 使用原始SQL语句，避免ORM的datetime处理问题
                    try:
                        # 打印调试信息
                        current_app.logger.info(f"创建菜单计划: 区域={area_id}, 日期={date_str}, 餐次={meal_type}")

                        # 使用原始SQL语句创建菜单计划
                        from sqlalchemy import text
                        sql = text("""
                        INSERT INTO menu_plans (area_id, plan_date, meal_type, status, created_by, created_at, updated_at)
                        OUTPUT inserted.id
                        VALUES (:area_id, :plan_date, :meal_type, :status, :created_by, GETDATE(), GETDATE())
                        """)

                        result = db.session.execute(
                            sql,
                            {
                                'area_id': area_id,
                                'plan_date': date_str,
                                'meal_type': meal_type,
                                'status': '计划中',
                                'created_by': current_user.id
                            }
                        )

                        # 获取新插入记录的ID
                        menu_plan_id = result.scalar()
                        new_plans_count += 1

                        current_app.logger.info(f"菜单计划创建成功，ID={menu_plan_id}")

                        # 添加菜品关联
                        for recipe in recipes:
                            # 验证recipe数据
                            if not isinstance(recipe, dict) or 'id' not in recipe:
                                current_app.logger.warning(f"跳过无效菜品数据: {recipe}")
                                continue

                            try:
                                recipe_id = int(recipe['id'])
                                quantity = float(recipe.get('quantity', 1))

                                # 验证食谱是否存在
                                recipe_obj = Recipe.query.get(recipe_id)
                                if not recipe_obj:
                                    current_app.logger.warning(f"食谱不存在，ID={recipe_id}")
                                    continue

                                # 创建菜品关联 - 使用原始SQL语句
                                from sqlalchemy import text
                                sql = text("""
                                INSERT INTO menu_recipes (menu_plan_id, recipe_id, planned_quantity, created_at, updated_at)
                                VALUES (:menu_plan_id, :recipe_id, :planned_quantity, GETDATE(), GETDATE())
                                """)

                                db.session.execute(
                                    sql,
                                    {
                                        'menu_plan_id': menu_plan_id,
                                        'recipe_id': recipe_id,
                                        'planned_quantity': quantity
                                    }
                                )
                                new_recipes_count += 1

                                current_app.logger.info(f"添加菜品: ID={recipe_id}, 名称={recipe_obj.name}, 数量={quantity}")
                            except (ValueError, TypeError) as e:
                                current_app.logger.warning(f"处理菜品数据出错: {str(e)}, 数据: {recipe}")
                                continue
                    except Exception as e:
                        current_app.logger.error(f"创建菜单计划出错: {str(e)}")
                        raise



            # 同步数据到 WeeklyMenu 和 WeeklyMenuRecipe 表
            try:
                # 检查是否已存在该周的周菜单计划
                weekly_menu = WeeklyMenu.query.filter_by(
                    area_id=area_id,
                    week_start=week_start_date
                ).first()

                # 如果存在，删除现有的菜单食谱
                if weekly_menu:
                    deleted_count = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=weekly_menu.id).delete()
                    current_app.logger.info(f"删除现有周菜单食谱: count={deleted_count}")
                else:
                    # 如果不存在，创建新的周菜单计划 - 使用原始SQL语句
                    from sqlalchemy import text
                    sql = text("""
                    INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by, created_at, updated_at)
                    OUTPUT inserted.id
                    VALUES (:area_id, :week_start, :week_end, :status, :created_by, GETDATE(), GETDATE())
                    """)

                    result = db.session.execute(
                        sql,
                        {
                            'area_id': area_id,
                            'week_start': week_start_date,
                            'week_end': week_end_date,
                            'status': '计划中',
                            'created_by': current_user.id
                        }
                    )

                    # 获取新插入记录的ID
                    weekly_menu_id = result.scalar()
                    current_app.logger.info(f"创建新的周菜单计划: id={weekly_menu_id}")

                # 将菜单数据转换为 WeeklyMenuRecipe 格式并保存
                weekly_recipes_count = 0
                for date_str, day_data in days_data.items():
                    # 计算星期几（1-7表示周一到周日）
                    try:
                        day_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                        day_of_week = day_date.weekday() + 1  # 0-6 转为 1-7

                        for meal_type, recipes in day_data.items():
                            if not isinstance(recipes, list):
                                continue

                            for recipe in recipes:
                                if not isinstance(recipe, dict) or 'id' not in recipe:
                                    continue

                                recipe_id = recipe.get('id')
                                recipe_name = recipe.get('name')

                                # 创建菜单食谱关联 - 使用原始SQL语句
                                from sqlalchemy import text
                                sql = text("""
                                INSERT INTO weekly_menu_recipes (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name, created_at, updated_at)
                                VALUES (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name, GETDATE(), GETDATE())
                                """)

                                db.session.execute(
                                    sql,
                                    {
                                        'weekly_menu_id': weekly_menu_id if 'weekly_menu_id' in locals() else weekly_menu.id,
                                        'day_of_week': day_of_week,
                                        'meal_type': meal_type,
                                        'recipe_id': int(recipe_id) if recipe_id and (isinstance(recipe_id, int) or (isinstance(recipe_id, str) and recipe_id.isdigit())) else None,
                                        'recipe_name': recipe_name
                                    }
                                )
                                weekly_recipes_count += 1
                    except Exception as e:
                        current_app.logger.error(f"同步周菜单数据出错: {str(e)}, date_str={date_str}")
                        continue

                current_app.logger.info(f"同步到周菜单完成: weekly_menu_id={weekly_menu_id if 'weekly_menu_id' in locals() else weekly_menu.id}, recipes_count={weekly_recipes_count}")
            except Exception as e:
                current_app.logger.error(f"同步到周菜单失败: {str(e)}")
                # 不要因为同步失败而影响主要功能，所以这里不抛出异常

            # 提交创建操作
            db.session.commit()

            # 清除缓存
            if hasattr(get_cached_week_menu, 'cache_clear'):
                get_cached_week_menu.cache_clear()

            # 记录总耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            return jsonify({
                'success': True,
                'message': '保存成功',
                'stats': {
                    'deleted_plans': len(menu_plan_ids),
                    'new_plans': new_plans_count,
                    'new_recipes': new_recipes_count,
                    'duration': duration
                }
            })

        except Exception as e:
            db.session.rollback()
            # 记录详细错误信息
            current_app.logger.error(f'菜单计划保存失败 (内部事务): {str(e)}')
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            })

    except Exception as e:
        db.session.rollback()
        # 记录详细错误信息
        current_app.logger.error(f'菜单计划保存失败 (外部): {str(e)}')
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}'
        })


