"""
数据修复模块

提供数据修复功能，包括检查和修复数据库中的错误数据。
"""

from flask import Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.utils.permissions import check_permission
from sqlalchemy import text
from datetime import datetime
import json
import traceback

data_repair_bp = Blueprint('data_repair', __name__)

@data_repair_bp.route('/data-repair')
@login_required
@check_permission('数据修复', 'view')
def index():
    """数据修复首页"""
    return render_template('data_repair/index.html',
                          title='数据修复')

@data_repair_bp.route('/data-repair/check-missing-data')
@login_required
@check_permission('数据修复', 'check')
def check_missing_data():
    """检查缺失数据"""
    try:
        # 检查周菜单中缺失的食谱
        missing_recipes = check_missing_recipes()
        
        # 检查食谱中缺失的食材
        missing_ingredients = check_missing_ingredients()
        
        # 检查采购订单中缺失的供应商
        missing_suppliers = check_missing_suppliers()
        
        return jsonify({
            'success': True,
            'data': {
                'missing_recipes': missing_recipes,
                'missing_ingredients': missing_ingredients,
                'missing_suppliers': missing_suppliers
            }
        })
    except Exception as e:
        current_app.logger.error(f"检查缺失数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'检查失败: {str(e)}'
        })

@data_repair_bp.route('/data-repair/fix-missing-data', methods=['POST'])
@login_required
@check_permission('数据修复', 'repair')
def fix_missing_data():
    """修复缺失数据"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            })
        
        # 修复周菜单中缺失的食谱
        if 'fix_recipes' in data and data['fix_recipes']:
            fix_missing_recipes()
        
        # 修复食谱中缺失的食材
        if 'fix_ingredients' in data and data['fix_ingredients']:
            fix_missing_ingredients()
        
        # 修复采购订单中缺失的供应商
        if 'fix_suppliers' in data and data['fix_suppliers']:
            fix_missing_suppliers()
        
        return jsonify({
            'success': True,
            'message': '修复成功'
        })
    except Exception as e:
        current_app.logger.error(f"修复缺失数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'修复失败: {str(e)}'
        })

@data_repair_bp.route('/data-repair/tools')
@login_required
@check_permission('数据修复', 'tool')
def repair_tools():
    """数据修复工具"""
    return render_template('data_repair/tools.html',
                          title='数据修复工具')

@data_repair_bp.route('/data-repair/run-tool', methods=['POST'])
@login_required
@check_permission('数据修复', 'tool')
def run_tool():
    """运行数据修复工具"""
    try:
        data = request.get_json()
        if not data or 'tool' not in data:
            return jsonify({
                'success': False,
                'message': '请求数据不完整'
            })
        
        tool = data['tool']
        params = data.get('params', {})
        
        # 根据工具类型执行不同的修复操作
        if tool == 'fix_weekly_menu':
            result = fix_weekly_menu(params)
        elif tool == 'fix_recipe_ingredients':
            result = fix_recipe_ingredients(params)
        elif tool == 'fix_purchase_orders':
            result = fix_purchase_orders(params)
        else:
            return jsonify({
                'success': False,
                'message': f'未知的工具: {tool}'
            })
        
        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        current_app.logger.error(f"运行数据修复工具失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'运行失败: {str(e)}'
        })

# 辅助函数
def check_missing_recipes():
    """检查周菜单中缺失的食谱"""
    # 使用原始SQL查询，避免ORM的复杂性
    sql = text("""
    SELECT wmr.id, wmr.weekly_menu_id, wmr.recipe_id, wmr.recipe_name, wm.week_start
    FROM weekly_menu_recipes wmr
    LEFT JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
    LEFT JOIN recipes r ON wmr.recipe_id = r.id
    WHERE wmr.recipe_id IS NOT NULL AND r.id IS NULL
    """)
    
    result = db.session.execute(sql)
    missing = []
    for row in result:
        missing.append({
            'id': row.id,
            'weekly_menu_id': row.weekly_menu_id,
            'recipe_id': row.recipe_id,
            'recipe_name': row.recipe_name,
            'week_start': row.week_start.strftime('%Y-%m-%d') if row.week_start else None
        })
    
    return missing

def check_missing_ingredients():
    """检查食谱中缺失的食材"""
    sql = text("""
    SELECT ri.id, ri.recipe_id, r.name as recipe_name, ri.ingredient_id
    FROM recipe_ingredients ri
    LEFT JOIN recipes r ON ri.recipe_id = r.id
    LEFT JOIN ingredients i ON ri.ingredient_id = i.id
    WHERE i.id IS NULL
    """)
    
    result = db.session.execute(sql)
    missing = []
    for row in result:
        missing.append({
            'id': row.id,
            'recipe_id': row.recipe_id,
            'recipe_name': row.recipe_name,
            'ingredient_id': row.ingredient_id
        })
    
    return missing

def check_missing_suppliers():
    """检查采购订单中缺失的供应商"""
    sql = text("""
    SELECT po.id, po.order_number, po.supplier_id
    FROM purchase_orders po
    LEFT JOIN suppliers s ON po.supplier_id = s.id
    WHERE po.supplier_id IS NOT NULL AND s.id IS NULL
    """)
    
    result = db.session.execute(sql)
    missing = []
    for row in result:
        missing.append({
            'id': row.id,
            'order_number': row.order_number,
            'supplier_id': row.supplier_id
        })
    
    return missing

def fix_missing_recipes():
    """修复周菜单中缺失的食谱"""
    # 将缺失的食谱ID设为NULL，保留食谱名称
    sql = text("""
    UPDATE weekly_menu_recipes
    SET recipe_id = NULL
    FROM weekly_menu_recipes wmr
    LEFT JOIN recipes r ON wmr.recipe_id = r.id
    WHERE wmr.recipe_id IS NOT NULL AND r.id IS NULL
    """)
    
    result = db.session.execute(sql)
    db.session.commit()
    return {'updated_count': result.rowcount}

def fix_missing_ingredients():
    """修复食谱中缺失的食材"""
    # 删除缺失的食材关联
    sql = text("""
    DELETE FROM recipe_ingredients
    WHERE id IN (
        SELECT ri.id
        FROM recipe_ingredients ri
        LEFT JOIN ingredients i ON ri.ingredient_id = i.id
        WHERE i.id IS NULL
    )
    """)
    
    result = db.session.execute(sql)
    db.session.commit()
    return {'deleted_count': result.rowcount}

def fix_missing_suppliers():
    """修复采购订单中缺失的供应商"""
    # 将缺失的供应商ID设为NULL
    sql = text("""
    UPDATE purchase_orders
    SET supplier_id = NULL
    FROM purchase_orders po
    LEFT JOIN suppliers s ON po.supplier_id = s.id
    WHERE po.supplier_id IS NOT NULL AND s.id IS NULL
    """)
    
    result = db.session.execute(sql)
    db.session.commit()
    return {'updated_count': result.rowcount}

def fix_weekly_menu(params):
    """修复周菜单数据"""
    # 根据参数执行不同的修复操作
    operation = params.get('operation')
    
    if operation == 'clean_orphaned':
        # 清理孤立的周菜单食谱记录
        sql = text("""
        DELETE FROM weekly_menu_recipes
        WHERE weekly_menu_id NOT IN (SELECT id FROM weekly_menus)
        """)
        
        result = db.session.execute(sql)
        db.session.commit()
        return {'deleted_count': result.rowcount}
    
    elif operation == 'sync_names':
        # 同步食谱名称
        sql = text("""
        UPDATE weekly_menu_recipes
        SET recipe_name = r.name
        FROM weekly_menu_recipes wmr
        JOIN recipes r ON wmr.recipe_id = r.id
        WHERE wmr.recipe_name != r.name
        """)
        
        result = db.session.execute(sql)
        db.session.commit()
        return {'updated_count': result.rowcount}
    
    return {'message': '未执行任何操作'}

def fix_recipe_ingredients(params):
    """修复食谱食材数据"""
    # 根据参数执行不同的修复操作
    operation = params.get('operation')
    
    if operation == 'clean_orphaned':
        # 清理孤立的食谱食材记录
        sql = text("""
        DELETE FROM recipe_ingredients
        WHERE recipe_id NOT IN (SELECT id FROM recipes)
        """)
        
        result = db.session.execute(sql)
        db.session.commit()
        return {'deleted_count': result.rowcount}
    
    return {'message': '未执行任何操作'}

def fix_purchase_orders(params):
    """修复采购订单数据"""
    # 根据参数执行不同的修复操作
    operation = params.get('operation')
    
    if operation == 'clean_orphaned_items':
        # 清理孤立的采购订单项
        sql = text("""
        DELETE FROM purchase_order_items
        WHERE order_id NOT IN (SELECT id FROM purchase_orders)
        """)
        
        result = db.session.execute(sql)
        db.session.commit()
        return {'deleted_count': result.rowcount}
    
    return {'message': '未执行任何操作'}
