from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import StockOut, StockOutItem, Warehouse, Inventory, ConsumptionPlan, MenuPlan, Ingredient, StorageLocation
from app import db
from datetime import datetime, date
import json
from sqlalchemy import text

stock_out_bp = Blueprint('stock_out', __name__)

@stock_out_bp.route('/stock-out')
@login_required
def index():
    """出库单列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    stock_out_type = request.args.get('stock_out_type', '')

    # 构建查询
    query = StockOut.query.join(Warehouse).filter(Warehouse.area_id.in_(area_ids))

    # 应用过滤条件
    if status:
        query = query.filter(StockOut.status == status)
    if start_date:
        query = query.filter(StockOut.stock_out_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(StockOut.stock_out_date <= datetime.strptime(end_date + ' 23:59:59', "%Y-%m-%d %H:%M"))
    if stock_out_type:
        query = query.filter(StockOut.stock_out_type == stock_out_type)

    # 按创建时间降序排序
    query = query.order_by(StockOut.created_at.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    stock_outs = pagination.items

    return render_template('stock_out/index.html',
                          stock_outs=stock_outs,
                          pagination=pagination,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          stock_out_type=stock_out_type)

@stock_out_bp.route('/stock-out/<int:id>')
@login_required
def view(id):
    """查看出库单详情"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限查看该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 获取关联的消耗计划
    consumption_plan = None
    menu_plan = None
    recipes = []
    if stock_out.consumption_plan_id:
        consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id)
        if consumption_plan:
            menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
            if menu_plan and hasattr(menu_plan, 'recipes'):
                recipes = menu_plan.recipes

    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    # 导入溯源相关模型
    from app.models import StockInItem, StockIn, StockInDocument
    from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow

    # 获取溯源信息
    batch_traceability = {}
    for item in stock_out_items:
        batch_number = item.batch_number
        if batch_number not in batch_traceability:
            batch_info = {
                'batch_number': batch_number,
                'ingredient': item.ingredient,
                'stock_in_items': [],
                'stock_ins': [],
                'certificates': [],
                'material_batch': None,
                'trace_documents': [],
                'batch_flows': [],
                'supplier': None
            }

            try:
                # 获取入库明细
                stock_in_items = StockInItem.query.filter_by(batch_number=batch_number).all()
                batch_info['stock_in_items'] = stock_in_items

                # 获取入库单
                stock_ins = []
                for stock_in_item in stock_in_items:
                    if stock_in_item.stock_in and stock_in_item.stock_in not in stock_ins:
                        stock_ins.append(stock_in_item.stock_in)
                        # 获取供应商信息
                        if stock_in_item.stock_in.supplier and not batch_info['supplier']:
                            batch_info['supplier'] = stock_in_item.stock_in.supplier
                batch_info['stock_ins'] = stock_ins

                # 获取检验检疫证明
                certificates = []
                for stock_in in stock_ins:
                    if hasattr(stock_in, 'documents') and stock_in.documents:
                        for doc in stock_in.documents:
                            if doc not in certificates:
                                certificates.append(doc)
                batch_info['certificates'] = certificates

                # 获取溯源批次信息
                material_batch = MaterialBatch.query.filter_by(batch_number=batch_number).first()
                if material_batch:
                    batch_info['material_batch'] = material_batch

                    # 优先使用溯源批次中的供应商信息
                    if material_batch.supplier:
                        batch_info['supplier'] = material_batch.supplier

                    # 获取溯源文档
                    trace_documents = TraceDocument.query.filter_by(batch_id=material_batch.id).all()
                    batch_info['trace_documents'] = trace_documents

                    # 获取批次流水
                    batch_flows = BatchFlow.query.filter_by(batch_id=material_batch.id).all()
                    batch_info['batch_flows'] = batch_flows
            except Exception as e:
                current_app.logger.error(f"获取溯源信息失败: {str(e)}")

            batch_traceability[batch_number] = batch_info

    return render_template('stock_out/view.html',
                          stock_out=stock_out,
                          stock_out_items=stock_out_items,
                          consumption_plan=consumption_plan,
                          menu_plan=menu_plan,
                          recipes=recipes,
                          batch_traceability=batch_traceability)

@stock_out_bp.route('/stock-out/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建出库单"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    if request.method == 'POST':
        # 获取表单数据
        warehouse_id = request.form.get('warehouse_id', type=int)
        stock_out_date = request.form.get('stock_out_date')
        stock_out_type = request.form.get('stock_out_type')
        recipient = request.form.get('recipient')
        department = request.form.get('department')
        notes = request.form.get('notes')

        # 验证数据
        if not warehouse_id or not stock_out_date or not stock_out_type:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('stock_out.create'))

        # 检查用户是否有权限操作该仓库
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        if not current_user.can_access_area_by_id(warehouse.area_id):
            flash('您没有权限操作该仓库', 'danger')
            return redirect(url_for('stock_out.index'))

        # 生成出库单号
        stock_out_number = f"CK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        try:
            # 使用原始SQL语句创建出库单，避免ORM处理datetime字段
            sql = text("""
            INSERT INTO stock_outs (
                stock_out_number, warehouse_id, stock_out_date, stock_out_type,
                recipient, department, operator_id, status, notes
            )
            OUTPUT inserted.id
            VALUES (
                :stock_out_number, :warehouse_id, :stock_out_date, :stock_out_type,
                :recipient, :department, :operator_id, :status, :notes
            )
            """).bindparams(
                stock_out_number=stock_out_number,
                warehouse_id=warehouse_id,
                stock_out_date=datetime.strptime(stock_out_date, '%Y-%m-%d'),
                stock_out_type=stock_out_type,
                recipient=recipient,
                department=department,
                operator_id=current_user.id,
                status='待审核',
                notes=notes
            )

            # 执行SQL语句
            result = db.session.execute(sql)

            # 获取新创建的出库单ID
            stock_out_id = result.fetchone()[0]

            # 提交事务
            db.session.commit()

            flash('出库单创建成功，请添加出库明细', 'success')
            return redirect(url_for('stock_out.edit', id=stock_out_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建出库单时出错: {str(e)}")
            flash(f'创建出库单时出错: {str(e)}', 'danger')
            return redirect(url_for('stock_out.create'))

    # GET请求，显示创建表单
    # 获取仓库列表
    warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids), Warehouse.status == '正常').all()

    return render_template('stock_out/form.html',
                          stock_out=None,
                          warehouses=warehouses,
                          title='创建出库单')

@stock_out_bp.route('/stock-out/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限编辑该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以编辑
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以编辑', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    if request.method == 'POST':
        # 获取表单数据
        stock_out_date = request.form.get('stock_out_date')
        stock_out_type = request.form.get('stock_out_type')
        recipient = request.form.get('recipient')
        department = request.form.get('department')
        notes = request.form.get('notes')

        # 验证数据
        if not stock_out_date or not stock_out_type:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('stock_out.edit', id=id))

        try:
            # 使用原始SQL语句更新出库单信息，避免ORM处理datetime字段
            sql = text("""
            UPDATE stock_outs
            SET stock_out_date = :stock_out_date,
                stock_out_type = :stock_out_type,
                recipient = :recipient,
                department = :department,
                notes = :notes
            WHERE id = :id
            """).bindparams(
                stock_out_date=datetime.strptime(stock_out_date, '%Y-%m-%d'),
                stock_out_type=stock_out_type,
                recipient=recipient,
                department=department,
                notes=notes,
                id=id
            )

            # 执行SQL语句
            db.session.execute(sql)

            # 提交事务
            db.session.commit()

            flash('出库单信息更新成功', 'success')
            return redirect(url_for('stock_out.view', id=id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新出库单时出错: {str(e)}")
            flash(f'更新出库单时出错: {str(e)}', 'danger')
            return redirect(url_for('stock_out.edit', id=id))

    # GET请求，显示编辑表单
    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    # 获取食材列表
    ingredients = Ingredient.query.all()

    # 获取库存列表（按食材分组）
    inventory_by_ingredient = {}
    for ingredient in ingredients:
        # 查询库存，确保只返回状态为"正常"且数量大于0的记录
        inventory_items = Inventory.query.filter(
            Inventory.warehouse_id == stock_out.warehouse_id,
            Inventory.ingredient_id == ingredient.id,
            Inventory.status == '正常',
            Inventory.quantity > 0
        ).order_by(Inventory.expiry_date).all()

        # 记录查询结果
        if inventory_items:
            current_app.logger.info(f"食材 {ingredient.name} 找到 {len(inventory_items)} 条库存记录")

        if inventory_items:
            inventory_by_ingredient[ingredient.id] = inventory_items

    return render_template('stock_out/edit.html',
                          stock_out=stock_out,
                          stock_out_items=stock_out_items,
                          ingredients=ingredients,
                          inventory_by_ingredient=inventory_by_ingredient)

@stock_out_bp.route('/stock-out/<int:id>/add-item', methods=['POST'])
@login_required
def add_item(id):
    """添加出库明细"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限编辑该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以添加明细
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以添加明细', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 获取表单数据
    inventory_id = request.form.get('inventory_id', type=int)
    quantity = request.form.get('quantity', type=float)
    notes = request.form.get('notes')

    # 验证数据
    if not inventory_id or not quantity or quantity <= 0:
        flash('请填写所有必填字段，且数量必须大于0', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 获取库存信息
    inventory = Inventory.query.get_or_404(inventory_id)

    # 检查库存是否属于该仓库
    if inventory.warehouse_id != stock_out.warehouse_id:
        flash('所选库存不属于该出库单的仓库', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 检查库存是否足够
    if inventory.quantity < quantity:
        flash(f'库存不足，当前库存: {inventory.quantity} {inventory.unit}', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 创建出库明细
    stock_out_item = StockOutItem(
        stock_out_id=id,
        inventory_id=inventory_id,
        ingredient_id=inventory.ingredient_id,
        batch_number=inventory.batch_number,
        quantity=quantity,
        unit=inventory.unit,
        notes=notes
    )

    db.session.add(stock_out_item)
    db.session.commit()

    flash('出库明细添加成功', 'success')
    return redirect(url_for('stock_out.edit', id=id))

@stock_out_bp.route('/stock-out/<int:id>/remove-item/<int:item_id>', methods=['POST'])
@login_required
def remove_item(id, item_id):
    """删除出库明细"""
    stock_out = StockOut.query.get_or_404(id)
    stock_out_item = StockOutItem.query.get_or_404(item_id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限编辑该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以删除明细
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以删除明细', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 检查明细是否属于该出库单
    if stock_out_item.stock_out_id != id:
        flash('该明细不属于当前出库单', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 删除明细
    db.session.delete(stock_out_item)
    db.session.commit()

    flash('出库明细删除成功', 'success')
    return redirect(url_for('stock_out.edit', id=id))

@stock_out_bp.route('/stock-out/<int:id>/approve', methods=['POST'])
@login_required
def approve(id):
    """审核出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限审核
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限审核该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以审核
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以审核', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 检查是否有出库明细
    if StockOutItem.query.filter_by(stock_out_id=id).count() == 0:
        flash('出库单没有明细，无法审核', 'warning')
        return redirect(url_for('stock_out.edit', id=id))

    try:
        # 使用原始SQL语句更新出库单状态，避免ORM处理datetime字段
        sql = text("""
        UPDATE stock_outs
        SET status = '已审核',
            approver_id = :approver_id
        WHERE id = :id
        """).bindparams(
            approver_id=current_user.id,
            id=id
        )

        # 执行SQL语句
        db.session.execute(sql)

        # 提交事务
        db.session.commit()

        flash('出库单审核成功', 'success')
        return redirect(url_for('stock_out.view', id=id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"审核出库单时出错: {str(e)}")
        flash(f'审核出库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_out.view', id=id))

@stock_out_bp.route('/stock-out/<int:id>/execute', methods=['POST'])
@login_required
def execute(id):
    """执行出库单（更新库存）"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限执行
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限执行该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有已审核状态的出库单可以执行
    if stock_out.status != '已审核':
        flash('只有已审核状态的出库单可以执行', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    # 更新库存
    for item in stock_out_items:
        inventory = Inventory.query.get(item.inventory_id)
        if inventory:
            # 检查库存是否足够
            if inventory.quantity < item.quantity:
                flash(f'食材 {item.ingredient.name} 库存不足，当前库存: {inventory.quantity} {inventory.unit}', 'danger')
                return redirect(url_for('stock_out.view', id=id))

            # 更新库存
            old_quantity = inventory.quantity
            inventory.quantity -= item.quantity

            # 记录库存更新
            current_app.logger.info(f"更新库存: 食材={item.ingredient.name}, 批次号={inventory.batch_number}, 原数量={old_quantity}, 减少={item.quantity}, 新数量={inventory.quantity}")

            # 如果库存数量小于等于0，将状态更新为"已用完"
            if inventory.quantity <= 0:
                inventory.status = '已用完'
                current_app.logger.info(f"库存已用完: 食材={item.ingredient.name}, 批次号={inventory.batch_number}, 状态更新为'已用完'")

    try:
        # 使用原始SQL语句更新出库单状态，避免ORM处理datetime字段
        sql = text("""
        UPDATE stock_outs
        SET status = '已出库'
        WHERE id = :id
        """).bindparams(
            id=id
        )

        # 执行SQL语句
        db.session.execute(sql)

        # 提交事务
        db.session.commit()

        flash('出库单执行成功，库存已更新', 'success')
        return redirect(url_for('stock_out.view', id=id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"执行出库单时出错: {str(e)}")
        flash(f'执行出库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_out.view', id=id))

@stock_out_bp.route('/stock-out/<int:id>/cancel', methods=['POST'])
@login_required
def cancel(id):
    """取消出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限取消
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限取消该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核或已审核状态的出库单可以取消
    if stock_out.status not in ['待审核', '已审核']:
        flash('只有待审核或已审核状态的出库单可以取消', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    try:
        # 使用原始SQL语句更新出库单状态，避免ORM处理datetime字段
        sql = text("""
        UPDATE stock_outs
        SET status = '已取消'
        WHERE id = :id
        """).bindparams(
            id=id
        )

        # 执行SQL语句
        db.session.execute(sql)

        # 提交事务
        db.session.commit()

        flash('出库单已取消', 'success')
        return redirect(url_for('stock_out.view', id=id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取消出库单时出错: {str(e)}")
        flash(f'取消出库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_out.view', id=id))

@stock_out_bp.route('/stock-out/<int:id>/print')
@login_required
def print_stock_out(id):
    """打印出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限查看该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 获取关联的消耗计划
    consumption_plan = None
    menu_plan = None
    if stock_out.consumption_plan_id:
        consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id)
        if consumption_plan:
            menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)

    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    return render_template('stock_out/print.html',
                          stock_out=stock_out,
                          stock_out_items=stock_out_items,
                          consumption_plan=consumption_plan,
                          menu_plan=menu_plan)
