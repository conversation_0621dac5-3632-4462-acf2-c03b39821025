"""
公开访问的API路由

提供公开访问的API接口，用于照片上传和评分。
"""

from flask import request, jsonify, current_app
from app.routes.daily_management import daily_management_bp
from app import db
from app.models_daily_management import DailyLog, InspectionRecord, Photo
from app.models import AdministrativeArea
from sqlalchemy import text
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from PIL import Image
import io
import traceback

def get_friendly_error_message(error):
    """将技术错误转换为用户友好的错误消息"""
    error_str = str(error).lower()

    # 文件相关错误
    if 'file' in error_str:
        if 'size' in error_str:
            return '文件大小超出限制，请选择小于5MB的图片'
        if 'format' in error_str or 'image' in error_str:
            return '不支持的图片格式，请使用JPG、PNG或GIF格式'
        return '文件处理失败，请重试'

    # 网络相关错误
    if 'network' in error_str or 'connection' in error_str:
        return '网络连接失败，请检查网络后重试'

    # 权限相关错误
    if 'permission' in error_str or 'access' in error_str:
        return '没有权限执行此操作'

    # 数据库相关错误
    if 'database' in error_str or 'sql' in error_str:
        return '系统暂时无法保存数据，请稍后重试'

    # 默认错误消息
    return '操作失败，请重试'

# 公开上传检查照片API
@daily_management_bp.route('/api/v2/inspections/public/upload-photos/<int:school_id>/<int:log_id>/<inspection_type>', methods=['POST'])
def public_upload_inspection_photos(school_id, log_id, inspection_type):
    """公开上传检查照片API"""
    try:
        # 验证学校和日志
        school = AdministrativeArea.query.get(school_id)
        if not school:
            return jsonify({
                'success': False,
                'error': '学校不存在',
                'error_type': 'not_found'
            }), 404

        log = DailyLog.query.filter_by(id=log_id, area_id=school_id).first()
        if not log:
            return jsonify({
                'success': False,
                'error': '日志不存在',
                'error_type': 'not_found'
            }), 404

        # 验证检查类型
        if inspection_type not in ['morning', 'noon', 'evening']:
            return jsonify({
                'success': False,
                'error': '检查类型无效',
                'error_type': 'invalid_input'
            }), 400

        # 获取检查项目（简化为时段检查）
        inspection_item = request.form.get('inspection_item', f'{inspection_type}_inspection')

        # 获取描述
        description = request.form.get('description', '')

        # 获取照片
        if 'photos' not in request.files:
            return jsonify({
                'success': False,
                'error': '请选择要上传的照片',
                'error_type': 'missing_file'
            }), 400

        photos = request.files.getlist('photos')
        if not photos or not photos[0].filename:
            return jsonify({
                'success': False,
                'error': '请选择要上传的照片',
                'error_type': 'missing_file'
            }), 400

        # 验证文件大小和类型
        for photo in photos:
            if photo.content_length and photo.content_length > 5 * 1024 * 1024:  # 5MB
                return jsonify({
                    'success': False,
                    'error': '文件大小超出限制，请选择小于5MB的图片',
                    'error_type': 'file_too_large'
                }), 400

            if not photo.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                return jsonify({
                    'success': False,
                    'error': '不支持的图片格式，请使用JPG、PNG或GIF格式',
                    'error_type': 'invalid_format'
                }), 400

        # 查找或创建检查记录（按时段统一管理）
        inspection = InspectionRecord.query.filter_by(
            daily_log_id=log_id,
            inspection_type=inspection_type,
            inspection_item=inspection_item
        ).first()

        if not inspection:
            # 创建新的检查记录
            inspection_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 使用原始SQL创建检查记录
            sql = text("""
                INSERT INTO inspection_records
                (daily_log_id, inspection_type, inspection_item, status, description, inspection_time)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :inspection_type, :inspection_item, :status, :description,
                 CONVERT(DATETIME2(1), :inspection_time, 120))
            """)

            result = db.session.execute(sql, {
                'daily_log_id': log_id,
                'inspection_type': inspection_type,
                'inspection_item': inspection_item,
                'status': 'normal',
                'description': description,
                'inspection_time': inspection_time_str
            })

            inspection_id = result.fetchone()[0]
            db.session.commit()

            # 创建一个临时对象用于后续处理
            class TempInspection:
                def __init__(self, id):
                    self.id = id

            inspection = TempInspection(inspection_id)

        # 上传照片 - 直接使用时段作为reference_type，日志ID作为reference_id
        uploaded_photos = []
        for photo_file in photos:
            if photo_file and photo_file.filename:
                try:
                    # 处理照片 - 使用时段类型和日志ID
                    photo_result = handle_photo_upload(photo_file, inspection_type, log_id, description)
                    if photo_result:
                        uploaded_photos.append(photo_result)
                except Exception as e:
                    current_app.logger.error(f"处理照片失败: {str(e)}")
                    current_app.logger.error(traceback.format_exc())
                    return jsonify({
                        'success': False,
                        'error': get_friendly_error_message(e),
                        'error_type': 'photo_processing_error'
                    }), 500

        if not uploaded_photos:
            return jsonify({
                'success': False,
                'error': '照片上传失败，请重试',
                'error_type': 'upload_failed'
            }), 500

        return jsonify({
            'success': True,
            'message': f'成功上传 {len(uploaded_photos)} 张照片',
            'photos': uploaded_photos
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"公开上传检查照片失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': get_friendly_error_message(e),
            'error_type': 'system_error'
        }), 500

# 公开评分照片API
@daily_management_bp.route('/api/v2/photos/public/rate', methods=['POST'])
def public_rate_photo():
    """公开评分照片API"""
    try:
        data = request.json
        if not data or 'photo_id' not in data or 'rating' not in data:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        photo_id = data['photo_id']
        rating = data['rating']
        comment = data.get('comment', '')

        # 验证评分范围
        if not isinstance(rating, int) or rating < 1 or rating > 5:
            return jsonify({'success': False, 'error': '评分必须是1-5之间的整数'}), 400

        # 更新照片评分和评论
        sql = text("""
        UPDATE photos
        SET rating = :rating, comment = :comment
        WHERE id = :photo_id
        """)

        result = db.session.execute(sql, {
            'photo_id': photo_id,
            'rating': rating,
            'comment': comment
        })
        if result.rowcount == 0:
            return jsonify({'success': False, 'error': '照片不存在'}), 404

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '评分成功',
            'photo_id': photo_id,
            'rating': rating
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"公开评分照片失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'error': f'评分失败: {str(e)}'}), 500

def handle_photo_upload(photo_file, reference_type, reference_id, description=''):
    """处理照片上传，返回照片路径"""
    if not photo_file or not photo_file.filename:
        return None

    try:
        filename = secure_filename(photo_file.filename)
        # 生成唯一文件名
        unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"

        # 确保目录存在 - 检查记录统一使用 inspection 文件夹
        upload_folder = os.path.join(
            current_app.static_folder,
            'uploads',
            'daily_management',
            'inspection'
        )
        os.makedirs(upload_folder, exist_ok=True)

        # 处理图片 - 压缩处理
        img = Image.open(photo_file)

        # 转换为RGB模式（如果是RGBA或其他模式）
        if img.mode in ('RGBA', 'LA', 'P'):
            img = img.convert('RGB')

        # 强制调整到800*600大小，保持宽高比
        target_size = (800, 600)

        # 计算缩放比例，确保图片适合目标尺寸
        img_ratio = img.width / img.height
        target_ratio = target_size[0] / target_size[1]

        if img_ratio > target_ratio:
            # 图片更宽，以宽度为准
            new_width = target_size[0]
            new_height = int(target_size[0] / img_ratio)
        else:
            # 图片更高，以高度为准
            new_height = target_size[1]
            new_width = int(target_size[1] * img_ratio)

        # 调整图片大小
        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 如果需要，创建800*600的白色背景并居中放置图片
        if new_width != target_size[0] or new_height != target_size[1]:
            # 创建白色背景
            background = Image.new('RGB', target_size, (255, 255, 255))

            # 计算居中位置
            x = (target_size[0] - new_width) // 2
            y = (target_size[1] - new_height) // 2

            # 将调整后的图片粘贴到背景上
            background.paste(img, (x, y))
            img = background

        # 压缩质量
        output = io.BytesIO()
        # 统一保存为JPEG格式，高质量压缩
        img.save(output, format='JPEG', quality=90, optimize=True)
        output.seek(0)

        # 更新文件名为.jpg
        if not unique_filename.lower().endswith('.jpg'):
            unique_filename = os.path.splitext(unique_filename)[0] + '.jpg'

        # 保存处理后的图片
        file_path = os.path.join(upload_folder, unique_filename)
        with open(file_path, 'wb') as f:
            f.write(output.getvalue())

        # 相对路径（用于数据库存储）- 检查记录统一使用 inspection 文件夹
        relative_path = f"/static/uploads/daily_management/inspection/{unique_filename}"

        # 创建照片记录 - 使用时段作为reference_type
        sql = text("""
        INSERT INTO photos
        (reference_id, reference_type, file_name, file_path, description, rating, upload_time)
        OUTPUT inserted.id, inserted.file_path
        VALUES
        (:reference_id, :reference_type, :file_name, :file_path, :description, :rating,
         CONVERT(DATETIME2(1), :upload_time, 120))
        """)

        result = db.session.execute(sql, {
            'reference_id': reference_id,
            'reference_type': reference_type,  # 这里会是 'morning', 'noon', 'evening'
            'file_name': unique_filename,
            'file_path': relative_path,
            'description': description,
            'rating': 3,  # 默认评分为3星
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

        row = result.fetchone()
        db.session.commit()

        return {
            'id': row[0],
            'file_path': row[1]
        }

    except Exception as e:
        current_app.logger.error(f"处理照片上传失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        raise
