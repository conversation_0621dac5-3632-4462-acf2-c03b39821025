<!DOCTYPE html>
<html>
<head>
    <title>测试上传</title>
</head>
<body>
    <h1>测试检查记录照片上传</h1>
    
    <form id="uploadForm">
        <input type="file" id="photos" name="photos" multiple accept="image/*" required>
        <br><br>
        <textarea id="description" name="description" placeholder="描述（可选）"></textarea>
        <br><br>
        <button type="submit">上传</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const photos = document.getElementById('photos').files;
            const description = document.getElementById('description').value;
            
            // 添加文件
            for (let i = 0; i < photos.length; i++) {
                formData.append('photos', photos[i]);
            }
            
            // 添加描述
            if (description) {
                formData.append('description', description);
            }
            
            // 添加检查项目
            formData.append('inspection_item', 'morning_inspection');
            
            console.log('发送请求到:', '/daily-management/api/v2/inspections/public/upload-photos/22/37/morning');
            
            // 发送请求
            fetch('/daily-management/api/v2/inspections/public/upload-photos/22/37/morning', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('响应数据:', data);
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('错误:', error);
                document.getElementById('result').innerHTML = '<p style="color: red;">错误: ' + error.message + '</p>';
            });
        });
    </script>
</body>
</html>
