"""
列出所有模板文件并检查strftime调用

查找所有模板文件中的 .strftime 调用，并输出文件名和行号
"""

import os
import re
import sys

# 模板目录
TEMPLATES_DIR = 'app/templates'

# 正则表达式模式，匹配 xxx.strftime('...') 或 xxx.strftime("%...")
STRFTIME_PATTERN = r'([a-zA-Z0-9_\.]+)\.strftime\([\'"]([^\'"]+)[\'"]\)'

def check_file(file_path):
    """检查单个文件中的strftime调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查文件是否包含 strftime 调用
        if '.strftime(' not in content:
            return []
            
        # 查找所有 strftime 调用
        matches = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '.strftime(' in line:
                matches.append((i+1, line.strip()))
                
        return matches
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return []

def scan_templates():
    """扫描所有模板文件"""
    template_files = []
    
    # 遍历模板目录
    for root, dirs, files in os.walk(TEMPLATES_DIR):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                template_files.append(file_path)
                
    print(f"找到 {len(template_files)} 个模板文件")
    
    # 检查每个文件
    files_with_strftime = 0
    total_matches = 0
    
    for file_path in template_files:
        matches = check_file(file_path)
        if matches:
            files_with_strftime += 1
            total_matches += len(matches)
            
            print(f"\n文件: {file_path}")
            for line_num, line in matches:
                print(f"  行 {line_num}: {line}")
                
    print(f"\n总计: 在 {files_with_strftime} 个文件中找到 {total_matches} 处 strftime 调用")

if __name__ == '__main__':
    print("开始扫描模板文件...")
    scan_templates()
    print("扫描完成！")
