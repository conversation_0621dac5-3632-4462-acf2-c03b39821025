{% extends 'base.html' %}

{% block title %}周菜单列表 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>周菜单列表</h2>
      <p class="text-muted">查看所有周菜单计划</p>
    </div>
    <div class="col-md-4 text-right">
      <div class="btn-group">
        <a href="{{ url_for('weekly_menu.plan') }}" class="btn btn-primary">
          <i class="fas fa-plus"></i> 新建周菜单
        </a>
        <a href="{{ url_for('weekly_menu.plan_time_aware') }}" class="btn btn-success">
          <i class="fas fa-calendar-alt"></i> 时间感知菜单
        </a>
        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-info">
          <i class="fas fa-rocket"></i> 使用新版周菜单
        </a>
      </div>
    </div>
  </div>

  <!-- 工作流程指导 -->
  <div class="card mb-4">
    <div class="card-header bg-info text-white">
      <h5 class="mb-0"><i class="fas fa-info-circle"></i> 工作流程指导</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-4">
          <div class="card h-100">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-calendar-alt text-primary"></i> 第1步：制定周菜单</h5>
              <p class="card-text">点击"新建周菜单"按钮，为下周制定菜单计划。</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-shopping-cart text-success"></i> 第2步：创建采购订单</h5>
              <p class="card-text">在菜单详情页点击"创建采购订单"，系统会根据菜单自动生成采购计划。</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-dolly text-warning"></i> 第3步：创建入库单</h5>
              <p class="card-text">在待确认、已确认或已送达状态的采购订单页面点击"创建入库单"按钮，使用向导式入库流程完成食材入库。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 筛选条件 -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">筛选条件</h5>
    </div>
    <div class="card-body">
      <form method="get" class="form-inline">
        <div class="form-group mr-3 mb-2">
          <label for="area_id" class="mr-2">区域</label>
          <select class="form-control" id="area_id" name="area_id">
            <option value="">全部区域</option>
            {% for area in areas %}
            <option value="{{ area.id }}" {% if area.id == area_id %}selected{% endif %}>
              {{ area.get_level_name() }} - {{ area.name }}
            </option>
            {% endfor %}
          </select>
        </div>
        <div class="form-group mr-3 mb-2">
          <label for="status" class="mr-2">状态</label>
          <select class="form-control" id="status" name="status">
            <option value="">全部状态</option>
            <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
            <option value="已发布" {% if status == '已发布' %}selected{% endif %}>已发布</option>
          </select>
        </div>
        <div class="form-group mr-3 mb-2">
          <label for="week" class="mr-2">周期</label>
          <input type="week" class="form-control" id="week" name="week" value="{{ week }}">
        </div>
        <div class="form-group mr-3 mb-2">
          <label for="start_date" class="mr-2">开始日期</label>
          <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
        </div>
        <div class="form-group mr-3 mb-2">
          <label for="end_date" class="mr-2">结束日期</label>
          <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
        </div>
        <button type="submit" class="btn btn-primary mb-2">应用筛选</button>
        <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-secondary mb-2 ml-2">重置</a>
      </form>
    </div>
  </div>

  <!-- 周菜单列表 -->
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">周菜单列表</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>ID</th>
              <th>区域</th>
              <th>周期</th>
              <th>状态</th>
              <th>创建人</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for weekly_menu in weekly_menus.items %}
            <tr>
              <td>{{ weekly_menu.id }}</td>
              <td>{{ weekly_menu.area.name }}</td>
              <td>{{ weekly_menu.week_display }}</td>
              <td>
                {% if weekly_menu.status == '计划中' %}
                <span class="badge badge-warning">计划中</span>
                {% elif weekly_menu.status == '已发布' %}
                <span class="badge badge-success">已发布</span>
                {% else %}
                <span class="badge badge-secondary">{{ weekly_menu.status }}</span>
                {% endif %}
              </td>
              <td>{{ weekly_menu.creator.real_name or weekly_menu.creator.username }}</td>
              <td>{{ weekly_menu.created_at|format_datetime }}</td>
              <td>
                <div class="btn-group btn-group-sm">
                  <a href="{{ url_for('weekly_menu.view', id=weekly_menu.id, t=now()|int) }}" class="btn btn-info" title="查看">
                    <i class="fas fa-eye"></i>
                  </a>
                  {% if weekly_menu.status == '计划中' %}
                  <a href="{{ url_for('weekly_menu.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start|format_datetime('%Y-%m-%d')) }}" class="btn btn-primary" title="编辑">
                    <i class="fas fa-edit"></i>
                  </a>
                  {% endif %}
                  <a href="{{ url_for('weekly_menu.print_menu', id=weekly_menu.id, t=now()|int) }}" class="btn btn-secondary" title="打印" target="_blank">
                    <i class="fas fa-print"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% else %}
            <tr>
              <td colspan="7" class="text-center">暂无周菜单数据</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      {% if weekly_menus.pages > 1 %}
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
          {% if weekly_menus.has_prev %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('weekly_menu.index', page=weekly_menus.prev_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">上一页</a>
          </li>
          {% else %}
          <li class="page-item disabled">
            <span class="page-link">上一页</span>
          </li>
          {% endif %}

          {% for page_num in weekly_menus.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
          {% if page_num %}
          {% if page_num == weekly_menus.page %}
          <li class="page-item active">
            <span class="page-link">{{ page_num }}</span>
          </li>
          {% else %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('weekly_menu.index', page=page_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">{{ page_num }}</a>
          </li>
          {% endif %}
          {% else %}
          <li class="page-item disabled">
            <span class="page-link">...</span>
          </li>
          {% endif %}
          {% endfor %}

          {% if weekly_menus.has_next %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('weekly_menu.index', page=weekly_menus.next_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">下一页</a>
          </li>
          {% else %}
          <li class="page-item disabled">
            <span class="page-link">下一页</span>
          </li>
          {% endif %}
        </ul>
      </nav>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
