"""
食堂日常管理仪表盘服务

提供食堂日常管理仪表盘所需的数据服务，包括摘要信息、周统计、月统计等。
"""

from datetime import datetime, date, timedelta
from sqlalchemy import func, and_, or_, extract
from flask_login import current_user
from app import db
from app.models_daily_management import (
    DailyLog, InspectionRecord, DiningCompanion,
    CanteenTrainingRecord, SpecialEvent, Issue, Photo
)
from app.models import (
    MenuPlan, MenuRecipe, Recipe, AdministrativeArea,
    WeeklyMenu, WeeklyMenuRecipe
)


class DashboardService:
    """仪表盘服务类"""

    @staticmethod
    def get_dashboard_summary(date_str=None, area_id=None):
        """
        获取仪表盘摘要信息

        Args:
            date_str: 日期字符串，格式为YYYY-MM-DD，默认为今天
            area_id: 区域ID，默认为None（所有可访问区域）

        Returns:
            dict: 包含摘要信息的字典
        """
        # 解析日期
        if date_str:
            try:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                target_date = date.today()
        else:
            target_date = date.today()

        # 获取当前用户可访问的区域
        if area_id:
            area_ids = [area_id]
        else:
            accessible_areas = current_user.get_accessible_areas()
            area_ids = [area.id for area in accessible_areas]

        # 获取日志
        daily_log = DailyLog.query.filter(
            DailyLog.log_date == target_date,
            DailyLog.area_id.in_(area_ids) if area_ids else True
        ).first()

        # 获取检查记录
        inspection_count = 0
        inspection_issues = 0
        if daily_log:
            inspections = InspectionRecord.query.filter_by(daily_log_id=daily_log.id).all()
            inspection_count = len(inspections)
            inspection_issues = sum(1 for i in inspections if i.result == 'abnormal')

        # 获取陪餐记录
        dining_companion_count = 0
        if daily_log:
            dining_companion_count = DiningCompanion.query.filter_by(daily_log_id=daily_log.id).count()

        # 获取培训记录
        training_count = 0
        if daily_log:
            training_count = CanteenTrainingRecord.query.filter_by(daily_log_id=daily_log.id).count()

        # 获取特殊事件
        event_count = 0
        if daily_log:
            event_count = SpecialEvent.query.filter_by(daily_log_id=daily_log.id).count()

        # 获取问题记录
        issue_count = Issue.query.filter(
            func.date(Issue.found_time) == target_date,
            Issue.area_id.in_(area_ids) if area_ids else True
        ).count()

        # 获取待处理问题
        pending_issue_count = Issue.query.filter(
            Issue.status == 'pending',
            Issue.area_id.in_(area_ids) if area_ids else True
        ).count()

        # 获取菜单计划
        menu_plans = MenuPlan.query.filter(
            MenuPlan.plan_date == target_date,
            MenuPlan.area_id.in_(area_ids) if area_ids else True
        ).all()

        # 构建菜单数据
        menu_data = {
            '早餐': None,
            '午餐': None,
            '晚餐': None
        }

        # 从菜单计划获取数据
        for plan in menu_plans:
            if plan.meal_type in menu_data:
                recipes = []
                for menu_recipe in plan.menu_recipes:
                    recipes.append({
                        'id': menu_recipe.recipe_id,
                        'name': menu_recipe.recipe.name if menu_recipe.recipe else '未知菜品',
                        'quantity': menu_recipe.planned_quantity
                    })

                menu_data[plan.meal_type] = {
                    'id': plan.id,
                    'status': plan.status,
                    'expected_diners': plan.expected_diners,
                    'recipes': recipes
                }

        # 如果某个餐次没有菜单计划，尝试从周菜单获取
        for meal_type in menu_data:
            if menu_data[meal_type] is None:
                # 获取当前是周几（0-6，0表示周一）
                weekday = target_date.weekday()

                # 查找包含当前日期的周菜单
                weekly_menu = WeeklyMenu.query.filter(
                    WeeklyMenu.week_start <= target_date,
                    WeeklyMenu.week_end >= target_date,
                    WeeklyMenu.area_id.in_(area_ids) if area_ids else True
                ).first()

                if weekly_menu:
                    # 查找对应日期和餐次的菜谱
                    weekly_recipes = WeeklyMenuRecipe.query.filter(
                        WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                        WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                        WeeklyMenuRecipe.meal_type == meal_type
                    ).all()

                    if weekly_recipes:
                        recipes = []
                        for weekly_recipe in weekly_recipes:
                            recipes.append({
                                'id': weekly_recipe.recipe_id,
                                'name': weekly_recipe.recipe_name,
                                'quantity': 0  # 周菜单没有数量信息
                            })

                        menu_data[meal_type] = {
                            'id': None,
                            'status': weekly_menu.status,
                            'expected_diners': 0,  # 周菜单没有预计就餐人数
                            'recipes': recipes,
                            'source': 'weekly_menu'  # 标记数据来源
                        }

        # 获取最近的陪餐记录（最多5条），不限于当天
        recent_dining_companions = []

        # 获取所有日志ID
        log_ids = [log.id for log in DailyLog.query.filter(DailyLog.area_id.in_(area_ids) if area_ids else True).all()]

        if log_ids:
            companions = DiningCompanion.query.filter(DiningCompanion.daily_log_id.in_(log_ids)).order_by(DiningCompanion.dining_time.desc()).limit(5).all()
            for companion in companions:
                recent_dining_companions.append({
                    'id': companion.id,
                    'name': companion.companion_name,
                    'role': companion.companion_role,
                    'time': companion.dining_time.strftime('%H:%M') if companion.dining_time else '',
                    'meal_type': companion.meal_type,
                    'comments': companion.comments,
                    'has_photo': Photo.query.filter_by(reference_id=companion.id, reference_type='companion').count() > 0
                })

        # 获取食品留样信息
        food_samples_count = 0
        if daily_log:
            # 这里需要根据您的实际模型结构查询食品留样信息
            # 示例代码，请根据实际情况调整
            from app.models import FoodSample
            food_samples_count = FoodSample.query.filter(
                func.date(FoodSample.sample_time) == target_date,
                FoodSample.area_id.in_(area_ids) if area_ids else True
            ).count()

        # 构建日常管理六大功能入口数据
        daily_management_functions = [
            {
                'name': '日志管理',
                'icon': 'fa-book',
                'url': '/daily-management/logs',
                'description': '记录每日食堂运营情况',
                'count': 1 if daily_log else 0
            },
            {
                'name': '检查记录',
                'icon': 'fa-clipboard-check',
                'url': '/daily-management/inspections',
                'description': '食品安全检查记录',
                'count': inspection_count
            },
            {
                'name': '陪餐记录',
                'icon': 'fa-utensils',
                'url': '/daily-management/dining-companions',
                'description': '教师陪餐记录管理',
                'count': dining_companion_count
            },
            {
                'name': '培训记录',
                'icon': 'fa-chalkboard-teacher',
                'url': '/daily-management/trainings',
                'description': '食堂人员培训记录',
                'count': training_count
            },
            {
                'name': '特殊事件',
                'icon': 'fa-exclamation-triangle',
                'url': '/daily-management/events',
                'description': '食堂特殊事件记录',
                'count': event_count
            },
            {
                'name': '问题记录',
                'icon': 'fa-bug',
                'url': '/daily-management/issues',
                'description': '食堂问题及整改记录',
                'count': issue_count
            }
        ]

        # 构建摘要数据
        summary = {
            'date': target_date.strftime('%Y-%m-%d'),
            'area_id': area_id,
            'daily_log': {
                'exists': daily_log is not None,
                'id': daily_log.id if daily_log else None,
                'manager': daily_log.manager if daily_log else None
            },
            'dining_companions': {
                'count': dining_companion_count,
                'recent': recent_dining_companions
            },
            'food_samples': {
                'count': food_samples_count
            },
            'daily_management': {
                'functions': daily_management_functions
            },
            'menu': menu_data,
            # 保留一些基本统计信息，但不在前端突出显示
            '_stats': {
                'student_count': daily_log.student_count if daily_log else 0,
                'teacher_count': daily_log.teacher_count if daily_log else 0,
                'other_count': daily_log.other_count if daily_log else 0,
                'total_count': (daily_log.student_count or 0) + (daily_log.teacher_count or 0) + (daily_log.other_count or 0) if daily_log else 0,
                'inspections': inspection_count,
                'inspection_issues': inspection_issues,
                'trainings': training_count,
                'events': event_count,
                'issues': issue_count,
                'pending_issues': pending_issue_count
            }
        }

        return summary

    @staticmethod
    def get_weekly_summary(week_start=None, area_id=None):
        """
        获取周摘要信息

        Args:
            week_start: 周开始日期字符串，格式为YYYY-MM-DD，默认为本周一
            area_id: 区域ID，默认为None（所有可访问区域）

        Returns:
            dict: 包含周摘要信息的字典
        """
        # 解析周开始日期
        if week_start:
            try:
                start_date = datetime.strptime(week_start, '%Y-%m-%d').date()
                # 确保是周一
                start_date = start_date - timedelta(days=start_date.weekday())
            except ValueError:
                # 如果日期格式不正确，使用当前日期的周一
                today = date.today()
                start_date = today - timedelta(days=today.weekday())
        else:
            # 默认为本周一
            today = date.today()
            start_date = today - timedelta(days=today.weekday())

        # 计算周结束日期（周日）
        end_date = start_date + timedelta(days=6)

        # 获取当前用户可访问的区域
        if area_id:
            area_ids = [area_id]
            area = AdministrativeArea.query.get(area_id)
            area_name = area.name if area else '未知区域'
        else:
            accessible_areas = current_user.get_accessible_areas()
            area_ids = [area.id for area in accessible_areas]
            area_name = '所有区域'

        # 获取周内的日志
        daily_logs = DailyLog.query.filter(
            DailyLog.log_date >= start_date,
            DailyLog.log_date <= end_date,
            DailyLog.area_id.in_(area_ids) if area_ids else True
        ).all()

        # 按日期组织日志
        logs_by_date = {log.log_date: log for log in daily_logs}

        # 获取周内的菜单计划
        menu_plans = MenuPlan.query.filter(
            MenuPlan.plan_date >= start_date,
            MenuPlan.plan_date <= end_date,
            MenuPlan.area_id.in_(area_ids) if area_ids else True
        ).all()

        # 按日期和餐次组织菜单计划
        plans_by_date_meal = {}
        for plan in menu_plans:
            if plan.plan_date not in plans_by_date_meal:
                plans_by_date_meal[plan.plan_date] = {}
            plans_by_date_meal[plan.plan_date][plan.meal_type] = plan

        # 构建周数据
        week_data = []
        for i in range(7):
            current_date = start_date + timedelta(days=i)
            log = logs_by_date.get(current_date)

            # 获取当日菜单
            daily_menu = {
                '早餐': None,
                '午餐': None,
                '晚餐': None
            }

            # 从菜单计划获取数据
            if current_date in plans_by_date_meal:
                for meal_type, plan in plans_by_date_meal[current_date].items():
                    if meal_type in daily_menu:
                        recipes = []
                        for menu_recipe in plan.menu_recipes:
                            recipes.append({
                                'id': menu_recipe.recipe_id,
                                'name': menu_recipe.recipe.name if menu_recipe.recipe else '未知菜品',
                                'quantity': menu_recipe.planned_quantity
                            })

                        daily_menu[meal_type] = {
                            'id': plan.id,
                            'status': plan.status,
                            'expected_diners': plan.expected_diners,
                            'recipes': recipes
                        }

            # 如果某个餐次没有菜单计划，尝试从周菜单获取
            for meal_type in daily_menu:
                if daily_menu[meal_type] is None:
                    # 获取当前是周几（0-6，0表示周一）
                    weekday = current_date.weekday()

                    # 查找包含当前日期的周菜单
                    weekly_menu = WeeklyMenu.query.filter(
                        WeeklyMenu.week_start <= current_date,
                        WeeklyMenu.week_end >= current_date,
                        WeeklyMenu.area_id.in_(area_ids) if area_ids else True
                    ).first()

                    if weekly_menu:
                        # 查找对应日期和餐次的菜谱
                        weekly_recipes = WeeklyMenuRecipe.query.filter(
                            WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                            WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                            WeeklyMenuRecipe.meal_type == meal_type
                        ).all()

                        if weekly_recipes:
                            recipes = []
                            for weekly_recipe in weekly_recipes:
                                recipes.append({
                                    'id': weekly_recipe.recipe_id,
                                    'name': weekly_recipe.recipe_name,
                                    'quantity': 0  # 周菜单没有数量信息
                                })

                            daily_menu[meal_type] = {
                                'id': None,
                                'status': weekly_menu.status,
                                'expected_diners': 0,  # 周菜单没有预计就餐人数
                                'recipes': recipes,
                                'source': 'weekly_menu'  # 标记数据来源
                            }

            # 构建日数据
            day_data = {
                'date': current_date.strftime('%Y-%m-%d'),
                'weekday': ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i],
                'log': {
                    'exists': log is not None,
                    'id': log.id if log else None,
                    'manager': log.manager if log else None,
                    'student_count': log.student_count if log else 0,
                    'teacher_count': log.teacher_count if log else 0,
                    'other_count': log.other_count if log else 0,
                    'total_count': (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) if log else 0
                },
                'menu': daily_menu
            }

            week_data.append(day_data)

        # 构建周摘要
        summary = {
            'week_start': start_date.strftime('%Y-%m-%d'),
            'week_end': end_date.strftime('%Y-%m-%d'),
            'area_id': area_id,
            'area_name': area_name,
            'week_data': week_data
        }

        return summary

    @staticmethod
    def get_monthly_summary(year=None, month=None, area_id=None):
        """
        获取月摘要信息

        Args:
            year: 年份，默认为当前年份
            month: 月份，默认为当前月份
            area_id: 区域ID，默认为None（所有可访问区域）

        Returns:
            dict: 包含月摘要信息的字典
        """
        # 解析年月
        if year is None or month is None:
            today = date.today()
            year = today.year
            month = today.month

        # 获取当前用户可访问的区域
        if area_id:
            area_ids = [area_id]
            area = AdministrativeArea.query.get(area_id)
            area_name = area.name if area else '未知区域'
        else:
            accessible_areas = current_user.get_accessible_areas()
            area_ids = [area.id for area in accessible_areas]
            area_name = '所有区域'

        # 获取月内的日志
        daily_logs = DailyLog.query.filter(
            extract('year', DailyLog.log_date) == year,
            extract('month', DailyLog.log_date) == month,
            DailyLog.area_id.in_(area_ids) if area_ids else True
        ).all()

        # 统计数据
        total_student_count = sum(log.student_count or 0 for log in daily_logs)
        total_teacher_count = sum(log.teacher_count or 0 for log in daily_logs)
        total_other_count = sum(log.other_count or 0 for log in daily_logs)
        total_diner_count = total_student_count + total_teacher_count + total_other_count

        # 获取月内的检查记录
        inspection_count = 0
        inspection_issues = 0
        for log in daily_logs:
            inspections = InspectionRecord.query.filter_by(daily_log_id=log.id).all()
            inspection_count += len(inspections)
            inspection_issues += sum(1 for i in inspections if i.result == 'abnormal')

        # 获取月内的陪餐记录
        dining_companion_count = 0
        for log in daily_logs:
            dining_companion_count += DiningCompanion.query.filter_by(daily_log_id=log.id).count()

        # 获取月内的培训记录
        training_count = 0
        for log in daily_logs:
            training_count += CanteenTrainingRecord.query.filter_by(daily_log_id=log.id).count()

        # 获取月内的特殊事件
        event_count = 0
        for log in daily_logs:
            event_count += SpecialEvent.query.filter_by(daily_log_id=log.id).count()

        # 获取月内的问题记录
        issue_count = Issue.query.filter(
            extract('year', Issue.found_time) == year,
            extract('month', Issue.found_time) == month,
            Issue.area_id.in_(area_ids) if area_ids else True
        ).count()

        # 获取待处理问题
        pending_issue_count = Issue.query.filter(
            Issue.status == 'pending',
            Issue.area_id.in_(area_ids) if area_ids else True
        ).count()

        # 构建月摘要
        summary = {
            'year': year,
            'month': month,
            'area_id': area_id,
            'area_name': area_name,
            'logs_count': len(daily_logs),
            'diners': {
                'student_count': total_student_count,
                'teacher_count': total_teacher_count,
                'other_count': total_other_count,
                'total_count': total_diner_count
            },
            'inspections': {
                'count': inspection_count,
                'issues': inspection_issues
            },
            'dining_companions': {
                'count': dining_companion_count
            },
            'trainings': {
                'count': training_count
            },
            'events': {
                'count': event_count
            },
            'issues': {
                'month_count': issue_count,
                'pending_count': pending_issue_count
            }
        }

        return summary
