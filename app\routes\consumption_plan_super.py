from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.models import (
    ConsumptionPlan, ConsumptionDetail, Ingredient, Inventory, InventoryAlert,
    AdministrativeArea, Warehouse, IngredientCategory
)
from app import db
from datetime import datetime, date
import json

consumption_plan_super_bp = Blueprint('consumption_plan_super', __name__)

@consumption_plan_super_bp.route('/consumption-plan/super-editor')
@login_required
def super_editor():
    """消耗计划超级编辑器"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    return render_template('consumption_plan/super_editor.html',
                          areas=accessible_areas,
                          today_date=date.today().strftime('%Y-%m-%d'))

@consumption_plan_super_bp.route('/consumption-plan/get-warehouses/<int:area_id>')
@login_required
def get_warehouses(area_id):
    """根据区域获取仓库列表"""
    try:
        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            return jsonify({'error': '您没有权限操作该区域'}), 403

        # 获取区域信息
        area = AdministrativeArea.query.get(area_id)
        if not area:
            return jsonify({
                'error': f'未找到ID为{area_id}的区域',
                'message': '请选择有效的区域'
            }), 404

        area_name = area.name

        # 获取该区域的所有仓库
        warehouses = Warehouse.query.filter_by(area_id=area_id, status='正常').all()

        # 转换为JSON格式
        warehouses_data = []
        for warehouse in warehouses:
            warehouses_data.append({
                'id': warehouse.id,
                'name': warehouse.name,
                'location': warehouse.location,
                'manager_id': warehouse.manager_id,
                'status': warehouse.status
            })

        return jsonify({
            'success': True,
            'area_id': area_id,
            'area_name': area_name,
            'warehouses': warehouses_data
        })

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取仓库列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取仓库列表失败',
            'message': str(e)
        }), 500

@consumption_plan_super_bp.route('/consumption-plan/get_inventory_batches')
@login_required
def get_inventory_batches():
    """获取仓库中的库存批次（按FIFO排序）"""
    warehouse_id = request.args.get('warehouse_id', type=int)

    if not warehouse_id:
        return jsonify({'error': '缺少仓库ID参数'}), 400

    # 检查仓库是否存在
    warehouse = Warehouse.query.get(warehouse_id)
    if not warehouse:
        return jsonify({'error': '仓库不存在'}), 404

    # 检查用户是否有权限访问该仓库
    if not current_user.can_access_area_by_id(warehouse.area_id):
        return jsonify({'error': '您没有权限访问该仓库'}), 403

    try:
        # 获取所有有库存的批次，按入库日期排序（FIFO原则）
        inventory_batches = Inventory.query.filter(
            Inventory.warehouse_id == warehouse_id,
            Inventory.status == '正常',
            Inventory.quantity > 0
        ).order_by(Inventory.production_date).all()

        # 转换为JSON格式
        result = []
        for batch in inventory_batches:
            # 获取供应商名称
            supplier_name = batch.supplier.name if batch.supplier else None

            # 获取食材分类
            category = batch.ingredient.category_rel.name if batch.ingredient.category_rel else '未分类'

            result.append({
                'id': batch.id,
                'ingredient_id': batch.ingredient_id,
                'ingredient_name': batch.ingredient.name,
                'category': category,
                'batch_number': batch.batch_number,
                'supplier_id': batch.supplier_id,
                'supplier_name': supplier_name,
                'production_date': batch.production_date.strftime('%Y-%m-%d') if batch.production_date else None,
                'expiry_date': batch.expiry_date.strftime('%Y-%m-%d') if batch.expiry_date else None,
                'quantity': float(batch.quantity),
                'unit': batch.unit,
                'unit_price': float(batch.unit_price) if hasattr(batch, 'unit_price') and batch.unit_price else 0.0,
                'storage_location_id': batch.storage_location_id,
                'storage_location_name': batch.storage_location.name if batch.storage_location else None
            })

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"获取库存批次时出错: {str(e)}")
        return jsonify({'error': '获取库存批次时出错', 'message': str(e)}), 500

@consumption_plan_super_bp.route('/consumption-plan/create_super', methods=['POST'])
@login_required
def create_super():
    """使用超级编辑器创建消耗计划"""
    try:
        # 获取表单数据
        area_id = request.form.get('area_id', type=int)
        warehouse_id = request.form.get('warehouse_id', type=int)
        consumption_date = request.form.get('consumption_date')
        meal_type = request.form.get('meal_type')
        diners_count = request.form.get('diners_count', type=int)
        notes = request.form.get('notes')

        # 验证必要参数
        if not all([area_id, warehouse_id, consumption_date, meal_type]):
            flash('请填写所有必要信息', 'danger')
            return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限操作该区域', 'danger')
            return redirect('http://127.0.0.1:5000/consumption-plan')

        # 获取选中的批次
        selected_batches = request.form.getlist('selected_batches[]')

        if not selected_batches:
            flash('请至少选择一个批次进行消耗', 'warning')
            return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

        # 使用原始 SQL 创建消耗计划，避免 SQLAlchemy ORM 的时间戳处理问题
        from sqlalchemy import text

        # 将日期字符串转换为 date 对象
        consumption_date_obj = datetime.strptime(consumption_date, '%Y-%m-%d').date()

        # 完全避免使用参数绑定，直接构建 SQL 语句
        # 将日期格式化为 SQL Server 可接受的格式
        formatted_date = consumption_date_obj.strftime('%Y-%m-%d')
        diners_count_value = diners_count or 1
        # 处理字符串中的单引号，避免 SQL 注入
        meal_type_safe = meal_type.replace("'", "''") if meal_type else ''
        notes_value = (notes or '').replace("'", "''")

        sql = text(f"""
            INSERT INTO consumption_plans
            (menu_plan_id, consumption_date, meal_type, diners_count, status, created_by, notes, created_at, updated_at)
            OUTPUT inserted.id
            VALUES
            (NULL, '{formatted_date}', '{meal_type_safe}', {diners_count_value}, '计划中', {current_user.id}, '{notes_value}',
            GETDATE(), GETDATE())
        """)

        result = db.session.execute(sql)
        consumption_plan_id = result.fetchone()[0]

        # 添加消耗明细
        for batch_id in selected_batches:
            quantity = request.form.get(f'quantity_{batch_id}', type=float)

            if quantity and quantity > 0:
                # 获取批次信息
                batch = Inventory.query.get(batch_id)

                if batch:
                    # 验证消耗数量不超过库存数量
                    if quantity > batch.quantity:
                        flash(f'食材 {batch.ingredient.name} (批次号: {batch.batch_number}) 的消耗数量不能超过库存数量 {batch.quantity} {batch.unit}', 'danger')
                        db.session.rollback()
                        return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

                    # 使用直接构建的 SQL 语句创建消耗明细
                    # 处理字符串中的单引号，避免 SQL 注入
                    unit_safe = batch.unit.replace("'", "''") if batch.unit else ''
                    batch_number_safe = batch.batch_number.replace("'", "''") if batch.batch_number else ''

                    # 将批次信息存储在 notes 字段中
                    notes_value = f'从批次 {batch_number_safe} 创建，库存ID: {batch.id}'
                    notes_safe = notes_value.replace("'", "''")

                    detail_sql = text(f"""
                        INSERT INTO consumption_details
                        (consumption_plan_id, ingredient_id, inventory_id, batch_number, planned_quantity, unit, status, is_main_ingredient, notes, created_at, updated_at)
                        VALUES
                        ({consumption_plan_id}, {batch.ingredient_id}, {batch.id}, '{batch_number_safe}', {quantity}, '{unit_safe}', '待出库', 1, '{notes_safe}', GETDATE(), GETDATE())
                    """)

                    db.session.execute(detail_sql)

        db.session.commit()

        # 检查库存是否足够
        from app.routes.consumption_plan import check_inventory
        check_inventory(consumption_plan_id, area_id, warehouse_id)

        flash('消耗计划创建成功', 'success')
        return redirect('http://127.0.0.1:5000/consumption-plan')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建消耗计划时出错: {str(e)}")
        flash(f'创建消耗计划时出错: {str(e)}', 'danger')
        return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')





