"""
检查项目中的链接问题
"""

import os
import re
import sys
from urllib.parse import urlparse

def is_valid_url(url):
    """检查URL是否有效"""
    parsed = urlparse(url)
    return bool(parsed.netloc) and bool(parsed.scheme)

def check_file_links(file_path):
    """检查文件中的链接"""
    problems = []
    
    # 跳过某些目录
    if 'venv' in file_path or '__pycache__' in file_path:
        return problems
    
    # 只检查HTML和Python文件
    if not (file_path.endswith('.html') or file_path.endswith('.py')):
        return problems
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查HTML链接
            if file_path.endswith('.html'):
                # 查找所有href属性
                href_pattern = re.compile(r'href=["\']([^"\']+)["\']')
                for match in href_pattern.finditer(content):
                    href = match.group(1)
                    
                    # 跳过锚点链接和JavaScript链接
                    if href.startswith('#') or href.startswith('javascript:'):
                        continue
                    
                    # 跳过有效的URL
                    if is_valid_url(href):
                        continue
                    
                    # 检查相对路径
                    if href.startswith('/'):
                        # 检查是否是有效的路由
                        if not href.startswith('/static/') and not href.startswith('/uploads/'):
                            # 检查是否使用了url_for
                            if 'url_for' not in content:
                                problems.append(f"可能的硬编码路径: {href}")
                    
                    # 检查空链接
                    if not href:
                        problems.append("空链接")
                
                # 查找所有src属性
                src_pattern = re.compile(r'src=["\']([^"\']+)["\']')
                for match in src_pattern.finditer(content):
                    src = match.group(1)
                    
                    # 跳过数据URI
                    if src.startswith('data:'):
                        continue
                    
                    # 跳过有效的URL
                    if is_valid_url(src):
                        continue
                    
                    # 检查相对路径
                    if src.startswith('/'):
                        # 检查是否是有效的静态文件路径
                        if not src.startswith('/static/') and not src.startswith('/uploads/'):
                            problems.append(f"可能的硬编码资源路径: {src}")
                    
                    # 检查空链接
                    if not src:
                        problems.append("空资源链接")
            
            # 检查Python文件中的URL
            elif file_path.endswith('.py'):
                # 查找硬编码的URL
                url_pattern = re.compile(r'["\']((http|https)://[^"\']+)["\']')
                for match in url_pattern.finditer(content):
                    url = match.group(1)
                    # 这里我们只是记录硬编码的URL，不一定是问题
                    if 'localhost' in url or '127.0.0.1' in url:
                        problems.append(f"硬编码的本地URL: {url}")
                
                # 查找硬编码的路由
                route_pattern = re.compile(r'@\w+_bp\.route\(["\']([^"\']+)["\']')
                redirect_pattern = re.compile(r'redirect\(["\']([^"\']+)["\']')
                
                for match in route_pattern.finditer(content):
                    route = match.group(1)
                    # 检查路由是否使用了变量
                    if '<' in route and '>' in route:
                        if not re.search(r'<\w+:\w+>', route):
                            problems.append(f"可能的路由类型问题: {route}")
                
                for match in redirect_pattern.finditer(content):
                    redirect_url = match.group(1)
                    # 检查是否使用了硬编码的重定向URL
                    if not redirect_url.startswith('/'):
                        continue
                    if 'url_for' not in content:
                        problems.append(f"硬编码的重定向URL: {redirect_url}")
    
    except Exception as e:
        problems.append(f"读取文件时出错: {str(e)}")
    
    return problems

def check_project_links(project_dir):
    """检查项目中的所有链接"""
    all_problems = {}
    
    for root, dirs, files in os.walk(project_dir):
        for file in files:
            file_path = os.path.join(root, file)
            problems = check_file_links(file_path)
            if problems:
                all_problems[file_path] = problems
    
    return all_problems

if __name__ == "__main__":
    project_dir = "app"
    if len(sys.argv) > 1:
        project_dir = sys.argv[1]
    
    print(f"检查项目 {project_dir} 中的链接问题...")
    problems = check_project_links(project_dir)
    
    if problems:
        print(f"发现 {len(problems)} 个文件中存在链接问题:")
        for file_path, file_problems in problems.items():
            print(f"\n{file_path}:")
            for problem in file_problems:
                print(f"  - {problem}")
    else:
        print("未发现链接问题。")
