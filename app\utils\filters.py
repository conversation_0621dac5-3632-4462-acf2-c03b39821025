"""
模板过滤器

提供各种模板过滤器，用于格式化数据。
"""

from datetime import datetime
from app.utils.datetime_helper import ensure_datetime, safe_strftime, parse_datetime

def format_datetime(value, format='%Y-%m-%d %H:%M'):
    """
    格式化日期时间，默认只精确到分钟

    Args:
        value: 日期时间对象或字符串
        format: 格式化字符串，默认为 '%Y-%m-%d %H:%M'

    Returns:
        str: 格式化后的日期时间字符串
    """
    if value is None:
        return ''
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except (ValueError, TypeError):
            try:
                value = datetime.strptime(value, '%Y-%m-%d %H:%M')
            except (ValueError, TypeError):
                return value
    if isinstance(value, datetime):
        return value.strftime(format)
    return value

def format_date(value, format='%Y-%m-%d'):
    """
    格式化日期，默认只精确到日

    Args:
        value: 日期对象或字符串
        format: 格式化字符串，默认为 '%Y-%m-%d'

    Returns:
        str: 格式化后的日期字符串
    """
    if value is None:
        return ''
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d')
        except (ValueError, TypeError):
            return value
    if isinstance(value, datetime):
        return value.strftime(format)
    return value

def format_time(value, format='%H:%M'):
    """
    格式化时间，默认只精确到分钟

    Args:
        value: 时间对象或字符串
        format: 格式化字符串，默认为 '%H:%M'

    Returns:
        str: 格式化后的时间字符串
    """
    if value is None:
        return ''
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%H:%M:%S')
        except (ValueError, TypeError):
            try:
                value = datetime.strptime(value, '%H:%M')
            except (ValueError, TypeError):
                return value
    if isinstance(value, datetime):
        return value.strftime(format)
    return value

def safe_datetime_format(value, format='%Y-%m-%d %H:%M:%S'):
    """
    安全地格式化日期时间，处理各种类型的输入

    Args:
        value: 输入值，可以是datetime对象、date对象、字符串或None
        format: 格式化字符串

    Returns:
        str: 格式化后的字符串，如果输入无效则返回空字符串
    """
    return safe_strftime(value, format)

def register_filters(app):
    """注册所有自定义过滤器"""
    app.jinja_env.filters['format_datetime'] = format_datetime
    app.jinja_env.filters['format_date'] = format_date
    app.jinja_env.filters['format_time'] = format_time
    app.jinja_env.filters['safe_datetime'] = safe_datetime_format
    app.jinja_env.filters['parse_datetime'] = parse_datetime

    # 添加一个特殊的过滤器，用于替代 .strftime() 方法
    # 这样模板中的 {{ value.strftime('%Y-%m-%d') }} 可以自动被处理
    app.jinja_env.filters['strftime'] = safe_datetime_format

    # 添加一个 Jinja2 扩展，拦截 .strftime 调用
    from jinja2 import nodes
    from jinja2.ext import Extension

    class SafeStrftimeExtension(Extension):
        """安全的 strftime 扩展，将 .strftime 调用转换为 |safe_datetime 过滤器"""

        def filter_stream(self, stream):
            """过滤模板令牌流"""
            for token in stream:
                yield token

    # 注册扩展
    app.jinja_env.add_extension(SafeStrftimeExtension)