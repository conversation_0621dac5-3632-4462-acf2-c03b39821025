"""
学校关联装饰器使用示例

本模块展示了如何在路由中使用学校关联装饰器。
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.utils.school_required import school_required, school_data_required
from app.models import AdministrativeArea
from app.models_daily_management import DailyLog
from app.services.base_service import BaseService

# 创建一个示例蓝图
example_bp = Blueprint('example', __name__)

# 示例1：使用school_required装饰器
@example_bp.route('/')
@login_required
@school_required
def index(user_area):
    """
    示例首页
    
    注意：school_required装饰器会自动将用户所属学校作为user_area参数传递给视图函数
    """
    # 获取用户所属学校的日志
    logs = DailyLog.query.filter_by(area_id=user_area.id).order_by(DailyLog.log_date.desc()).all()
    
    return render_template('example/index.html',
                          title=f'{user_area.name} - 示例首页',
                          logs=logs,
                          school=user_area)

# 示例2：使用school_data_required装饰器
@example_bp.route('/view_log/<int:id>')
@login_required
@school_data_required(DailyLog)
def view_log(id, record, user_area):
    """
    查看日志详情
    
    注意：school_data_required装饰器会自动检查日志是否属于用户所属学校，
    并将日志作为record参数和用户所属学校作为user_area参数传递给视图函数
    """
    return render_template('example/view_log.html',
                          title=f'{user_area.name} - 查看日志',
                          log=record,
                          school=user_area)

# 示例3：使用BaseService过滤查询
@example_bp.route('/logs')
@login_required
def logs():
    """
    日志列表
    
    使用BaseService过滤查询，只显示用户所属学校的日志
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法查看日志', 'danger')
        return redirect(url_for('main.index'))
    
    # 使用BaseService过滤查询
    query = DailyLog.query
    query = BaseService.filter_query_by_school(query, DailyLog)
    logs = query.order_by(DailyLog.log_date.desc()).all()
    
    return render_template('example/logs.html',
                          title=f'{user_area.name} - 日志列表',
                          logs=logs,
                          school=user_area)

# 示例4：使用BaseService执行SQL
@example_bp.route('/sql_example')
@login_required
def sql_example():
    """
    SQL示例
    
    使用BaseService执行带有学校筛选的SQL
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法查看数据', 'danger')
        return redirect(url_for('main.index'))
    
    # 使用BaseService执行SQL
    sql = """
    SELECT * FROM daily_logs
    ORDER BY log_date DESC
    """
    result = BaseService.execute_school_filtered_sql(sql)
    logs = result.fetchall()
    
    return render_template('example/sql_example.html',
                          title=f'{user_area.name} - SQL示例',
                          logs=logs,
                          school=user_area)

# 示例5：使用BaseService创建记录
@example_bp.route('/create_log', methods=['GET', 'POST'])
@login_required
def create_log():
    """
    创建日志
    
    使用BaseService创建关联到用户所属学校的记录
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建日志', 'danger')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        try:
            # 准备数据
            data = {
                'log_date': request.form.get('log_date'),
                'manager': request.form.get('manager'),
                'student_count': request.form.get('student_count', type=int),
                'teacher_count': request.form.get('teacher_count', type=int),
                'other_count': request.form.get('other_count', type=int),
                'created_by': current_user.id
            }
            
            # 使用BaseService创建记录
            log = BaseService.create_with_school(DailyLog, data)
            
            flash('日志创建成功', 'success')
            return redirect(url_for('example.view_log', id=log.id))
        except Exception as e:
            flash(f'创建日志失败: {str(e)}', 'danger')
    
    return render_template('example/create_log.html',
                          title=f'{user_area.name} - 创建日志',
                          school=user_area)
