#!/usr/bin/env python3
"""
调试单价保存问题的脚本
"""

import requests
import sys
from bs4 import BeautifulSoup

def debug_batch_editor_form():
    """调试批次编辑器表单"""
    print("🔍 调试批次编辑器单价保存问题...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76  # 测试用的入库单ID
    
    try:
        editor_url = f"{base_url}/stock-in/{stock_in_id}/batch-editor-simplified"
        print(f"📤 访问批次编辑器页面: {editor_url}")
        
        response = requests.get(editor_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 批次编辑器页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找单价输入框
            unit_price_inputs = soup.find_all('input', {'name': lambda x: x and x.startswith('unit_price_')})
            
            print(f"📋 找到 {len(unit_price_inputs)} 个单价输入框")
            
            for i, input_field in enumerate(unit_price_inputs[:3]):  # 只检查前3个
                name = input_field.get('name', '')
                value = input_field.get('value', '')
                required = input_field.get('required', False)
                disabled = input_field.get('disabled', False)
                
                print(f"  输入框 {i+1}:")
                print(f"    name: {name}")
                print(f"    value: {value}")
                print(f"    required: {required}")
                print(f"    disabled: {disabled}")
                
                if not value or value == '0' or value == '0.0':
                    print(f"    ⚠️ 单价值为空或为0")
                else:
                    print(f"    ✅ 单价值正常: {value}")
            
            # 查找表单
            form = soup.find('form', {'id': 'batchEditForm'})
            if form:
                action = form.get('action', '')
                method = form.get('method', '')
                print(f"📋 表单信息:")
                print(f"  action: {action}")
                print(f"  method: {method}")
                
                # 查找复选框
                checkboxes = form.find_all('input', {'class': 'batch-checkbox'})
                print(f"  复选框数量: {len(checkboxes)}")
                
                for i, checkbox in enumerate(checkboxes[:3]):  # 只检查前3个
                    value = checkbox.get('value', '')
                    checked = checkbox.get('checked', False)
                    print(f"    复选框 {i+1}: value={value}, checked={checked}")
            
            return True
            
        elif response.status_code == 302:
            print("⚠️ 重定向，可能需要登录")
            return False
        else:
            print(f"❌ 状态码: {response.status_code}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def debug_form_submission():
    """调试表单提交数据"""
    print("\n🔍 调试表单提交数据...")
    
    # 模拟表单数据
    form_data = {
        'csrf_token': 'test_token',
        'selected_items[]': ['1', '2'],
        'supplier_id_1': '1',
        'storage_location_id_1': '1',
        'quantity_1': '10.0',
        'unit_price_1': '5.0',  # 这是关键字段
        'production_date_1': '2025-05-25',
        'expiry_date_1': '2025-06-24',
        'supplier_id_2': '1',
        'storage_location_id_2': '1',
        'quantity_2': '20.0',
        'unit_price_2': '3.0',  # 这是关键字段
        'production_date_2': '2025-05-25',
        'expiry_date_2': '2025-06-24',
    }
    
    print("📋 模拟的表单数据:")
    for key, value in form_data.items():
        if 'unit_price' in key:
            print(f"  ✅ {key}: {value} (单价字段)")
        elif 'quantity' in key:
            print(f"  📊 {key}: {value} (数量字段)")
        else:
            print(f"  📝 {key}: {value}")
    
    return True

def check_database_schema():
    """检查数据库模式"""
    print("\n🔍 检查数据库模式...")
    
    # 这里我们无法直接访问数据库，但可以检查模型定义
    print("📋 根据代码分析，StockInItem 模型应该包含以下字段:")
    print("  - id: 主键")
    print("  - stock_in_id: 入库单ID")
    print("  - ingredient_id: 食材ID")
    print("  - supplier_id: 供应商ID")
    print("  - storage_location_id: 存储位置ID")
    print("  - batch_number: 批次号")
    print("  - quantity: 数量 (Float)")
    print("  - unit: 单位")
    print("  - unit_price: 单价 (Float) ⭐ 关键字段")
    print("  - production_date: 生产日期")
    print("  - expiry_date: 过期日期")
    print("  - created_at: 创建时间")
    print("  - updated_at: 更新时间")
    
    return True

def analyze_potential_issues():
    """分析潜在问题"""
    print("\n🔍 分析潜在问题...")
    
    issues = [
        {
            "问题": "禁用字段不提交",
            "描述": "当复选框未选中时，输入框被禁用，禁用的字段可能不会在表单提交时发送",
            "影响": "高",
            "解决方案": "在表单提交前启用所有选中行的字段，或使用隐藏字段"
        },
        {
            "问题": "JavaScript验证阻止提交",
            "描述": "如果单价为0或空，JavaScript验证会阻止表单提交",
            "影响": "中",
            "解决方案": "检查验证逻辑，确保允许合理的单价值"
        },
        {
            "问题": "AJAX提交问题",
            "描述": "使用FormData提交时，可能存在字段丢失",
            "影响": "中",
            "解决方案": "检查FormData是否正确包含所有字段"
        },
        {
            "问题": "服务器端处理问题",
            "描述": "服务器端可能没有正确处理单价字段",
            "影响": "低",
            "解决方案": "检查后端代码的字段处理逻辑"
        },
        {
            "问题": "数据类型转换问题",
            "描述": "单价字段的数据类型转换可能有问题",
            "影响": "低",
            "解决方案": "确保正确的数据类型转换"
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"\n{i}. {issue['问题']} (影响: {issue['影响']})")
        print(f"   描述: {issue['描述']}")
        print(f"   解决方案: {issue['解决方案']}")
    
    return True

def suggest_fixes():
    """建议修复方案"""
    print("\n💡 建议的修复方案:")
    
    fixes = [
        {
            "优先级": "高",
            "方案": "修复禁用字段问题",
            "实施": [
                "在表单提交前，临时启用所有选中行的输入字段",
                "或者使用隐藏字段来保存数据",
                "确保FormData包含所有必要的字段"
            ]
        },
        {
            "优先级": "中",
            "方案": "改进JavaScript验证",
            "实施": [
                "允许单价为0的情况（如果业务需要）",
                "改进错误提示信息",
                "添加调试日志"
            ]
        },
        {
            "优先级": "中",
            "方案": "添加调试功能",
            "实施": [
                "在表单提交前打印所有字段值",
                "在服务器端记录接收到的数据",
                "添加详细的错误日志"
            ]
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f"\n{i}. {fix['方案']} (优先级: {fix['优先级']})")
        for step in fix['实施']:
            print(f"   - {step}")
    
    return True

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code in [200, 302]:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def main():
    """主函数"""
    print("🚀 开始调试单价保存问题\n")
    
    # 检查服务器连接
    if not test_server_connection():
        print("\n❌ 无法连接到服务器，请先启动Flask应用")
        return False
    
    print()
    
    # 运行调试步骤
    steps = [
        ("检查批次编辑器表单", debug_batch_editor_form),
        ("调试表单提交数据", debug_form_submission),
        ("检查数据库模式", check_database_schema),
        ("分析潜在问题", analyze_potential_issues),
        ("建议修复方案", suggest_fixes),
    ]
    
    results = []
    for step_name, step_func in steps:
        print(f"\n{'='*60}")
        print(f"🔍 {step_name}")
        print('='*60)
        
        try:
            result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"❌ 步骤异常: {e}")
            results.append((step_name, False))
    
    # 输出调试结果摘要
    print(f"\n{'='*60}")
    print("📊 调试结果摘要")
    print('='*60)
    
    passed = 0
    total = len(results)
    
    for step_name, result in results:
        status = "✅ 完成" if result else "❌ 失败"
        print(f"{step_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个步骤完成")
    
    print("\n🎯 最可能的问题原因:")
    print("1. 🔴 禁用字段不提交：当复选框未选中时，输入框被禁用，禁用的字段不会在表单提交时发送")
    print("2. 🟡 JavaScript验证：单价为0时被验证逻辑阻止")
    print("3. 🟡 AJAX提交：FormData可能没有正确包含禁用的字段")
    
    print("\n💡 建议立即检查:")
    print("1. 在浏览器开发者工具中查看表单提交的数据")
    print("2. 检查是否所有选中的行的单价字段都被正确提交")
    print("3. 在服务器端添加日志，查看接收到的单价数据")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
