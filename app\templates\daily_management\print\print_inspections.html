{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print-styles.css') }}">
<style>
    body {
        font-size: 14pt;
    }
    
    .section-title {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 10px;
        border-bottom: 1px solid #4e73df;
        padding-bottom: 5px;
        color: #4e73df;
    }
    
    .info-row {
        margin-bottom: 10px;
    }
    
    .info-label {
        font-weight: bold;
    }
    
    .print-page {
        padding: 20mm;
        margin-bottom: 20mm;
        border: 1px solid #ddd;
        background: #fff;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        body {
            font-size: 12pt;
        }
        
        .section-title {
            font-size: 14pt;
        }
        
        .print-page {
            padding: 0;
            margin: 0;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 打印控制按钮 -->
    <div class="no-print mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">打印预览 - 检查记录</h6>
                <div>
                    <a href="{{ url_for('daily_management.inspections', log_id=log.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left mr-1"></i> 返回检查记录
                    </a>
                    <button onclick="window.print()" class="btn btn-primary btn-sm">
                        <i class="fas fa-print mr-1"></i> 打印
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-1"></i> 打印预览模式。点击"打印"按钮开始打印，或使用浏览器的打印功能（Ctrl+P）。
                </div>
            </div>
        </div>
    </div>

    <!-- 打印内容 -->
    <div class="print-preview">
        <!-- 第一页：检查记录 -->
        <div class="print-page avoid-break">
            <div class="print-page-indicator no-print">第1页</div>
            
            <!-- 页眉 -->
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂检查记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <!-- 早餐检查记录 -->
            {% if morning_inspections %}
            <div class="section-title">早餐检查记录</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="20%">检查项目</th>
                        <th width="15%">状态</th>
                        <th width="45%">描述</th>
                        <th width="15%">检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in morning_inspections %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time|safe_datetime('%H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="section-title">早餐检查记录</div>
            <p class="text-muted">暂无早餐检查记录</p>
            {% endif %}
        </div>
        
        <!-- 第二页：午餐检查记录 -->
        {% if noon_inspections %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">第2页</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂检查记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">午餐检查记录</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="20%">检查项目</th>
                        <th width="15%">状态</th>
                        <th width="45%">描述</th>
                        <th width="15%">检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in noon_inspections %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time|safe_datetime('%H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        <!-- 第三页：晚餐检查记录 -->
        {% if evening_inspections %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">第3页</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂检查记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">晚餐检查记录</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="20%">检查项目</th>
                        <th width="15%">状态</th>
                        <th width="45%">描述</th>
                        <th width="15%">检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in evening_inspections %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time|safe_datetime('%H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        <!-- 签名区域 -->
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">签名页</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂检查记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">签名确认</div>
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="text-center">
                        <p>检查人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>食堂负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>学校负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <p>（本文档由系统自动生成，打印后有效）</p>
            </div>
        </div>
    </div>
</div>

<!-- 打印控制按钮（固定在右下角） -->
<div class="print-controls no-print">
    <button onclick="window.print()"><i class="fas fa-print mr-1"></i> 打印</button>
    <button onclick="window.location.href='{{ url_for('daily_management.inspections', log_id=log.id) }}'"><i class="fas fa-times mr-1"></i> 取消</button>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 打印预览初始化
        $('.print-page').each(function(index) {
            $(this).find('.print-page-indicator').text('第' + (index + 1) + '页');
        });
    });
</script>
{% endblock %}
