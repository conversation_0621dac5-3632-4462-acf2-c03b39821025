#!/usr/bin/env python3
"""
快速测试脚本 - 验证CSP和文件上传修复
"""

import requests
import sys

def quick_test():
    """快速测试主要功能"""
    print("🚀 快速测试 - CSP和文件上传修复")
    print("="*50)
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试页面列表
    test_pages = [
        ("/", "主页"),
        ("/stock-in/create-from-purchase-order/40", "入库单创建页面"),
        ("/trace-document/upload/1", "溯源文档上传页面"),
    ]
    
    results = []
    
    for path, name in test_pages:
        url = base_url + path
        print(f"\n📄 测试 {name}: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            status_code = response.status_code
            
            if status_code == 200:
                print(f"✅ 页面可访问 (状态码: {status_code})")
                
                # 检查CSP头
                csp_header = response.headers.get('Content-Security-Policy')
                if csp_header:
                    print("✅ 发现CSP头")
                    if 'unsafe-eval' in csp_header:
                        print("✅ CSP允许eval函数")
                    else:
                        print("⚠️ CSP可能阻止eval函数")
                else:
                    print("⚠️ 未发现CSP头")
                
                # 检查修复脚本
                content = response.text
                if 'file-upload-fix.js' in content:
                    print("✅ 包含文件上传修复脚本")
                else:
                    print("❌ 未包含文件上传修复脚本")
                
                # 检查文件上传元素
                if 'type="file"' in content:
                    print("✅ 包含文件上传元素")
                else:
                    print("ℹ️ 无文件上传元素")
                
                results.append((name, True))
                
            elif status_code == 404:
                print(f"⚠️ 页面不存在 (状态码: {status_code})")
                results.append((name, False))
            else:
                print(f"❌ 页面访问异常 (状态码: {status_code})")
                results.append((name, False))
                
        except requests.RequestException as e:
            print(f"❌ 请求失败: {e}")
            results.append((name, False))
    
    # 输出结果摘要
    print(f"\n{'='*50}")
    print("📊 测试结果摘要")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个页面测试通过")
    
    if passed == total:
        print("\n🎉 快速测试通过！")
        print("\n💡 下一步手动测试:")
        print("1. 打开浏览器访问测试页面")
        print("2. 打开开发者工具查看Console")
        print("3. 确认无CSP错误")
        print("4. 测试文件上传功能")
        return True
    else:
        print("\n⚠️ 部分测试失败")
        if passed == 0:
            print("💡 请确保Flask应用正在运行: python run.py")
        return False

if __name__ == '__main__':
    success = quick_test()
    sys.exit(0 if success else 1)
