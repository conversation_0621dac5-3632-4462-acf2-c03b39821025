"""
系统修复路由 - 提供通过GET请求执行各种系统修复操作的功能
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.system_fix import system_fix_bp
from sqlalchemy import text
import traceback
import json
import os
from datetime import datetime

# 修复操作日志
def log_fix_operation(operation, status, details=None):
    """记录修复操作日志"""
    log_dir = os.path.join(current_app.root_path, 'logs')
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, f'system_fix_{datetime.now().strftime("%Y%m%d")}.log')

    with open(log_file, 'a', encoding='utf-8') as f:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        user_info = f"{current_user.username}({current_user.id})" if current_user.is_authenticated else "未登录用户"
        log_entry = f"[{timestamp}] {user_info} - {operation} - {status}"
        if details:
            log_entry += f" - {details}"
        f.write(log_entry + "\n")

# 权限检查装饰器
def admin_required(func):
    """确保只有管理员可以执行修复操作"""
    @login_required
    def decorated_view(*args, **kwargs):
        if not current_user.is_admin():
            flash('只有管理员可以执行此操作', 'danger')
            log_fix_operation(func.__name__, "权限拒绝")
            return redirect(url_for('main.index'))
        return func(*args, **kwargs)
    decorated_view.__name__ = func.__name__
    return decorated_view

@system_fix_bp.route('/')
@admin_required
def index():
    """修复工具首页"""
    return render_template('system_fix/index.html')

@system_fix_bp.route('/fix_recipes_table')
@admin_required
def fix_recipes_table():
    """修复recipes表结构"""
    try:
        # 检查表是否存在
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'recipes'"))
        if result.scalar() == 0:
            flash("recipes表不存在，请先创建表", "danger")
            log_fix_operation("fix_recipes_table", "失败", "表不存在")
            return redirect(url_for('system_fix.index'))

        # 获取当前表结构
        result = db.session.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'recipes'"))
        existing_columns = [row[0].lower() for row in result.fetchall()]

        # 需要添加的列
        columns_to_add = {
            'category': 'NVARCHAR(50)',
            'meal_type': 'NVARCHAR(20)',
            'main_image': 'NVARCHAR(200)',
            'calories': 'FLOAT',
            'cooking_steps': 'NVARCHAR(MAX)',
            'parent_id': 'INT',
            'is_template': 'BIT DEFAULT 0',
            'template_type': 'NVARCHAR(20)',
            'variation_reason': 'NVARCHAR(200)',
            'version': 'INT DEFAULT 1'
        }

        # 添加缺少的列
        added_columns = []
        for column, data_type in columns_to_add.items():
            if column.lower() not in existing_columns:
                try:
                    sql = f"ALTER TABLE recipes ADD {column} {data_type}"
                    db.session.execute(text(sql))
                    added_columns.append(f"{column} ({data_type})")
                except Exception as e:
                    flash(f"添加列 {column} 时出错: {str(e)}", "danger")
                    log_fix_operation("fix_recipes_table", "部分失败", f"添加列 {column} 时出错: {str(e)}")
                    db.session.rollback()
                    return redirect(url_for('system_fix.index'))

        # 提交事务
        db.session.commit()

        if added_columns:
            flash(f"recipes表结构修复完成，已添加列: {', '.join(added_columns)}", "success")
            log_fix_operation("fix_recipes_table", "成功", f"已添加列: {', '.join(added_columns)}")
        else:
            flash("recipes表结构已是最新，无需修复", "info")
            log_fix_operation("fix_recipes_table", "成功", "表结构已是最新")

        return redirect(url_for('system_fix.index'))

    except Exception as e:
        db.session.rollback()
        error_details = traceback.format_exc()
        flash(f"修复recipes表时出错: {str(e)}", "danger")
        log_fix_operation("fix_recipes_table", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/fix_sql_syntax')
@admin_required
def fix_sql_syntax():
    """修复SQL语法兼容性问题"""
    # 这个功能比较复杂，需要扫描和修改代码文件
    # 在实际实现中，可能需要更复杂的逻辑或者调用外部脚本
    flash("SQL语法修复功能需要在命令行中执行，请使用 python fix_sql_syntax_issues.py", "warning")
    log_fix_operation("fix_sql_syntax", "跳过", "需要在命令行执行")
    return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/fix_missing_data')
@admin_required
def fix_missing_data():
    """修复缺失的数据"""
    try:
        # 这里可以实现检查和修复缺失数据的逻辑
        # 例如，检查关键表中是否有数据，如果没有则从备份或其他来源恢复

        # 示例：检查administrative_areas表是否有数据
        from app.models import AdministrativeArea

        areas_count = AdministrativeArea.query.count()
        if areas_count == 0:
            flash("行政区域表中没有数据，请使用数据导入功能添加数据", "warning")
            log_fix_operation("fix_missing_data", "警告", "行政区域表中没有数据")
        else:
            flash(f"行政区域表中有 {areas_count} 条数据", "info")
            log_fix_operation("fix_missing_data", "成功", f"行政区域表中有 {areas_count} 条数据")

        return redirect(url_for('system_fix.index'))

    except Exception as e:
        error_details = traceback.format_exc()
        flash(f"检查数据时出错: {str(e)}", "danger")
        log_fix_operation("fix_missing_data", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/verify_database_structure')
@admin_required
def verify_database_structure():
    """验证数据库结构"""
    try:
        # 获取所有表
        result = db.session.execute(text("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"))
        tables = [row[0] for row in result.fetchall()]

        # 检查关键表是否存在
        required_tables = [
            'users', 'roles', 'user_roles', 'administrative_areas',
            'ingredients', 'recipes', 'suppliers', 'menu_plans'
        ]

        missing_tables = [table for table in required_tables if table.lower() not in [t.lower() for t in tables]]

        if missing_tables:
            flash(f"缺少以下关键表: {', '.join(missing_tables)}", "danger")
            log_fix_operation("verify_database_structure", "失败", f"缺少表: {', '.join(missing_tables)}")
        else:
            flash("所有关键表都存在", "success")
            log_fix_operation("verify_database_structure", "成功", f"检查了 {len(tables)} 个表")

        return redirect(url_for('system_fix.index'))

    except Exception as e:
        error_details = traceback.format_exc()
        flash(f"验证数据库结构时出错: {str(e)}", "danger")
        log_fix_operation("verify_database_structure", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/fix_food_samples_table')
@admin_required
def fix_food_samples_table():
    """修复food_samples表结构"""
    try:
        # 检查表是否存在
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'food_samples'"))
        if result.scalar() == 0:
            flash("food_samples表不存在，请先创建表", "danger")
            log_fix_operation("fix_food_samples_table", "失败", "表不存在")
            return redirect(url_for('system_fix.index'))

        # 获取当前表结构
        result = db.session.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'food_samples'"))
        existing_columns = [row[0].lower() for row in result.fetchall()]

        # 需要添加的列
        columns_to_add = {
            'operator_id': 'INT',
            'destruction_time': 'DATETIME2',
            'destruction_operator_id': 'INT'
        }

        # 添加缺少的列
        added_columns = []
        for column, data_type in columns_to_add.items():
            if column.lower() not in existing_columns:
                try:
                    sql = f"ALTER TABLE food_samples ADD {column} {data_type}"
                    db.session.execute(text(sql))
                    added_columns.append(f"{column} ({data_type})")
                except Exception as e:
                    flash(f"添加列 {column} 时出错: {str(e)}", "danger")
                    log_fix_operation("fix_food_samples_table", "部分失败", f"添加列 {column} 时出错: {str(e)}")
                    db.session.rollback()
                    return redirect(url_for('system_fix.index'))

        # 提交事务
        db.session.commit()

        if added_columns:
            flash(f"food_samples表结构修复完成，已添加列: {', '.join(added_columns)}", "success")
            log_fix_operation("fix_food_samples_table", "成功", f"已添加列: {', '.join(added_columns)}")
        else:
            flash("food_samples表结构已是最新，无需修复", "info")
            log_fix_operation("fix_food_samples_table", "成功", "表结构已是最新")

        return redirect(url_for('system_fix.index'))

    except Exception as e:
        db.session.rollback()
        error_details = traceback.format_exc()
        flash(f"修复food_samples表时出错: {str(e)}", "danger")
        log_fix_operation("fix_food_samples_table", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/fix_purchase_orders_table')
@admin_required
def fix_purchase_orders_table():
    """修复purchase_orders表结构"""
    try:
        # 检查表是否存在
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'purchase_orders'"))
        if result.scalar() > 0:
            flash("purchase_orders表已存在，无需创建", "info")
            log_fix_operation("fix_purchase_orders_table", "成功", "表已存在")
            return redirect(url_for('system_fix.index'))

        # 创建purchase_orders表
        create_table_sql = """
        CREATE TABLE purchase_orders (
            id INT IDENTITY(1,1) PRIMARY KEY,
            order_number NVARCHAR(50) NOT NULL,
            supplier_id INT NOT NULL,
            requisition_id INT NULL,
            area_id INT NOT NULL,
            total_amount DECIMAL(12, 2) NOT NULL,
            order_date DATETIME2 NOT NULL,
            expected_delivery_date DATE NULL,
            payment_terms NVARCHAR(200) NULL,
            delivery_terms NVARCHAR(200) NULL,
            status NVARCHAR(20) NOT NULL,
            delivery_date DATETIME2 NULL,
            created_by INT NOT NULL,
            approved_by INT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL
        )
        """
        db.session.execute(text(create_table_sql))

        # 创建purchase_order_items表
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'purchase_order_items'"))
        if result.scalar() == 0:
            create_items_table_sql = """
            CREATE TABLE purchase_order_items (
                id INT IDENTITY(1,1) PRIMARY KEY,
                order_id INT NOT NULL,
                product_id INT NOT NULL,
                ingredient_id INT NOT NULL,
                quantity FLOAT NOT NULL,
                unit NVARCHAR(20) NOT NULL,
                unit_price DECIMAL(10, 2) NOT NULL,
                total_price DECIMAL(10, 2) NOT NULL,
                received_quantity FLOAT NULL DEFAULT 0,
                notes NVARCHAR(MAX) NULL,
                created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
                updated_at DATETIME2 NULL
            )
            """
            db.session.execute(text(create_items_table_sql))
            flash("purchase_order_items表已创建", "success")
            log_fix_operation("fix_purchase_orders_table", "成功", "创建了purchase_order_items表")

        # 提交事务
        db.session.commit()
        flash("purchase_orders表已创建", "success")
        log_fix_operation("fix_purchase_orders_table", "成功", "创建了purchase_orders表")

        return redirect(url_for('system_fix.index'))

    except Exception as e:
        db.session.rollback()
        error_details = traceback.format_exc()
        flash(f"创建purchase_orders表时出错: {str(e)}", "danger")
        log_fix_operation("fix_purchase_orders_table", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/auto_fix')
@admin_required
def auto_fix():
    """自动检测和修复所有问题"""
    try:
        from app.system_fix.auto_fix import auto_fix_all

        # 执行自动修复
        result = auto_fix_all()

        # 处理结果
        fixed_tables = result.get('fixed_tables', [])
        fixed_columns = result.get('fixed_columns', {})

        if fixed_tables or fixed_columns:
            message = "自动修复完成。"
            if fixed_tables:
                message += f"已创建表: {', '.join(fixed_tables)}。"
            if fixed_columns:
                for table, columns in fixed_columns.items():
                    message += f"已在表 {table} 中添加列: {', '.join(columns)}。"
            flash(message, "success")
        else:
            flash("自动修复完成，未发现需要修复的问题", "info")

        log_fix_operation("auto_fix", "成功", f"已修复表: {fixed_tables}, 已修复列: {fixed_columns}")
        return redirect(url_for('system_fix.index'))

    except Exception as e:
        error_details = traceback.format_exc()
        flash(f"自动修复时出错: {str(e)}", "danger")
        log_fix_operation("auto_fix", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/enhanced_auto_fix')
@admin_required
def enhanced_auto_fix():
    """增强版自动检测和修复所有问题"""
    try:
        from app.system_fix.enhanced_auto_fix import enhanced_auto_fix_all

        # 执行增强版自动修复
        result = enhanced_auto_fix_all()

        # 处理结果
        fixed_tables = result.get('fixed_tables', [])
        fixed_columns = result.get('fixed_columns', {})
        type_issues = result.get('type_issues', {})
        fixed_files = result.get('fixed_files', [])

        # 构建消息
        message_parts = []
        if fixed_tables:
            message_parts.append(f"已创建表: {', '.join(fixed_tables)}")

        if fixed_columns:
            for table, columns in fixed_columns.items():
                message_parts.append(f"已在表 {table} 中添加列: {', '.join(columns)}")

        if type_issues:
            message_parts.append(f"发现 {len(type_issues)} 个数据类型不匹配问题")

        if fixed_files:
            message_parts.append(f"修复了 {len(fixed_files)} 个文件中的SQL语法兼容性问题")

        if message_parts:
            flash(f"增强版自动修复完成。{' '.join(message_parts)}", "success")
        else:
            flash("增强版自动修复完成，未发现需要修复的问题", "info")

        log_fix_operation("enhanced_auto_fix", "成功",
                         f"已修复表: {fixed_tables}, 已修复列: {fixed_columns}, " +
                         f"类型问题: {len(type_issues)}, 修复文件: {len(fixed_files)}")
        return redirect(url_for('system_fix.index'))

    except Exception as e:
        error_details = traceback.format_exc()
        flash(f"增强版自动修复时出错: {str(e)}", "danger")
        log_fix_operation("enhanced_auto_fix", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/fix_supplier_school_relations')
@admin_required
def fix_supplier_school_relations():
    """修复supplier_school_relations表结构"""
    try:
        from app.system_fix.fix_supplier_school_relations import fix_supplier_school_relations_table

        # 执行修复
        fix_supplier_school_relations_table()

        flash("supplier_school_relations表结构修复完成", "success")
        log_fix_operation("fix_supplier_school_relations", "成功", "修复了datetime精度问题")
        return redirect(url_for('system_fix.index'))

    except Exception as e:
        error_details = traceback.format_exc()
        flash(f"修复supplier_school_relations表时出错: {str(e)}", "danger")
        log_fix_operation("fix_supplier_school_relations", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/fix_needs_inspection')
@admin_required
def fix_needs_inspection_route():
    """修复 needs_inspection 和 inspection_type 字段缺失的问题"""
    try:
        from app.system_fix.fix_needs_inspection import fix_needs_inspection

        # 执行修复
        result = fix_needs_inspection()

        flash(result, "success")
        log_fix_operation("fix_needs_inspection", "成功", result)
        return redirect(url_for('system_fix.index'))

    except Exception as e:
        error_details = traceback.format_exc()
        flash(f"修复 needs_inspection 和 inspection_type 字段时出错: {str(e)}", "danger")
        log_fix_operation("fix_needs_inspection", "失败", error_details)
        return redirect(url_for('system_fix.index'))

@system_fix_bp.route('/fix_all')
@admin_required
def fix_all():
    """执行所有修复操作"""
    try:
        # 执行增强版自动修复
        enhanced_auto_fix()

        # 依次执行各种修复操作
        # 1. 修复recipes表
        fix_recipes_table()

        # 2. 修复food_samples表
        fix_food_samples_table()

        # 3. 修复purchase_orders表
        fix_purchase_orders_table()

        # 4. 修复supplier_school_relations表
        fix_supplier_school_relations()

        # 5. 修复 needs_inspection 和 inspection_type 字段
        fix_needs_inspection_route()

        # 6. 验证数据库结构
        verify_database_structure()

        # 7. 检查缺失数据
        fix_missing_data()

        flash("所有修复操作已完成", "success")
        log_fix_operation("fix_all", "成功", "执行了所有修复操作")
        return redirect(url_for('system_fix.index'))

    except Exception as e:
        error_details = traceback.format_exc()
        flash(f"执行所有修复操作时出错: {str(e)}", "danger")
        log_fix_operation("fix_all", "失败", error_details)
        return redirect(url_for('system_fix.index'))
