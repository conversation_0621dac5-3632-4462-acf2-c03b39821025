from app import create_app, db
from app.models import AdministrativeArea, WeeklyMenu, WeeklyMenuRecipe
from sqlalchemy import text

app = create_app()

with app.app_context():
    # 查询所有区域
    print('区域列表:')
    areas = AdministrativeArea.query.all()
    for area in areas:
        print(f'ID: {area.id}, 名称: {area.name}')
    
    # 查询城南小学的区域ID
    chengnan = AdministrativeArea.query.filter(AdministrativeArea.name.like('%城南%')).first()
    if chengnan:
        print(f'\n城南小学区域ID: {chengnan.id}')
        
        # 查询该区域的菜单
        print(f'\n城南小学的菜单:')
        menus = WeeklyMenu.query.filter_by(area_id=chengnan.id).all()
        if menus:
            for menu in menus:
                print(f'ID: {menu.id}, 开始日期: {menu.week_start}, 状态: {menu.status}')
                
                # 查询菜单详情
                recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=menu.id).count()
                print(f'  - 包含 {recipes} 个菜谱项')
        else:
            print('没有找到菜单记录')
            
        # 使用原始SQL查询
        print('\n使用原始SQL查询:')
        result = db.session.execute(text(f"SELECT * FROM weekly_menu WHERE area_id = {chengnan.id}"))
        rows = result.fetchall()
        if rows:
            for row in rows:
                print(f'ID: {row[0]}, 区域ID: {row[1]}, 开始日期: {row[2]}, 状态: {row[4]}')
        else:
            print('没有找到菜单记录')
    else:
        print('未找到城南小学')
