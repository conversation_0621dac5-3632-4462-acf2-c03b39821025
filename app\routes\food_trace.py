"""
食材溯源路由模块
提供食材溯源相关的路由
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify, abort
from flask_login import login_required, current_user
from app.models import (
    WeeklyMenu, WeeklyMenuRecipe, MenuPlan, MenuRecipe, Recipe, RecipeIngredient,
    ConsumptionPlan, ConsumptionDetail, StockOut, StockOutItem, Inventory,
    StockIn, StockInItem, PurchaseOrder, PurchaseOrderItem, Supplier, Ingredient,
    FoodSample, AdministrativeArea
)
from app.utils.decorators import check_permission
from app.utils.food_trace import trace_food_supply_chain
from datetime import datetime, date, timedelta
from app.services.daily_management_service import DailyLogService

food_trace_bp = Blueprint('food_trace', __name__)

@food_trace_bp.route('/food-trace')
@login_required
def index():
    """食材溯源首页，支持 consumption_plan_id 参数"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    trace_date = request.args.get('date')
    meal_type = request.args.get('meal_type')
    consumption_plan_id = request.args.get('consumption_plan_id', type=int)

    trace_data = None
    # 优先按consumption_plan_id查
    if consumption_plan_id:
        from app.models import ConsumptionPlan
        plan = ConsumptionPlan.query.get(consumption_plan_id)
        if plan:
            # 用消耗计划的日期、餐次、区域查
            area_id = plan.menu_plan.area_id if plan.menu_plan else area_id
            trace_date = plan.consumption_date.strftime('%Y-%m-%d') if plan.consumption_date else trace_date
            meal_type = plan.meal_type
            trace_result = trace_food_supply_chain(trace_date, meal_type, area_id)
            if trace_result['success']:
                trace_data = trace_result['data']
    else:
        # 默认为今天
        if not trace_date:
            trace_date = date.today().strftime('%Y-%m-%d')
        # 默认为午餐
        if not meal_type:
            meal_type = '午餐'
        # 如果没有指定区域，使用用户的第一个可访问区域
        if not area_id and accessible_areas:
            area_id = accessible_areas[0].id
        if area_id and trace_date and meal_type:
            trace_result = trace_food_supply_chain(trace_date, meal_type, area_id)
            if trace_result['success']:
                trace_data = trace_result['data']

    return render_template('food_trace/index.html',
                          areas=accessible_areas,
                          area_id=area_id,
                          trace_date=trace_date,
                          meal_type=meal_type,
                          trace_data=trace_data)

@food_trace_bp.route('/food-trace/get-recipes', methods=['GET'])
@login_required
def get_recipes():
    """
    获取指定区域、日期和餐次的菜谱列表。
    用于前端动态加载菜谱信息。
    """
    area_id = request.args.get('area_id', type=int)
    query_date_str = request.args.get('date')
    meal_type = request.args.get('meal_type')

    if not area_id or not query_date_str or not meal_type:
        return jsonify({'success': False, 'message': '缺少area_id, date或meal_type参数'}), 400

    try:
        query_date = datetime.strptime(query_date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'success': False, 'message': '日期格式不正确，应为YYYY-MM-DD'}), 400

    # 检查用户是否有权限访问该区域
    if not current_user.can_access_area_by_id(area_id):
         return jsonify({'success': False, 'message': '您没有权限访问该区域的数据'}), 403

    # 查找指定区域、日期和餐次的菜单计划
    menu_plan = MenuPlan.query.filter(
        MenuPlan.area_id == area_id,
        MenuPlan.plan_date == query_date,
        MenuPlan.meal_type == meal_type,
        MenuPlan.status.in_(['已发布', '已执行'])
    ).first()

    recipes_list = []
    if menu_plan:
        # 获取菜单计划关联的菜谱
        menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all()
        for menu_recipe in menu_recipes:
            if menu_recipe.recipe:
                recipes_list.append({
                    'id': menu_recipe.recipe.id,
                    'name': menu_recipe.recipe.name,
                    'notes': menu_recipe.recipe.notes,
                    # 可以添加其他需要的菜谱信息
                })
    # else:
        # 如果没有找到菜单计划，也可以选择从周菜单查找，但这会增加复杂性，暂不实现，只返回空列表

    return jsonify({'success': True, 'data': recipes_list})

@food_trace_bp.route('/food-trace/sample-management')
@login_required
def sample_management():
    """留样管理页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    trace_date = request.args.get('date')
    meal_type = request.args.get('meal_type')

    # 默认为今天
    if not trace_date:
        trace_date = date.today().strftime('%Y-%m-%d')

    # 默认为午餐
    if not meal_type:
        meal_type = '午餐'

    # 如果没有指定区域，使用用户的第一个可访问区域
    if not area_id and accessible_areas:
        area_id = accessible_areas[0].id

    return render_template('food_trace/sample_management.html',
                          areas=accessible_areas,
                          area_id=area_id,
                          today=date.today().strftime('%Y-%m-%d'))

@food_trace_bp.route('/food-trace/one-click-sample')
@login_required
def one_click_sample():
    """一键留样功能，自动创建今日工作日志并跳转到留样管理页面"""
    try:
        # 获取当前用户所属学校/区域
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法使用一键留样功能', 'danger')
            return redirect(url_for('main.index'))

        # 准备今日日志数据
        today_date = date.today()
        data = {
            'log_date': today_date.strftime('%Y-%m-%d'),
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'student_count': 0,
            'teacher_count': 0,
            'other_count': 0,
            'created_by': current_user.id
        }

        # 创建或获取今日工作日志
        log = DailyLogService.create_daily_log(data)

        if log:
            flash(f'已自动创建或获取{user_area.name}今日工作日志', 'info')
        else:
            flash('无法创建或获取今日工作日志', 'warning')

        # 重定向到留样管理页面
        return redirect(url_for('food_trace.sample_management', area_id=user_area.id))
    except Exception as e:
        flash(f'创建工作日志失败: {str(e)}', 'danger')
        return redirect(url_for('main.index'))

@food_trace_bp.route('/food-trace/print-samples')
@login_required
def print_samples():
    """打印留样记录"""
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    trace_date = request.args.get('date')
    meal_type = request.args.get('meal_type')
    recipe_ids = request.args.get('recipes')

    # 留样设置参数
    sample_quantity = request.args.get('quantity', 50, type=float)
    storage_hours = request.args.get('hours', 48, type=int)
    storage_location = request.args.get('location', '留样冰箱')
    storage_temp = request.args.get('temp', -18, type=float)

    # 默认为今天
    if not trace_date:
        trace_date = date.today().strftime('%Y-%m-%d')

    # 默认为午餐
    if not meal_type:
        meal_type = '午餐'

    # 获取区域信息
    area = None
    if area_id:
        area = AdministrativeArea.query.get(area_id)

        # 检查用户权限
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限查看该区域的数据', 'danger')
            return redirect(url_for('food_trace.index'))

    # 将字符串日期转换为日期对象
    sample_date = datetime.strptime(trace_date, '%Y-%m-%d').date()

    # 处理指定的菜谱ID
    selected_recipe_ids = []
    if recipe_ids:
        selected_recipe_ids = [int(id) for id in recipe_ids.split(',')]

    # 获取菜单信息
    menu_plan = None
    all_recipes = []

    menu_plan_query = MenuPlan.query.filter(
        MenuPlan.plan_date == sample_date,
        MenuPlan.meal_type == meal_type,
        MenuPlan.status.in_(['已发布', '已执行'])
    )

    if area_id:
        menu_plan_query = menu_plan_query.filter(MenuPlan.area_id == area_id)

    menu_plan = menu_plan_query.first()

    if menu_plan:
        menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all()
        for menu_recipe in menu_recipes:
            if menu_recipe.recipe:
                all_recipes.append(menu_recipe.recipe)
    else:
        # 如果没有找到菜单计划，尝试从周菜单中查找
        weekday = sample_date.weekday()  # 0-6，0表示周一

        weekly_menu_query = WeeklyMenu.query.filter(
            WeeklyMenu.week_start <= sample_date,
            WeeklyMenu.week_end >= sample_date,
            WeeklyMenu.status == '已发布'
        )

        if area_id:
            weekly_menu_query = weekly_menu_query.filter(WeeklyMenu.area_id == area_id)

        weekly_menu = weekly_menu_query.first()

        if weekly_menu:
            weekly_recipes = WeeklyMenuRecipe.query.filter(
                WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                WeeklyMenuRecipe.meal_type == meal_type
            ).all()

            for weekly_recipe in weekly_recipes:
                if weekly_recipe.recipe:
                    all_recipes.append(weekly_recipe.recipe)

    # 如果指定了菜谱ID，则只处理这些菜谱
    if selected_recipe_ids:
        recipes_to_print = [recipe for recipe in all_recipes if recipe.id in selected_recipe_ids]
    else:
        recipes_to_print = all_recipes

    # 获取留样记录
    food_samples = []
    if area_id:
        food_samples = FoodSample.query.filter(
            FoodSample.meal_date == sample_date,
            FoodSample.meal_type == meal_type,
            FoodSample.area_id == area_id
        ).all()

    # 将菜谱和留样记录关联起来，并获取主要食材信息
    recipes_with_samples = []
    for recipe in recipes_to_print:
        # 获取食谱的主要食材（最多3种）
        main_ingredients = []
        recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe.id).all()
        for ri in recipe_ingredients[:3]:  # 最多取前3种食材
            if ri.ingredient:
                main_ingredients.append(ri.ingredient.name)

        recipe_data = {
            'recipe': recipe,
            'sample': None,
            'main_ingredients': ', '.join(main_ingredients) if main_ingredients else '无食材信息'
        }

        # 查找对应的留样记录
        for sample in food_samples:
            if sample.recipe_id == recipe.id:
                recipe_data['sample'] = sample
                break

        recipes_with_samples.append(recipe_data)

    # 计算留样时间和销毁时间
    current_time = datetime.now()
    end_time = current_time + timedelta(hours=storage_hours)

    return render_template('food_trace/print_samples.html',
                          area=area,
                          trace_date=trace_date,
                          meal_type=meal_type,
                          menu_plan=menu_plan,
                          recipes_with_samples=recipes_with_samples,
                          current_time=current_time,
                          end_time=end_time,
                          sample_quantity=sample_quantity,
                          storage_location=storage_location,
                          storage_temp=storage_temp)

@food_trace_bp.route('/food-trace/view/<int:area_id>/<string:date_str>/<string:meal_type>')
@login_required
def view_trace(area_id, date_str, meal_type):
    """查看指定日期和餐次的溯源信息"""
    # 检查用户权限
    if not current_user.can_access_area_by_id(area_id):
        flash('您没有权限查看该区域的数据', 'danger')
        return redirect(url_for('food_trace.index'))

    # 获取区域信息
    area = AdministrativeArea.query.get_or_404(area_id)

    # 获取溯源数据
    trace_result = trace_food_supply_chain(date_str, meal_type, area_id)
    if not trace_result['success']:
        flash(trace_result['message'], 'danger')
        return redirect(url_for('food_trace.index'))

    trace_data = trace_result['data']

    return render_template('food_trace/view.html',
                          area=area,
                          trace_date=date_str,
                          meal_type=meal_type,
                          trace_data=trace_data)
