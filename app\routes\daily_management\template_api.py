"""
食堂日常检查模板API路由

提供食堂日常检查模板相关的API接口，用于前端AJAX调用。
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models_daily_management import InspectionTemplate
from app.utils.error_handler import api_error_response, db_operation_error_handler
from app.utils.school_required import school_required
from datetime import datetime
import traceback
import json

template_api_bp = Blueprint('template_api', __name__, url_prefix='/api/v2/inspection-templates')

@template_api_bp.route('/', methods=['GET'])
@login_required
@school_required
def get_templates(user_area=None):
    """获取检查模板列表"""
    try:
        # 获取查询参数
        category = request.args.get('category')
        is_default = request.args.get('is_default', type=bool)

        # 构建查询
        query = InspectionTemplate.query

        # 应用筛选条件
        if category:
            query = query.filter(InspectionTemplate.category == category)
        
        if is_default is not None:
            query = query.filter(InspectionTemplate.is_default == is_default)
        
        # 获取用户所属区域的模板和默认模板
        query = query.filter(
            (InspectionTemplate.area_id == user_area.id) | 
            (InspectionTemplate.is_default == True)
        )

        # 执行查询
        templates = query.all()

        # 构建响应数据
        result = []
        for template in templates:
            result.append({
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'category': template.category,
                'items': template.get_items(),
                'is_default': template.is_default,
                'created_by': template.created_by,
                'area_id': template.area_id,
                'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else None,
                'updated_at': template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else None
            })

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"获取检查模板列表失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'获取检查模板列表失败: {str(e)}', 500)

@template_api_bp.route('/<int:template_id>', methods=['GET'])
@login_required
@school_required
def get_template(template_id, user_area=None):
    """获取检查模板详情"""
    try:
        # 获取模板
        template = InspectionTemplate.query.get(template_id)
        if not template:
            return api_error_response('模板不存在', 404)

        # 验证用户是否有权限访问该模板
        if not template.is_default and template.area_id != user_area.id:
            return api_error_response('无权访问此模板', 403)

        # 构建响应数据
        result = {
            'id': template.id,
            'name': template.name,
            'description': template.description,
            'category': template.category,
            'items': template.get_items(),
            'is_default': template.is_default,
            'created_by': template.created_by,
            'area_id': template.area_id,
            'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else None,
            'updated_at': template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else None
        }

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"获取检查模板详情失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'获取检查模板详情失败: {str(e)}', 500)

@template_api_bp.route('/', methods=['POST'])
@login_required
@school_required
@db_operation_error_handler
def create_template(user_area=None):
    """创建检查模板"""
    try:
        # 获取请求数据
        data = request.json if request.is_json else request.form.to_dict()

        # 验证必要参数
        required_fields = ['name', 'category', 'items']
        for field in required_fields:
            if field not in data or not data[field]:
                return api_error_response(f'缺少必要参数: {field}', 400)

        # 创建模板
        template = InspectionTemplate(
            name=data['name'],
            description=data.get('description', ''),
            category=data['category'],
            is_default=data.get('is_default', False),
            created_by=current_user.id,
            area_id=user_area.id
        )

        # 设置检查项目
        items = data['items']
        if isinstance(items, str):
            try:
                items = json.loads(items)
            except:
                return api_error_response('检查项目格式无效', 400)
        
        template.set_items(items)

        # 如果设置为默认模板，取消其他同类别的默认模板
        if template.is_default:
            InspectionTemplate.query.filter(
                InspectionTemplate.category == template.category,
                InspectionTemplate.is_default == True,
                InspectionTemplate.id != template.id
            ).update({'is_default': False})

        # 保存模板
        db.session.add(template)
        db.session.commit()

        # 构建响应数据
        result = {
            'id': template.id,
            'name': template.name,
            'description': template.description,
            'category': template.category,
            'items': template.get_items(),
            'is_default': template.is_default,
            'created_by': template.created_by,
            'area_id': template.area_id,
            'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else None,
            'updated_at': template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else None
        }

        return jsonify(result), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建检查模板失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'创建检查模板失败: {str(e)}', 500)

@template_api_bp.route('/<int:template_id>', methods=['PUT'])
@login_required
@school_required
@db_operation_error_handler
def update_template(template_id, user_area=None):
    """更新检查模板"""
    try:
        # 获取模板
        template = InspectionTemplate.query.get(template_id)
        if not template:
            return api_error_response('模板不存在', 404)

        # 验证用户是否有权限更新该模板
        if template.is_default and not current_user.is_admin():
            return api_error_response('无权更新默认模板', 403)
        
        if not template.is_default and template.area_id != user_area.id:
            return api_error_response('无权更新此模板', 403)

        # 获取请求数据
        data = request.json if request.is_json else request.form.to_dict()

        # 更新模板
        if 'name' in data and data['name']:
            template.name = data['name']
        
        if 'description' in data:
            template.description = data['description']
        
        if 'category' in data and data['category']:
            template.category = data['category']
        
        if 'is_default' in data:
            template.is_default = data['is_default']
        
        if 'items' in data and data['items']:
            items = data['items']
            if isinstance(items, str):
                try:
                    items = json.loads(items)
                except:
                    return api_error_response('检查项目格式无效', 400)
            
            template.set_items(items)

        # 如果设置为默认模板，取消其他同类别的默认模板
        if template.is_default:
            InspectionTemplate.query.filter(
                InspectionTemplate.category == template.category,
                InspectionTemplate.is_default == True,
                InspectionTemplate.id != template.id
            ).update({'is_default': False})

        # 保存模板
        db.session.commit()

        # 构建响应数据
        result = {
            'id': template.id,
            'name': template.name,
            'description': template.description,
            'category': template.category,
            'items': template.get_items(),
            'is_default': template.is_default,
            'created_by': template.created_by,
            'area_id': template.area_id,
            'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else None,
            'updated_at': template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else None
        }

        return jsonify(result)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新检查模板失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'更新检查模板失败: {str(e)}', 500)

@template_api_bp.route('/<int:template_id>', methods=['DELETE'])
@login_required
@school_required
@db_operation_error_handler
def delete_template(template_id, user_area=None):
    """删除检查模板"""
    try:
        # 获取模板
        template = InspectionTemplate.query.get(template_id)
        if not template:
            return api_error_response('模板不存在', 404)

        # 验证用户是否有权限删除该模板
        if template.is_default and not current_user.is_admin():
            return api_error_response('无权删除默认模板', 403)
        
        if not template.is_default and template.area_id != user_area.id:
            return api_error_response('无权删除此模板', 403)

        # 删除模板
        db.session.delete(template)
        db.session.commit()

        return jsonify({'message': '模板删除成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除检查模板失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'删除检查模板失败: {str(e)}', 500)
