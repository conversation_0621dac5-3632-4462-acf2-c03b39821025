"""
权限管理工具
"""

# 系统权限定义
PERMISSIONS = {
    # 用户管理
    '用户管理': {
        'name': '用户管理',
        'actions': {
            'view': '查看用户',
            'create': '创建用户',
            'edit': '编辑用户',
            'delete': '删除用户',
            'change_status': '修改用户状态',
            'reset_password': '重置密码'
        }
    },
    # 采购管理
    '采购管理': {
        'name': '采购管理',
        'actions': {
            'view': '查看采购',
            'create': '创建采购',
            'edit': '编辑采购',
            'delete': '删除采购',
            'approve': '审核采购'
        }
    },
    # 采购订单管理
    '采购订单管理': {
        'name': '采购订单管理',
        'actions': {
            'view': '查看采购订单',
            'create': '创建采购订单',
            'edit': '编辑采购订单',
            'delete': '删除采购订单',
            'approve': '审核采购订单',
            'execute': '执行采购订单',
            'import': '导入采购订单'
        }
    },
    # 周菜单管理
    '周菜单管理': {
        'name': '周菜单管理',
        'actions': {
            'view': '查看周菜单',
            'create': '创建周菜单',
            'edit': '编辑周菜单',
            'delete': '删除周菜单',
            'approve': '审核周菜单',
            'publish': '发布周菜单'
        }
    },
    # 食堂日常管理
    '食堂日常管理': {
        'name': '食堂日常管理',
        'actions': {
            'view': '查看日常管理',
            'create': '创建日志记录',
            'edit': '编辑日志记录',
            'delete': '删除日志记录',
            'manage_inspection': '管理检查记录',
            'manage_companion': '管理陪餐记录',
            'manage_training': '管理培训记录',
            'manage_event': '管理特殊事件',
            'manage_issue': '管理问题记录'
        }
    },
    # 角色管理
    '角色管理': {
        'name': '角色管理',
        'actions': {
            'view': '查看角色',
            'create': '创建角色',
            'edit': '编辑角色',
            'delete': '删除角色'
        }
    },
    # 区域管理
    '区域管理': {
        'name': '区域管理',
        'actions': {
            'view': '查看区域',
            'create': '创建区域',
            'edit': '编辑区域',
            'delete': '删除区域'
        }
    },
    # 供应商管理
    '供应商管理': {
        'name': '供应商管理',
        'actions': {
            'view': '查看供应商',
            'create': '创建供应商',
            'edit': '编辑供应商',
            'delete': '删除供应商'
        }
    },
    # 食材管理
    '食材管理': {
        'name': '食材管理',
        'actions': {
            'view': '查看食材',
            'create': '创建食材',
            'edit': '编辑食材',
            'delete': '删除食材'
        }
    },
    # 食谱管理
    '食谱管理': {
        'name': '食谱管理',
        'actions': {
            'view': '查看食谱',
            'create': '创建食谱',
            'edit': '编辑食谱',
            'delete': '删除食谱',
            'approve': '审核食谱'
        }
    },
    # 菜单计划管理 (已弃用，由周菜单管理替代)
    '菜单计划管理': {
        'name': '菜单计划管理(已弃用)',
        'actions': {
            'view': '查看菜单计划',
            'create': '创建菜单计划',
            'edit': '编辑菜单计划',
            'delete': '删除菜单计划',
            'approve': '审核菜单计划',
            'execute': '执行菜单计划'
        }
    },
    # 留样管理 (已与溯源模块合并)
    '留样管理': {
        'name': '留样管理',
        'actions': {
            'view': '查看留样',
            'create': '创建留样',
            'edit': '编辑留样',
            'delete': '删除留样'
        }
    },
    # 食材溯源与留样
    '食材溯源与留样': {
        'name': '食材溯源与留样',
        'actions': {
            'view': '查看溯源信息',
            'create': '创建溯源记录',
            'edit': '编辑溯源记录',
            'delete': '删除溯源记录',
            'manage_sample': '管理留样'
        }
    },
    # 系统设置
    '系统设置': {
        'name': '系统设置',
        'actions': {
            'view': '查看设置',
            'edit': '修改设置',
            'super_delete': '超级删除',
            'project_name': '修改项目名称',
            'system_config': '系统配置'
        }
    },
    # 数据备份
    '数据备份': {
        'name': '数据备份',
        'actions': {
            'view': '查看备份',
            'create': '创建备份',
            'restore': '恢复备份',
            'delete': '删除备份',
            'download': '下载备份'
        }
    },
    # 系统监控
    '系统监控': {
        'name': '系统监控',
        'actions': {
            'view': '查看监控',
            'export': '导出监控数据'
        }
    },
    # 日志管理
    '日志管理': {
        'name': '日志管理',
        'actions': {
            'view': '查看日志',
            'export': '导出日志'
        }
    },
    # 报表管理
    '报表管理': {
        'name': '报表管理',
        'actions': {
            'view': '查看报表',
            'export': '导出报表',
            'print': '打印报表'
        }
    },
    # 数据修复
    '数据修复': {
        'name': '数据修复',
        'actions': {
            'view': '查看数据修复',
            'repair': '修复数据',
            'check': '检查数据',
            'tool': '使用修复工具'
        }
    },
    # 全局权限
    '*': {
        'name': '全局权限',
        'actions': {
            '*': '所有操作'
        }
    }
}

def get_all_permissions():
    """获取所有权限"""
    return PERMISSIONS

def get_permission_list():
    """获取权限列表，用于前端显示"""
    result = []

    # 尝试导入详细描述
    try:
        from app.utils.permission_descriptions import get_permission_description, get_module_description
        has_descriptions = 1
    except ImportError:
        has_descriptions = 0

    for module, module_info in PERMISSIONS.items():
        module_data = {
            'module': module,
            'name': module_info['name'],
            'actions': []
        }

        # 添加模块详细描述
        if has_descriptions:
            module_desc = get_module_description(module)
            if module_desc:
                module_data['description'] = module_desc.get('description', '')
                module_data['usage_scenarios'] = module_desc.get('usage_scenarios', '')

        for action, action_name in module_info['actions'].items():
            action_data = {
                'action': action,
                'name': action_name
            }

            # 添加操作详细描述
            if has_descriptions:
                action_desc = get_permission_description(module, action)
                if action_desc:
                    action_data['description'] = action_desc.get('description', '')
                    action_data['impact'] = action_desc.get('impact', '')
                    action_data['typical_roles'] = action_desc.get('typical_roles', [])

            module_data['actions'].append(action_data)

        result.append(module_data)
    return result

def format_permissions_json(permissions_dict):
    """将权限字典格式化为JSON字符串"""
    import json
    return json.dumps(permissions_dict, indent=2)

def parse_permissions_json(permissions_json):
    """将JSON字符串解析为权限字典"""
    import json
    try:
        return json.loads(permissions_json)
    except:
        return {}

def get_role_templates():
    """获取预定义角色模板"""
    try:
        from app.utils.permission_descriptions import get_all_role_templates
        return get_all_role_templates()
    except ImportError:
        # 如果没有导入成功，返回基本模板
        return {
            'system_admin': {
                'name': '系统管理员',
                'description': '拥有系统最高权限，可以执行所有操作',
                'permissions': {
                    '*': ['*']
                }
            },
            'super_admin': {
                'name': '超级管理员',
                'description': '拥有除系统设置外的所有权限',
                'permissions': {
                    '用户管理': ['view', 'create', 'edit', 'change_status', 'reset_password'],
                    '角色管理': ['view'],
                    '区域管理': ['view', 'create', 'edit'],
                    '供应商管理': ['*'],
                    '食材管理': ['*'],
                    '食谱管理': ['*'],
                    '菜单计划管理': ['*'],
                    '周菜单管理': ['*'],
                    '留样管理': ['*'],
                    '采购管理': ['*'],
                    '采购订单管理': ['*'],
                    '日志管理': ['view'],
                    '报表管理': ['*'],
                    '数据修复': ['*']
                }
            },
            'school_admin': {
                'name': '学校管理员',
                'description': '拥有学校级别的管理权限',
                'permissions': {
                    '用户管理': ['view'],
                    '区域管理': ['view'],
                    '供应商管理': ['view', 'create', 'edit'],
                    '食材管理': ['view', 'create', 'edit'],
                    '食谱管理': ['view', 'approve'],
                    '菜单计划管理': ['view', 'approve'],
                    '周菜单管理': ['view', 'approve', 'publish'],
                    '留样管理': ['view'],
                    '采购管理': ['view', 'approve'],
                    '采购订单管理': ['view', 'approve', 'import'],
                    '报表管理': ['view', 'export', 'print'],
                    '数据修复': ['view', 'check']
                }
            },
            'cafeteria_admin': {
                'name': '食堂管理员',
                'description': '拥有食堂级别的管理权限',
                'permissions': {
                    '供应商管理': ['view'],
                    '食材管理': ['view'],
                    '食谱管理': ['view', 'create', 'edit'],
                    '菜单计划管理': ['view', 'create', 'edit', 'execute'],
                    '周菜单管理': ['view', 'create', 'edit', 'publish'],
                    '留样管理': ['view', 'create', 'edit'],
                    '采购管理': ['view', 'create'],
                    '采购订单管理': ['view', 'create', 'edit', 'import'],
                    '报表管理': ['view', 'print'],
                    '数据修复': ['view']
                }
            }
        }

def has_permission(user_permissions, module, action):
    """检查用户是否有指定模块的指定操作权限"""
    # 如果用户有全局权限，直接返回True
    if '*' in user_permissions and '*' in user_permissions['*']:
        return 1

    # 如果用户有指定模块的所有权限，返回True
    if module in user_permissions and '*' in user_permissions[module]:
        return 1

    # 检查用户是否有指定模块的指定操作权限
    if module in user_permissions and action in user_permissions[module]:
        return 1

    return 0

# 权限装饰器
from functools import wraps
from flask import flash, redirect, url_for, request
from flask_login import current_user

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))

        if not current_user.is_admin():
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('main.index'))

        return f(*args, **kwargs)

    return decorated_function

def check_permission(module, action):
    """
    检查用户是否有指定模块和操作的权限
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login', next=request.url))

            # 系统管理员拥有所有权限
            if current_user.is_admin():
                return f(*args, **kwargs)

            # 检查用户是否有权限
            if not current_user.has_permission(module, action):
                flash('您没有权限执行此操作', 'danger')
                return redirect(url_for('main.index'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator
