-- 创建食材检验检疫记录表
CREATE TABLE ingredient_inspections (
    id INT IDENTITY(1,1) PRIMARY KEY,
    stock_in_item_id INT NOT NULL,
    inspector_id INT NOT NULL,
    inspection_date DATE NOT NULL DEFAULT GETDATE(),
    inspection_type NVARCHAR(50) NOT NULL,
    result NVARCHAR(20) NOT NULL,
    notes NVARCHAR(MAX) NULL,
    document_id INT NULL,
    created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_IngredientInspections_StockInItems FOREIGN KEY (stock_in_item_id) REFERENCES stock_in_items(id),
    CONSTRAINT FK_IngredientInspections_Users FOREIGN KEY (inspector_id) REFERENCES users(id),
    CONSTRAINT FK_IngredientInspections_Documents FOREIGN KEY (document_id) REFERENCES stock_in_documents(id)
);

-- 创建索引
CREATE INDEX IX_IngredientInspections_StockInItemId ON ingredient_inspections(stock_in_item_id);
CREATE INDEX IX_IngredientInspections_InspectorId ON ingredient_inspections(inspector_id);
CREATE INDEX IX_IngredientInspections_DocumentId ON ingredient_inspections(document_id);
