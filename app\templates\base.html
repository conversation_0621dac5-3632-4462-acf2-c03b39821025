<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂(Scmmp) ') }}{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/jquery-ui/css/jquery-ui.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap4.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/local-fonts.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-image-uploader.css') }}?v=1.0.0">
    {% block styles %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">{{ project_name|default('校园餐智慧食堂(Scmmp) ') }}</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mr-auto">

                    {% if current_user.is_authenticated %}
                        {% for menu_item in user_menu %}
                            {% if menu_item.children %}
                                <li class="nav-item dropdown">
                                    {% if menu_item.url %}
                                        <a class="nav-link dropdown-toggle" href="{{ url_for(menu_item.url) if not menu_item.get('url_params') else get_url(menu_item) }}" id="{{ menu_item.id }}Dropdown" role="button" data-toggle="dropdown">
                                            {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i> {% endif %}
                                            {{ menu_item.name }}
                                        </a>
                                    {% else %}
                                        <a class="nav-link dropdown-toggle" href="#" id="{{ menu_item.id }}Dropdown" role="button" data-toggle="dropdown">
                                            {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i> {% endif %}
                                            {{ menu_item.name }}
                                        </a>
                                    {% endif %}
                                    <div class="dropdown-menu">
                                        {% for child in menu_item.children %}
                                            {% if loop.index > 1 and loop.index0 is divisibleby 4 %}
                                                <div class="dropdown-divider"></div>
                                            {% endif %}
                                            <a class="dropdown-item" href="{{ url_for(child.url) if not child.get('url_params') else get_url(child) }}">
                                                {% if child.icon %}<i class="{{ child.icon }}"></i> {% endif %}
                                                {{ child.name }}
                                            </a>
                                        {% endfor %}
                                        {% if menu_item.id == 'area' and current_user.area %}
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item" href="{{ url_for('area.view_area', id=current_user.area_id) }}">
                                                当前区域: {{ current_user.area.name }}
                                            </a>
                                        {% endif %}
                                    </div>
                                </li>
                            {% else %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for(menu_item.url) if not menu_item.get('url_params') else get_url(menu_item) }}">
                                        {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i> {% endif %}
                                        {{ menu_item.name }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <!-- 通知图标和下拉菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link" href="#" id="notificationDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            {% if current_user.unread_notifications_count > 0 %}
                            <span class="badge badge-danger notification-badge" id="notification-badge">{{ current_user.unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right notification-dropdown" aria-labelledby="notificationDropdown">
                            <h6 class="dropdown-header">通知中心</h6>
                            <div id="notification-list">
                                {% if current_user.recent_notifications %}
                                    {% for notification in current_user.recent_notifications %}
                                    <a class="dropdown-item notification-item {% if not notification.is_read %}unread{% endif %}" href="{{ url_for('notification.view', id=notification.id) }}">
                                        <div class="notification-title">
                                            {% if notification.level == 2 %}
                                            <span class="badge badge-danger">紧急</span>
                                            {% elif notification.level == 1 %}
                                            <span class="badge badge-warning">重要</span>
                                            {% endif %}
                                            {{ notification.title }}
                                        </div>
                                        <div class="notification-content">{{  notification.content|truncate(50)  }}</div>
                                        <div class="notification-time">
                                            {% if notification.created_at is string %}
                                                {{ notification.created_at }}
                                            {% else %}
                                                {{ notification.created_at|format_datetime }}
                                            {% endif %}
                                        </div>
                                    </a>
                                    {% endfor %}
                                {% else %}
                                <div class="dropdown-item text-center">暂无通知</div>
                                {% endif %}
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="{{ url_for('notification.index') }}">查看全部通知</a>
                            {% if current_user.unread_notifications_count > 0 %}
                            <a class="dropdown-item text-center" href="{{ url_for('notification.mark_all_read') }}">全部标为已读</a>
                            {% endif %}
                        </div>
                    </li>
                    <!-- 用户下拉菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown">
                            {{ current_user.username }}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="#">个人资料</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">退出登录</a>
                        </div>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">注册</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">{{ project_name|default('校园餐智慧食堂(Scmmp) ') }} &copy; {{ now.year }}</span>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/moment/moment.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/moment/moment-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/jquery-ui/js/jquery-ui.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/jquery-ui/jquery-ui-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/datepicker-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/toastr/toastr.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/toastr/toastr-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/datatables/datatables-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/select2/select2-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/chart-js/chart-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/form-validation-zh-CN.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/i18n.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/mock-api-handler.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/auth-helper.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/enhanced-image-uploader.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/file-upload-fix.js') }}?v=1.0.0"></script>
    <script>
        // 配置toastr通知
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // 初始化本地化设置
        $(document).ready(function() {
            // 设置 moment.js 的语言
            moment.locale('zh-CN');

            // 设置 bootstrap-table 的默认选项
            $.extend($.fn.bootstrapTable.defaults, {
                locale: 'zh-CN',
                formatLoadingMessage: function() {
                    return '正在加载中...';
                }
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
