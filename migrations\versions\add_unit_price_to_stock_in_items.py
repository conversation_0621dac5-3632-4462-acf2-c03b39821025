"""添加单价字段到入库明细表

Revision ID: add_unit_price_001
Revises: 
Create Date: 2025-01-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_unit_price_001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """添加 unit_price 字段到 stock_in_items 表"""
    # 添加 unit_price 列
    op.add_column('stock_in_items', sa.Column('unit_price', sa.Float(), nullable=True))


def downgrade():
    """移除 unit_price 字段"""
    # 删除 unit_price 列
    op.drop_column('stock_in_items', 'unit_price')
