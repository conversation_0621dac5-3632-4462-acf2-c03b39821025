#!/usr/bin/env python3
"""
测试视图函数修复的脚本
"""

import requests
import sys
from datetime import datetime

def test_create_from_purchase_get():
    """测试从采购订单创建入库单的GET请求"""
    print("🧪 测试从采购订单创建入库单的GET请求...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试GET请求
        url = f"{base_url}/stock-in/create-from-purchase/40"
        
        print(f"📤 发送GET请求到: {url}")
        
        response = requests.get(url, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ GET请求成功，页面正常加载")
            
            # 检查页面内容
            if "从采购订单创建入库单" in response.text:
                print("✅ 页面标题正确")
                return True
            else:
                print("⚠️ 页面内容可能不正确")
                return False
                
        elif response.status_code == 302:
            print("⚠️ 重定向响应，可能需要登录")
            return True  # 重定向是正常的响应
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_create_from_purchase_post():
    """测试从采购订单创建入库单的POST请求"""
    print("\n🧪 测试从采购订单创建入库单的POST请求...")
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试数据
    test_data = {
        'stock_in_date': datetime.now().strftime('%Y-%m-%d'),
        'notes': '测试POST请求 - 视图函数修复'
    }
    
    try:
        # 测试POST请求
        url = f"{base_url}/stock-in/create-from-purchase/40"
        
        print(f"📤 发送POST请求到: {url}")
        print(f"📋 请求数据: {test_data}")
        
        # 模拟表单提交
        response = requests.post(url, data=test_data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ POST请求成功")
            return True
        elif response.status_code == 302:
            print("✅ POST请求成功，收到重定向响应")
            print(f"📍 重定向位置: {response.headers.get('Location', '未知')}")
            return True
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_create_from_purchase_order_get():
    """测试新版本的从采购订单创建入库单的GET请求"""
    print("\n🧪 测试新版本的从采购订单创建入库单的GET请求...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试GET请求
        url = f"{base_url}/stock-in/create-from-purchase-order/40"
        
        print(f"📤 发送GET请求到: {url}")
        
        response = requests.get(url, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ GET请求成功，页面正常加载")
            return True
        elif response.status_code == 302:
            print("⚠️ 重定向响应，可能需要登录或权限检查")
            return True
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def main():
    """主测试函数"""
    print("🚀 开始视图函数修复测试\n")
    
    # 检查服务器连接
    if not test_server_connection():
        return False
    
    # 运行测试
    tests = [
        ("旧版本GET请求测试", test_create_from_purchase_get),
        ("旧版本POST请求测试", test_create_from_purchase_post),
        ("新版本GET请求测试", test_create_from_purchase_order_get),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print(f"\n{'='*50}")
    print("📊 测试结果摘要")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！视图函数问题已修复！")
        print("\n💡 修复内容:")
        print("1. ✅ 修复了 create_from_purchase 函数缺少返回语句的问题")
        print("2. ✅ 添加了完整的POST请求处理逻辑")
        print("3. ✅ 添加了GET请求的模板渲染")
        print("4. ✅ 使用原始SQL避免datetime精度问题")
        return True
    else:
        print("⚠️ 部分测试失败，可能仍存在问题")
        print("\n💡 如果仍有问题，请检查:")
        print("1. 视图函数是否有完整的返回语句")
        print("2. 模板文件是否存在")
        print("3. 路由配置是否正确")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
