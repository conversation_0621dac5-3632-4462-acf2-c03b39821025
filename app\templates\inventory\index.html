{% extends 'base.html' %}

{% block title %}库存管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 库存管理流程引导 -->
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-boxes"></i> 库存管理 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle" style="font-size: 1.2rem; margin-right: 10px;"></i> <strong>提示：</strong> 库存管理是食堂管理的核心环节，及时了解库存情况可以合理安排采购和消耗计划，避免食材浪费。
            </div>

            <div class="d-flex justify-content-between mt-3" style="margin-top: 10px;">
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-sign-out-alt"></i> 食材出库</p>
                    <small>已完成食材出库</small>
                    <div class="mt-2">
                        <a href="{{ url_for('stock_out.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回出库管理
                        </a>
                    </div>
                </div>
                <div style="flex: 1; padding: 0 10px; border-left: 1px solid #dee2e6; border-right: 1px solid #dee2e6;" class="bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="font-weight-bold"><i class="fas fa-boxes"></i> 库存管理</p>
                    <small>查看和管理库存</small>
                </div>
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-calendar-alt"></i> 周菜单计划</p>
                    <small>开始新一轮的菜单计划</small>
                    <div class="mt-2">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往周菜单计划
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>定期检查库存情况，特别是临期和过期食材</li>
                    <li>根据库存情况调整采购计划，避免重复采购或库存不足</li>
                    <li>可以使用详细视图查看每批次食材的详细信息，或使用汇总视图查看总体库存情况</li>
                    <li>对于临期食材，应优先安排在菜单中使用</li>
                    <li>库存管理与周菜单计划相互配合，形成完整的食堂管理闭环</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">库存列表</h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <a href="{{ url_for('inventory.index', view_type='detail') }}" class="btn btn-sm {% if view_type == 'detail' %}btn-primary{% else %}btn-default{% endif %}">
                                <i class="fas fa-list"></i> 详细视图
                            </a>
                            <a href="{{ url_for('inventory.index', view_type='summary') }}" class="btn btn-sm {% if view_type == 'summary' %}btn-primary{% else %}btn-default{% endif %}">
                                <i class="fas fa-chart-pie"></i> 汇总视图
                            </a>
                        </div>
                        <a href="{{ url_for('inventory.check_expiry') }}" class="btn btn-warning btn-sm ml-2">
                            <i class="fas fa-exclamation-triangle"></i> 检查临期/过期
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('inventory.index') }}" class="mb-4">
                        <input type="hidden" name="view_type" value="{{ view_type }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>仓库</label>
                                    <select name="warehouse_id" class="form-control" id="warehouse_id" onchange="loadStorageLocations()">
                                        <option value="">全部</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>存储位置</label>
                                    <select name="storage_location_id" class="form-control" id="storage_location_id">
                                        <option value="">全部</option>
                                        {% for location in storage_locations %}
                                        <option value="{{ location.id }}" {% if storage_location_id == location.id %}selected{% endif %}>{{ location.name }} ({{ location.location_code }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>食材</label>
                                    <select name="ingredient_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for ingredient in ingredients %}
                                        <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>{{ ingredient.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>状态</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部</option>
                                        <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                                        <option value="待检" {% if status == '待检' %}selected{% endif %}>待检</option>
                                        <option value="冻结" {% if status == '冻结' %}selected{% endif %}>冻结</option>
                                        <option value="已过期" {% if status == '已过期' %}selected{% endif %}>已过期</option>
                                        <option value="已用完" {% if status == '已用完' %}selected{% endif %}>已用完</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>临期天数</label>
                                    <select name="expiry_days" class="form-control">
                                        <option value="">全部</option>
                                        <option value="7" {% if expiry_days == 7 %}selected{% endif %}>7天内过期</option>
                                        <option value="15" {% if expiry_days == 15 %}selected{% endif %}>15天内过期</option>
                                        <option value="30" {% if expiry_days == 30 %}selected{% endif %}>30天内过期</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 库存列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>食材名称</th>
                                    <th>仓库</th>
                                    <th>存储位置</th>
                                    <th>批次号</th>
                                    <th>数量</th>
                                    <th>单位</th>
                                    <th>生产日期</th>
                                    <th>过期日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inventory in inventories %}
                                <tr>
                                    <td>{{ inventory.ingredient.name }}</td>
                                    <td>{{ inventory.warehouse.name }}</td>
                                    <td>{{ inventory.storage_location.name }} ({{ inventory.storage_location.location_code }})</td>
                                    <td>{{ inventory.batch_number }}</td>
                                    <td>{{ inventory.quantity }}</td>
                                    <td>{{ inventory.unit }}</td>
                                    <td>{{  inventory.production_date|format_datetime('%Y-%m-%d')  }}</td>
                                    <td>
                                        {{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}

                                        {% if inventory.status == '已过期' %}
                                            <span class="badge badge-danger">已过期</span>
                                        {% elif inventory.status == '临期' %}
                                            <span class="badge badge-warning">临期</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inventory.status == '正常' %}
                                        <span class="badge badge-success">正常</span>
                                        {% elif inventory.status == '待检' %}
                                        <span class="badge badge-warning">待检</span>
                                        {% elif inventory.status == '冻结' %}
                                        <span class="badge badge-info">冻结</span>
                                        {% elif inventory.status == '已过期' %}
                                        <span class="badge badge-danger">已过期</span>
                                        {% elif inventory.status == '已用完' %}
                                        <span class="badge badge-secondary">已用完</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('inventory.detail', id=inventory.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="10" class="text-center">暂无库存数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.prev_num, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) if pagination.has_prev else '#' }}">上一页</a>
                            </li>

                            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inventory.index', page=page_num, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.next_num, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) if pagination.has_next else '#' }}">下一页</a>
                            </li>
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 加载存储位置
    function loadStorageLocations() {
        const warehouseId = document.getElementById('warehouse_id').value;
        const storageLocationSelect = document.getElementById('storage_location_id');

        // 清空现有选项
        storageLocationSelect.innerHTML = '<option value="">全部</option>';

        if (!warehouseId) return;

        // 发送AJAX请求获取存储位置
        fetch(`{{ url_for('inventory.get_storage_locations') }}?warehouse_id=${warehouseId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    storageLocationSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading storage locations:', error));
    }
</script>
{% endblock %}
