#!/usr/bin/env python3
"""
测试单价保存修复的脚本
"""

import requests
import sys
from bs4 import BeautifulSoup

def test_unit_price_fix():
    """测试单价保存修复"""
    print("🧪 测试单价保存修复...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76  # 测试用的入库单ID
    
    try:
        editor_url = f"{base_url}/stock-in/{stock_in_id}/batch-editor-simplified"
        print(f"📤 访问批次编辑器页面: {editor_url}")
        
        response = requests.get(editor_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 批次编辑器页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找表单
            form = soup.find('form', {'id': 'batchEditForm'})
            if not form:
                print("❌ 未找到批次编辑表单")
                return False
            
            print("✅ 找到批次编辑表单")
            
            # 查找复选框
            checkboxes = form.find_all('input', {'class': 'batch-checkbox'})
            print(f"📋 找到 {len(checkboxes)} 个复选框")
            
            if len(checkboxes) == 0:
                print("⚠️ 没有找到可编辑的批次项目")
                return True
            
            # 检查第一个项目的字段
            first_checkbox = checkboxes[0]
            item_id = first_checkbox.get('value')
            print(f"📋 检查项目ID: {item_id}")
            
            # 查找对应的单价输入框
            unit_price_input = form.find('input', {'name': f'unit_price_{item_id}'})
            if unit_price_input:
                value = unit_price_input.get('value', '')
                required = unit_price_input.get('required', False)
                print(f"✅ 找到单价输入框: name=unit_price_{item_id}, value={value}, required={required}")
                
                if not value or value == '0' or value == '0.0':
                    print(f"⚠️ 单价值为空或为0，这可能是问题所在")
                else:
                    print(f"✅ 单价值正常: {value}")
            else:
                print(f"❌ 未找到单价输入框: unit_price_{item_id}")
                return False
            
            # 检查JavaScript代码
            scripts = soup.find_all('script')
            has_fix_code = False
            for script in scripts:
                if script.string and 'disabledFields' in script.string:
                    has_fix_code = True
                    print("✅ 找到修复代码：禁用字段处理逻辑")
                    break
            
            if not has_fix_code:
                print("⚠️ 未找到修复代码，可能需要检查JavaScript")
            
            return True
            
        elif response.status_code == 302:
            print("⚠️ 重定向，可能需要登录")
            return False
        else:
            print(f"❌ 状态码: {response.status_code}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_javascript_fix():
    """检查JavaScript修复代码"""
    print("\n🔍 检查JavaScript修复代码...")
    
    try:
        with open('app/templates/stock_in/batch_editor_simplified_scripts.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复代码
        fixes_found = []
        
        if 'disabledFields = []' in content:
            fixes_found.append("✅ 禁用字段数组初始化")
        
        if 'disabledFields.push' in content:
            fixes_found.append("✅ 禁用字段收集逻辑")
        
        if 'prop(\'disabled\', false)' in content:
            fixes_found.append("✅ 临时启用字段逻辑")
        
        if 'disabledFields.forEach' in content:
            fixes_found.append("✅ 恢复禁用状态逻辑")
        
        if 'console.log(\'提交前检查' in content:
            fixes_found.append("✅ 调试日志代码")
        
        print(f"📋 修复代码检查结果:")
        for fix in fixes_found:
            print(f"  {fix}")
        
        if len(fixes_found) >= 4:
            print("✅ JavaScript修复代码完整")
            return True
        else:
            print("⚠️ JavaScript修复代码不完整")
            return False
            
    except FileNotFoundError:
        print("❌ 未找到JavaScript文件")
        return False
    except Exception as e:
        print(f"❌ 检查JavaScript文件时出错: {e}")
        return False

def check_backend_fix():
    """检查后端修复代码"""
    print("\n🔍 检查后端修复代码...")
    
    try:
        with open('app/routes/stock_in.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复代码
        fixes_found = []
        
        if 'current_app.logger.info(f"单价字段' in content:
            fixes_found.append("✅ 单价字段调试日志")
        
        if 'current_app.logger.info(f"项目 {item_id} 详细信息:' in content:
            fixes_found.append("✅ 项目详细信息日志")
        
        if 'unit_price = request.form.get(f\'unit_price_{item_id}\')' in content:
            fixes_found.append("✅ 单价字段获取逻辑")
        
        if 'unit_price = :unit_price' in content:
            fixes_found.append("✅ SQL更新语句包含单价")
        
        print(f"📋 后端修复代码检查结果:")
        for fix in fixes_found:
            print(f"  {fix}")
        
        if len(fixes_found) >= 3:
            print("✅ 后端修复代码完整")
            return True
        else:
            print("⚠️ 后端修复代码不完整")
            return False
            
    except FileNotFoundError:
        print("❌ 未找到后端路由文件")
        return False
    except Exception as e:
        print(f"❌ 检查后端文件时出错: {e}")
        return False

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code in [200, 302]:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def provide_testing_instructions():
    """提供测试说明"""
    print("\n📋 手动测试说明:")
    print("="*60)
    
    print("\n1. 🌐 打开浏览器访问批次编辑器:")
    print("   http://127.0.0.1:5000/stock-in/76/batch-editor-simplified")
    
    print("\n2. 🔧 打开浏览器开发者工具 (F12):")
    print("   - 切换到 Console 标签页")
    print("   - 查看控制台输出")
    
    print("\n3. ✅ 勾选一个或多个批次项目:")
    print("   - 确保复选框被选中")
    print("   - 输入框应该变为可编辑状态")
    
    print("\n4. 💰 修改单价:")
    print("   - 在单价输入框中输入一个新的数值")
    print("   - 例如：19999.0")
    
    print("\n5. 💾 点击保存按钮:")
    print("   - 观察控制台输出的调试信息")
    print("   - 查看是否有 '提交前检查' 和 '启用禁用字段' 的日志")
    
    print("\n6. 🔍 检查保存结果:")
    print("   - 保存成功后，页面会跳转到详情页面")
    print("   - 在详情页面查看单价是否已更新")
    
    print("\n7. 📊 查看服务器日志:")
    print("   - 在运行Flask应用的终端中查看日志输出")
    print("   - 应该能看到 '单价字段' 和 '项目详细信息' 的日志")
    
    print("\n💡 预期结果:")
    print("   ✅ 单价值应该成功保存到数据库")
    print("   ✅ 详情页面应该显示更新后的单价")
    print("   ✅ 控制台和服务器日志应该显示正确的调试信息")

def main():
    """主函数"""
    print("🚀 开始测试单价保存修复\n")
    
    # 检查服务器连接
    if not test_server_connection():
        print("\n❌ 无法连接到服务器，请先启动Flask应用")
        return False
    
    print()
    
    # 运行测试步骤
    steps = [
        ("检查JavaScript修复代码", check_javascript_fix),
        ("检查后端修复代码", check_backend_fix),
        ("测试批次编辑器页面", test_unit_price_fix),
    ]
    
    results = []
    for step_name, step_func in steps:
        print(f"\n{'='*60}")
        print(f"🔍 {step_name}")
        print('='*60)
        
        try:
            result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"❌ 步骤异常: {e}")
            results.append((step_name, False))
    
    # 输出测试结果摘要
    print(f"\n{'='*60}")
    print("📊 测试结果摘要")
    print('='*60)
    
    passed = 0
    total = len(results)
    
    for step_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{step_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！单价保存修复应该已经生效。")
    else:
        print("\n⚠️ 部分测试失败，可能仍存在问题。")
    
    # 提供手动测试说明
    provide_testing_instructions()
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
