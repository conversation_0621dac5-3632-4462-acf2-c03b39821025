"""
学校关联装饰器

提供用于限制路由只能访问用户所属学校数据的装饰器函数。
"""

from functools import wraps
from flask import flash, redirect, url_for, request, abort
from flask_login import current_user

def school_required(f):
    """
    装饰器：确保用户有关联的学校，否则重定向到首页
    
    用法示例：
    @app.route('/some_route')
    @login_required
    @school_required
    def some_route():
        # 这里的代码只有当用户有关联学校时才会执行
        ...
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查用户是否有关联的学校
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法访问此功能', 'danger')
            return redirect(url_for('main.index'))
        
        # 将用户所属学校添加到kwargs中，方便视图函数使用
        kwargs['user_area'] = user_area
        
        return f(*args, **kwargs)
    return decorated_function

def school_data_required(model_class, id_param='id', area_field='area_id'):
    """
    装饰器：确保请求的数据属于用户所属学校
    
    Args:
        model_class: 数据模型类
        id_param: URL参数中的ID参数名
        area_field: 模型中表示学校/区域的字段名
    
    用法示例：
    @app.route('/view_data/<int:id>')
    @login_required
    @school_data_required(SomeModel)
    def view_data(id):
        # 这里的代码只有当数据属于用户所属学校时才会执行
        ...
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查用户是否有关联的学校
            user_area = current_user.get_current_area()
            if not user_area:
                flash('您没有关联到任何学校，无法访问此功能', 'danger')
                return redirect(url_for('main.index'))
            
            # 获取ID参数
            if id_param in kwargs:
                record_id = kwargs[id_param]
                
                # 查询记录并检查是否属于用户所属学校
                record = model_class.query.get_or_404(record_id)
                
                # 检查记录是否属于用户所属学校
                record_area_id = getattr(record, area_field, None)
                if record_area_id != user_area.id and not current_user.is_admin():
                    flash('您没有权限访问此数据', 'danger')
                    return redirect(url_for('main.index'))
                
                # 将记录添加到kwargs中，方便视图函数使用
                kwargs['record'] = record
            
            # 将用户所属学校添加到kwargs中，方便视图函数使用
            kwargs['user_area'] = user_area
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def filter_query_by_school(query, model_class, area_field='area_id'):
    """
    辅助函数：根据用户所属学校筛选查询结果
    
    Args:
        query: SQLAlchemy查询对象
        model_class: 数据模型类
        area_field: 模型中表示学校/区域的字段名
    
    Returns:
        筛选后的查询对象
    
    用法示例：
    query = SomeModel.query
    query = filter_query_by_school(query, SomeModel)
    results = query.all()
    """
    # 检查用户是否有关联的学校
    user_area = current_user.get_current_area()
    if not user_area and not current_user.is_admin():
        # 如果用户没有关联学校且不是管理员，返回空查询
        return query.filter(False)
    
    # 如果用户是管理员，不进行筛选
    if current_user.is_admin():
        return query
    
    # 获取字段对象
    area_field_obj = getattr(model_class, area_field, None)
    if area_field_obj is None:
        # 如果模型没有区域字段，返回原查询
        return query
    
    # 根据用户所属学校筛选查询结果
    return query.filter(area_field_obj == user_area.id)
