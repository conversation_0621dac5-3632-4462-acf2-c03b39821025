"""
食堂日常管理模块API接口 V2

提供食堂日常管理模块的RESTful API接口，用于前端和仪表盘调用。
"""

from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from app.routes.daily_management import daily_management_bp
from app.services import (
    DailyLogService, InspectionService, DiningCompanionService as CompanionService,
    TrainingService, EventService, IssueService, PhotoService, DashboardService
)
from datetime import datetime, date

# API路由前缀
API_PREFIX = '/api/v2'

# 日志API
@daily_management_bp.route(f'{API_PREFIX}/daily-logs', methods=['GET'])
@login_required
def api_v2_get_daily_logs():
    """获取日志列表"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    area_id = request.args.get('area_id', type=int)

    logs = DailyLogService.get_daily_logs(start_date, end_date, area_id)
    return jsonify([log.to_dict() for log in logs])

@daily_management_bp.route(f'{API_PREFIX}/daily-logs/<int:log_id>', methods=['GET'])
@login_required
def api_v2_get_daily_log(log_id):
    """获取日志详情"""
    log = DailyLogService.get_daily_log_by_id(log_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404
    return jsonify(log.to_dict())

@daily_management_bp.route(f'{API_PREFIX}/daily-logs/by-date/<string:date_str>', methods=['GET'])
@login_required
def api_v2_get_daily_log_by_date(date_str):
    """根据日期获取日志"""
    try:
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': '日期格式无效'}), 400

    area_id = request.args.get('area_id', type=int)
    log = DailyLogService.get_daily_log_by_date(log_date, area_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404
    return jsonify(log.to_dict())

@daily_management_bp.route(f'{API_PREFIX}/daily-logs', methods=['POST'])
@login_required
def api_create_daily_log():
    """创建日志"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 添加创建者信息
    data['created_by'] = current_user.id

    log = DailyLogService.create_daily_log(data)
    return jsonify(log.to_dict()), 201

@daily_management_bp.route(f'{API_PREFIX}/daily-logs/<int:log_id>', methods=['PUT'])
@login_required
def api_update_daily_log(log_id):
    """更新日志"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    log = DailyLogService.update_daily_log(log_id, data)
    if not log:
        return jsonify({'error': '日志不存在'}), 404
    return jsonify(log.to_dict())

@daily_management_bp.route(f'{API_PREFIX}/daily-logs/<int:log_id>', methods=['DELETE'])
@login_required
def api_delete_daily_log(log_id):
    """删除日志"""
    result = DailyLogService.delete_daily_log(log_id)
    if not result:
        return jsonify({'error': '日志不存在'}), 404
    return jsonify({'message': '日志已删除'})

# 检查记录API
@daily_management_bp.route(f'{API_PREFIX}/daily-logs/<int:log_id>/inspections', methods=['GET'])
@login_required
def api_v2_get_inspections(log_id):
    """获取检查记录列表"""
    inspections = InspectionService.get_inspections_by_daily_log(log_id)
    return jsonify([inspection.to_dict() for inspection in inspections])

@daily_management_bp.route(f'{API_PREFIX}/inspections/<int:inspection_id>', methods=['GET'])
@login_required
def api_v2_get_inspection(inspection_id):
    """获取检查记录详情"""
    inspection = InspectionService.get_inspection_by_id(inspection_id)
    if not inspection:
        return jsonify({'error': '检查记录不存在'}), 404
    return jsonify(inspection.to_dict())

@daily_management_bp.route(f'{API_PREFIX}/daily-logs/<int:log_id>/inspections', methods=['POST'])
@login_required
def api_v2_create_inspection(log_id):
    """创建检查记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 添加日志ID和检查人信息
    data['daily_log_id'] = log_id
    data['inspector_id'] = current_user.id

    inspection = InspectionService.create_inspection(data)
    return jsonify(inspection.to_dict()), 201

@daily_management_bp.route(f'{API_PREFIX}/add-inspection', methods=['POST'])
@login_required
def api_v2_add_inspection():
    """添加检查记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    log_id = data.get('log_id')
    time_of_day = data.get('time_of_day')
    inspection_item = data.get('inspection_item')
    status = data.get('status', 'normal')
    description = data.get('description', '')

    if not all([log_id, time_of_day, inspection_item]):
        return jsonify({'error': '缺少必要参数'}), 400

    if time_of_day not in ['morning', 'noon', 'evening']:
        return jsonify({'error': '无效的时间段'}), 400

    # 获取日志
    log = DailyLogService.get_daily_log_by_id(log_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404

    # 检查用户是否有权限操作该日志
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该日志'}), 403

    # 创建检查记录
    inspection_data = {
        'daily_log_id': log_id,
        'inspection_type': time_of_day,
        'inspection_item': inspection_item,
        'status': status,
        'description': description,
        'inspector_id': current_user.id,
        'inspection_time': datetime.now()
    }

    inspection = InspectionService.create_inspection(inspection_data)
    if not inspection:
        return jsonify({'error': '创建检查记录失败'}), 500

    return jsonify({
        'message': '检查记录已创建',
        'inspection': inspection.to_dict()
    }), 201

# 陪餐记录API
@daily_management_bp.route(f'{API_PREFIX}/companions', methods=['POST'])
@login_required
def api_v2_create_companion():
    """创建陪餐记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 验证必填字段
    required_fields = ['daily_log_id', 'companion_name', 'dining_time', 'dining_location']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 获取日志
    log_id = data.get('daily_log_id')
    log = DailyLogService.get_daily_log_by_id(log_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404

    # 检查用户是否有权限操作该日志
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该日志'}), 403

    # 添加创建者信息
    data['creator_id'] = current_user.id

    # 创建陪餐记录
    companion = CompanionService.create_companion(data)
    if not companion:
        return jsonify({'error': '创建陪餐记录失败'}), 500

    return jsonify({
        'message': '陪餐记录已创建',
        'companion': companion.to_dict()
    }), 201

@daily_management_bp.route(f'{API_PREFIX}/companions/<int:companion_id>', methods=['GET'])
@login_required
def api_v2_get_companion(companion_id):
    """获取陪餐记录"""
    companion = CompanionService.get_companion_by_id(companion_id)
    if not companion:
        return jsonify({'error': '陪餐记录不存在'}), 404

    # 检查用户是否有权限查看该陪餐记录
    log = DailyLogService.get_daily_log_by_id(companion.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限查看该陪餐记录'}), 403

    return jsonify({
        'data': companion.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/companions/<int:companion_id>', methods=['PUT'])
@login_required
def api_v2_update_companion(companion_id):
    """更新陪餐记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 获取陪餐记录
    companion = CompanionService.get_companion_by_id(companion_id)
    if not companion:
        return jsonify({'error': '陪餐记录不存在'}), 404

    # 检查用户是否有权限操作该陪餐记录
    log = DailyLogService.get_daily_log_by_id(companion.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该陪餐记录'}), 403

    # 更新陪餐记录
    updated_companion = CompanionService.update_companion(companion_id, data)
    if not updated_companion:
        return jsonify({'error': '更新陪餐记录失败'}), 500

    return jsonify({
        'message': '陪餐记录已更新',
        'companion': updated_companion.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/companions/<int:companion_id>', methods=['DELETE'])
@login_required
def api_v2_delete_companion(companion_id):
    """删除陪餐记录"""
    # 获取陪餐记录
    companion = CompanionService.get_companion_by_id(companion_id)
    if not companion:
        return jsonify({'error': '陪餐记录不存在'}), 404

    # 检查用户是否有权限操作该陪餐记录
    log = DailyLogService.get_daily_log_by_id(companion.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该陪餐记录'}), 403

    # 删除陪餐记录
    result = CompanionService.delete_companion(companion_id)
    if not result:
        return jsonify({'error': '删除陪餐记录失败'}), 500

    return jsonify({
        'message': '陪餐记录已删除'
    })

# 培训记录API
@daily_management_bp.route(f'{API_PREFIX}/trainings', methods=['POST'])
@login_required
def api_v2_create_training():
    """创建培训记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 验证必填字段
    required_fields = ['daily_log_id', 'training_topic', 'training_time', 'training_location', 'trainer']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 获取日志
    log_id = data.get('daily_log_id')
    log = DailyLogService.get_daily_log_by_id(log_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404

    # 检查用户是否有权限操作该日志
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该日志'}), 403

    # 添加创建者信息
    data['creator_id'] = current_user.id

    # 创建培训记录
    training = TrainingService.create_training(data)
    if not training:
        return jsonify({'error': '创建培训记录失败'}), 500

    return jsonify({
        'message': '培训记录已创建',
        'training': training.to_dict()
    }), 201

@daily_management_bp.route(f'{API_PREFIX}/trainings/<int:training_id>', methods=['GET'])
@login_required
def api_v2_get_training(training_id):
    """获取培训记录"""
    training = TrainingService.get_training_by_id(training_id)
    if not training:
        return jsonify({'error': '培训记录不存在'}), 404

    # 检查用户是否有权限查看该培训记录
    log = DailyLogService.get_daily_log_by_id(training.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限查看该培训记录'}), 403

    return jsonify({
        'data': training.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/trainings/<int:training_id>', methods=['PUT'])
@login_required
def api_v2_update_training(training_id):
    """更新培训记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 获取培训记录
    training = TrainingService.get_training_by_id(training_id)
    if not training:
        return jsonify({'error': '培训记录不存在'}), 404

    # 检查用户是否有权限操作该培训记录
    log = DailyLogService.get_daily_log_by_id(training.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该培训记录'}), 403

    # 更新培训记录
    updated_training = TrainingService.update_training(training_id, data)
    if not updated_training:
        return jsonify({'error': '更新培训记录失败'}), 500

    return jsonify({
        'message': '培训记录已更新',
        'training': updated_training.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/trainings/<int:training_id>', methods=['DELETE'])
@login_required
def api_v2_delete_training(training_id):
    """删除培训记录"""
    # 获取培训记录
    training = TrainingService.get_training_by_id(training_id)
    if not training:
        return jsonify({'error': '培训记录不存在'}), 404

    # 检查用户是否有权限操作该培训记录
    log = DailyLogService.get_daily_log_by_id(training.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该培训记录'}), 403

    # 删除培训记录
    result = TrainingService.delete_training(training_id)
    if not result:
        return jsonify({'error': '删除培训记录失败'}), 500

    return jsonify({
        'message': '培训记录已删除'
    })

# 特殊事件API
@daily_management_bp.route(f'{API_PREFIX}/events', methods=['POST'])
@login_required
def api_v2_create_event():
    """创建特殊事件"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 验证必填字段
    required_fields = ['daily_log_id', 'event_type', 'event_time', 'event_location', 'event_description']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 获取日志
    log_id = data.get('daily_log_id')
    log = DailyLogService.get_daily_log_by_id(log_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404

    # 检查用户是否有权限操作该日志
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该日志'}), 403

    # 添加创建者信息
    data['creator_id'] = current_user.id

    # 创建特殊事件
    event = EventService.create_event(data)
    if not event:
        return jsonify({'error': '创建特殊事件失败'}), 500

    return jsonify({
        'message': '特殊事件已创建',
        'event': event.to_dict()
    }), 201

@daily_management_bp.route(f'{API_PREFIX}/events/<int:event_id>', methods=['GET'])
@login_required
def api_v2_get_event(event_id):
    """获取特殊事件"""
    event = EventService.get_event_by_id(event_id)
    if not event:
        return jsonify({'error': '特殊事件不存在'}), 404

    # 检查用户是否有权限查看该特殊事件
    log = DailyLogService.get_daily_log_by_id(event.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限查看该特殊事件'}), 403

    return jsonify({
        'data': event.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/events/<int:event_id>', methods=['PUT'])
@login_required
def api_v2_update_event(event_id):
    """更新特殊事件"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 获取特殊事件
    event = EventService.get_event_by_id(event_id)
    if not event:
        return jsonify({'error': '特殊事件不存在'}), 404

    # 检查用户是否有权限操作该特殊事件
    log = DailyLogService.get_daily_log_by_id(event.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该特殊事件'}), 403

    # 更新特殊事件
    updated_event = EventService.update_event(event_id, data)
    if not updated_event:
        return jsonify({'error': '更新特殊事件失败'}), 500

    return jsonify({
        'message': '特殊事件已更新',
        'event': updated_event.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/events/<int:event_id>', methods=['DELETE'])
@login_required
def api_v2_delete_event(event_id):
    """删除特殊事件"""
    # 获取特殊事件
    event = EventService.get_event_by_id(event_id)
    if not event:
        return jsonify({'error': '特殊事件不存在'}), 404

    # 检查用户是否有权限操作该特殊事件
    log = DailyLogService.get_daily_log_by_id(event.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该特殊事件'}), 403

    # 删除特殊事件
    result = EventService.delete_event(event_id)
    if not result:
        return jsonify({'error': '删除特殊事件失败'}), 500

    return jsonify({
        'message': '特殊事件已删除'
    })

# 问题记录API
@daily_management_bp.route(f'{API_PREFIX}/issues', methods=['POST'])
@login_required
def api_v2_create_issue():
    """创建问题记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 验证必填字段
    required_fields = ['daily_log_id', 'issue_type', 'found_time', 'finder', 'issue_description']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 获取日志
    log_id = data.get('daily_log_id')
    log = DailyLogService.get_daily_log_by_id(log_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404

    # 检查用户是否有权限操作该日志
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该日志'}), 403

    # 添加创建者信息
    data['creator_id'] = current_user.id

    # 创建问题记录
    issue = IssueService.create_issue(data)
    if not issue:
        return jsonify({'error': '创建问题记录失败'}), 500

    return jsonify({
        'message': '问题记录已创建',
        'issue': issue.to_dict()
    }), 201

@daily_management_bp.route(f'{API_PREFIX}/issues/<int:issue_id>', methods=['GET'])
@login_required
def api_v2_get_issue(issue_id):
    """获取问题记录"""
    issue = IssueService.get_issue_by_id(issue_id)
    if not issue:
        return jsonify({'error': '问题记录不存在'}), 404

    # 检查用户是否有权限查看该问题记录
    log = DailyLogService.get_daily_log_by_id(issue.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限查看该问题记录'}), 403

    return jsonify({
        'data': issue.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/issues/<int:issue_id>', methods=['PUT'])
@login_required
def api_v2_update_issue(issue_id):
    """更新问题记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    # 获取问题记录
    issue = IssueService.get_issue_by_id(issue_id)
    if not issue:
        return jsonify({'error': '问题记录不存在'}), 404

    # 检查用户是否有权限操作该问题记录
    log = DailyLogService.get_daily_log_by_id(issue.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该问题记录'}), 403

    # 更新问题记录
    updated_issue = IssueService.update_issue(issue_id, data)
    if not updated_issue:
        return jsonify({'error': '更新问题记录失败'}), 500

    return jsonify({
        'message': '问题记录已更新',
        'issue': updated_issue.to_dict()
    })

@daily_management_bp.route(f'{API_PREFIX}/issues/<int:issue_id>', methods=['DELETE'])
@login_required
def api_v2_delete_issue(issue_id):
    """删除问题记录"""
    # 获取问题记录
    issue = IssueService.get_issue_by_id(issue_id)
    if not issue:
        return jsonify({'error': '问题记录不存在'}), 404

    # 检查用户是否有权限操作该问题记录
    log = DailyLogService.get_daily_log_by_id(issue.daily_log_id)
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该问题记录'}), 403

    # 删除问题记录
    result = IssueService.delete_issue(issue_id)
    if not result:
        return jsonify({'error': '删除问题记录失败'}), 500

    return jsonify({
        'message': '问题记录已删除'
    })

@daily_management_bp.route(f'{API_PREFIX}/inspections/<int:inspection_id>', methods=['PUT'])
@login_required
def api_v2_update_inspection(inspection_id):
    """更新检查记录"""
    data = request.json
    if not data:
        return jsonify({'error': '无效的数据'}), 400

    inspection = InspectionService.update_inspection(inspection_id, data)
    if not inspection:
        return jsonify({'error': '检查记录不存在'}), 404
    return jsonify(inspection.to_dict())

@daily_management_bp.route(f'{API_PREFIX}/inspections/<int:inspection_id>', methods=['DELETE'])
@login_required
def api_v2_delete_inspection(inspection_id):
    """删除检查记录"""
    result = InspectionService.delete_inspection(inspection_id)
    if not result:
        return jsonify({'error': '检查记录不存在'}), 404
    return jsonify({'message': '检查记录已删除'})

# 照片API
@daily_management_bp.route(f'{API_PREFIX}/photos/<string:reference_type>/<int:reference_id>', methods=['GET'])
@login_required
def api_v2_get_photos(reference_type, reference_id):
    """获取照片列表"""
    photos = PhotoService.get_photos_by_reference(reference_type, reference_id)
    return jsonify([photo.to_dict() for photo in photos])

@daily_management_bp.route(f'{API_PREFIX}/photos/<int:photo_id>', methods=['GET'])
@login_required
def api_v2_get_photo(photo_id):
    """获取照片详情"""
    photo = PhotoService.get_photo_by_id(photo_id)
    if not photo:
        return jsonify({'error': '照片不存在'}), 404
    return jsonify(photo.to_dict())

@daily_management_bp.route(f'{API_PREFIX}/photos/<string:reference_type>/<int:reference_id>', methods=['POST'])
def api_v2_upload_photo(reference_type, reference_id):
    """上传照片"""
    if 'photo' not in request.files:
        return jsonify({'error': '没有文件'}), 400

    photo_file = request.files['photo']
    if not photo_file.filename:
        return jsonify({'error': '没有选择文件'}), 400

    description = request.form.get('description', '')

    photo = PhotoService.upload_photo(photo_file, reference_type, reference_id, description)
    if not photo:
        return jsonify({'error': '上传失败'}), 500
    return jsonify(photo.to_dict()), 201

@daily_management_bp.route(f'{API_PREFIX}/photos/<int:photo_id>', methods=['DELETE'])
@login_required
def api_v2_delete_photo(photo_id):
    """删除照片"""
    result = PhotoService.delete_photo(photo_id)
    if not result:
        return jsonify({'error': '照片不存在'}), 404
    return jsonify({'message': '照片已删除'})

@daily_management_bp.route(f'{API_PREFIX}/photos/<int:photo_id>/rating', methods=['PUT'])
@login_required
def api_v2_update_photo_rating(photo_id):
    """更新照片评分"""
    data = request.json
    if not data or 'rating' not in data:
        return jsonify({'error': '无效的数据'}), 400

    rating = data.get('rating')
    if not isinstance(rating, int) or rating < 0 or rating > 5:
        return jsonify({'error': '评分必须是0-5之间的整数'}), 400

    photo = PhotoService.update_photo_rating(photo_id, rating)
    if not photo:
        return jsonify({'error': '照片不存在'}), 404
    return jsonify(photo.to_dict())

@daily_management_bp.route(f'{API_PREFIX}/upload-photos', methods=['POST'])
@login_required
def api_v2_upload_photos():
    """上传多张照片"""
    if 'photos' not in request.files:
        return jsonify({'error': '没有文件'}), 400

    log_id = request.form.get('log_id', type=int)
    if not log_id:
        return jsonify({'error': '缺少日志ID'}), 400

    time_of_day = request.form.get('time_of_day')
    if not time_of_day or time_of_day not in ['morning', 'noon', 'evening']:
        return jsonify({'error': '无效的时间段'}), 400

    # 获取日志
    log = DailyLogService.get_daily_log_by_id(log_id)
    if not log:
        return jsonify({'error': '日志不存在'}), 404

    # 检查用户是否有权限操作该日志
    if not current_user.is_admin and log.area_id != current_user.get_current_area().id:
        return jsonify({'error': '没有权限操作该日志'}), 403

    # 上传照片
    photos = []
    photo_files = request.files.getlist('photos')

    for photo_file in photo_files:
        if photo_file.filename:
            # 创建照片记录
            photo = PhotoService.upload_photo(
                photo_file=photo_file,
                reference_type='daily_log',
                reference_id=log_id,
                description=f'{time_of_day} inspection',
                time_of_day=time_of_day
            )
            if photo:
                photos.append(photo.to_dict())

    if not photos:
        return jsonify({'error': '没有成功上传任何照片'}), 400

    return jsonify({
        'message': f'成功上传 {len(photos)} 张照片',
        'photos': photos
    }), 201

# 仪表盘API
@daily_management_bp.route(f'{API_PREFIX}/dashboard/summary', methods=['GET'])
@login_required
def api_v2_dashboard_summary():
    """获取仪表盘摘要"""
    date_str = request.args.get('date')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_dashboard_summary(date_str, area_id)
    return jsonify(summary)

@daily_management_bp.route(f'{API_PREFIX}/dashboard/weekly', methods=['GET'])
@login_required
def api_v2_weekly_summary():
    """获取周摘要"""
    week_start = request.args.get('week_start')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_weekly_summary(week_start, area_id)
    return jsonify(summary)

@daily_management_bp.route(f'{API_PREFIX}/dashboard/monthly', methods=['GET'])
@login_required
def api_v2_monthly_summary():
    """获取月摘要"""
    year = request.args.get('year', type=int)
    month = request.args.get('month', type=int)
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_monthly_summary(year, month, area_id)
    return jsonify(summary)

# 统计API
@daily_management_bp.route(f'{API_PREFIX}/statistics/daily', methods=['GET'])
@login_required
def api_v2_daily_statistics():
    """获取日志统计数据"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    area_id = request.args.get('area_id', type=int)

    statistics = DailyLogService.get_daily_statistics(start_date, end_date, area_id)
    return jsonify(statistics)

@daily_management_bp.route(f'{API_PREFIX}/statistics/inspection', methods=['GET'])
@login_required
def api_v2_inspection_statistics():
    """获取检查记录统计数据"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    area_id = request.args.get('area_id', type=int)

    statistics = InspectionService.get_inspection_statistics(start_date, end_date, area_id)
    return jsonify(statistics)

@daily_management_bp.route(f'{API_PREFIX}/statistics/companion', methods=['GET'])
@login_required
def api_v2_companion_statistics():
    """获取陪餐记录统计数据"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    area_id = request.args.get('area_id', type=int)

    statistics = CompanionService.get_rating_statistics(start_date, end_date, area_id)
    return jsonify(statistics)

@daily_management_bp.route(f'{API_PREFIX}/statistics/issue', methods=['GET'])
@login_required
def api_v2_issue_statistics():
    """获取问题记录统计数据"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    area_id = request.args.get('area_id', type=int)

    statistics = IssueService.get_issue_statistics(start_date, end_date, area_id)
    return jsonify(statistics)

@daily_management_bp.route(f'{API_PREFIX}/statistics/photo', methods=['GET'])
@login_required
def api_v2_photo_statistics():
    """获取照片统计数据"""
    statistics = PhotoService.get_photo_statistics()
    return jsonify(statistics)
