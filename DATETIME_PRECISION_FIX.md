# SQL Server DateTime 精度问题修复指南

## 问题描述

遇到了 SQL Server 的 datetime 精度错误：
```
(pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
```

这个错误是由于 SQLAlchemy ORM 在处理 datetime 字段时与 SQL Server 的精度要求不匹配导致的。

## 错误原因分析

1. **模型定义问题**：某些日期字段被错误地定义为 `DATETIME2(precision=1)` 而不是 `db.Date`
2. **ORM处理问题**：SQLAlchemy ORM 在绑定 datetime 参数时出现精度不匹配
3. **查询方式问题**：使用 ORM 查询包含 datetime 字段的表时触发精度错误

## 修复方案

### 1. 模型定义修复

#### 修复前：
```python
# 错误的定义
stock_in_date = db.Column(DATETIME2(precision=1), nullable=0)
stock_out_date = db.Column(DATETIME2(precision=1), nullable=0)
```

#### 修复后：
```python
# 正确的定义
stock_in_date = db.Column(db.Date, nullable=0)
stock_out_date = db.Column(db.Date, nullable=0)
```

### 2. 查询方式修复

#### 修复前（使用ORM）：
```python
# 会触发精度错误
default_storage_location = StorageLocation.query.filter_by(warehouse_id=warehouse_id, status='正常').first()
```

#### 修复后（使用原始SQL）：
```python
# 避免ORM的datetime字段问题
storage_location_sql = text("""
SELECT TOP 1 id, name FROM storage_locations 
WHERE warehouse_id = :warehouse_id AND status = :status
""")

storage_result = db.session.execute(storage_location_sql, {
    'warehouse_id': warehouse_id,
    'status': '正常'
}).fetchone()
```

### 3. 数据插入修复

#### 修复前（使用ORM）：
```python
# 会触发精度错误
stock_in = StockIn(
    stock_in_number=stock_in_number,
    warehouse_id=warehouse_id,
    stock_in_date=datetime.strptime(stock_in_date, '%Y-%m-%d').date(),
    # ...
)
db.session.add(stock_in)
```

#### 修复后（使用原始SQL）：
```python
# 避免ORM处理datetime字段
sql = text("""
INSERT INTO stock_ins (stock_in_number, warehouse_id, stock_in_date, ...)
OUTPUT inserted.id
VALUES (:stock_in_number, :warehouse_id, :stock_in_date, ...)
""")

result = db.session.execute(sql, {
    'stock_in_number': stock_in_number,
    'warehouse_id': warehouse_id,
    'stock_in_date': stock_in_date,  # 直接使用字符串格式
    # ...
})
```

## 修复的文件列表

### 1. 模型定义文件
- **`app/models.py`**
  - 修复 `StockIn.stock_in_date` 字段类型：`DATETIME2(precision=1)` → `db.Date`
  - 修复 `StockOut.stock_out_date` 字段类型：`DATETIME2(precision=1)` → `db.Date`
  - 修复相应的 `to_dict()` 方法中的日期格式化

### 2. 路由文件
- **`app/routes/stock_in.py`**
  - 修复入库单创建的SQL语句，移除不必要的 `CONVERT` 函数
  - 修复存储位置查询，使用原始SQL避免ORM问题
  - 修复入库明细创建，使用原始SQL处理日期字段

- **`app/routes/stock_in_wizard.py`**
  - 修复向导创建入库单的SQL语句
  - 移除不必要的日期转换函数

### 3. 测试文件
- **`test_datetime_precision_fix.py`** - 专门的测试脚本（新建）
- **`DATETIME_PRECISION_FIX.md`** - 本修复指南（新建）

## 技术要点

### 1. 日期字段类型选择

| 用途 | 推荐类型 | 说明 |
|------|----------|------|
| 纯日期（如入库日期） | `db.Date` | 只存储年月日，避免时间精度问题 |
| 创建/更新时间 | `DATETIME2(precision=1)` | 需要精确的时间戳 |
| 业务日期 | `db.Date` | 大多数业务场景只需要日期 |

### 2. SQL语句编写规范

```python
# 正确的日期参数绑定
sql = text("""
INSERT INTO table_name (date_field, datetime_field)
VALUES (:date_field, GETDATE())
""")

# 参数使用字符串格式的日期
params = {
    'date_field': '2025-05-25'  # 使用字符串而不是datetime对象
}
```

### 3. 避免的操作

```python
# 避免在SQL中使用复杂的日期转换
# 错误示例：
sql = text("... CONVERT(DATE, :date_field, 23) ...")

# 正确示例：
sql = text("... :date_field ...")  # 直接使用参数
```

## 测试验证

### 运行测试脚本
```bash
python test_datetime_precision_fix.py
```

### 手动测试步骤
1. 启动Flask应用：`python run.py`
2. 访问入库单创建页面：`http://127.0.0.1:5000/stock-in/create-from-purchase-order/40`
3. 填写表单并提交
4. 确认没有出现精度错误

### 验证要点
- ✅ 入库单创建成功
- ✅ 存储位置查询正常
- ✅ 日期字段显示正确
- ✅ 无SQL精度错误

## 性能影响

### 优化效果
1. **减少错误**：消除了datetime精度错误
2. **提高稳定性**：使用原始SQL更可控
3. **简化逻辑**：避免复杂的日期转换

### 注意事项
1. **类型一致性**：确保前端传递的日期格式正确
2. **验证逻辑**：在应用层添加日期格式验证
3. **兼容性**：确保修改不影响现有功能

## 维护建议

### 1. 代码规范
- 新增日期字段时明确选择合适的类型
- 优先使用原始SQL处理复杂的日期操作
- 统一日期格式标准（YYYY-MM-DD）

### 2. 测试策略
- 每次修改日期相关代码后运行测试脚本
- 在不同环境中验证日期功能
- 监控生产环境的日期相关错误

### 3. 文档更新
- 及时更新API文档中的日期格式说明
- 记录特殊的日期处理逻辑
- 维护数据库字段类型文档

## 总结

通过以上修复，成功解决了SQL Server的datetime精度问题：

1. **✅ 模型层面**：修正了错误的字段类型定义
2. **✅ 查询层面**：使用原始SQL避免ORM的精度问题
3. **✅ 插入层面**：简化了日期参数的处理方式
4. **✅ 测试层面**：提供了完整的测试验证方案

这些修复确保了系统的稳定性和可靠性，同时保持了代码的可维护性。
