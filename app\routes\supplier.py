from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Supplier, SupplierCategory, SupplierSchoolRelation, AdministrativeArea
from app.forms.supplier import SupplierForm
from app.utils.log_activity import log_activity
from datetime import datetime

supplier_bp = Blueprint('supplier', __name__)

@supplier_bp.route('/')
@login_required
def index():
    """供应商列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    # 基本查询
    query = Supplier.query

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Supplier.name.like(f'%{keyword}%'))

    # 根据用户区域权限筛选供应商
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        query = query.join(SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct()

    pagination = query.order_by(Supplier.id.desc()).paginate(page=page, per_page=per_page)
    suppliers = pagination.items

    categories = SupplierCategory.query.all()

    # 获取当前用户的区域信息
    current_area = current_user.get_current_area()
    area_path = []
    if current_area:
        area_path = [current_area]
        ancestors = current_area.get_ancestors()
        area_path = ancestors + area_path

    return render_template('supplier/index.html',
                          suppliers=suppliers,
                          pagination=pagination,
                          categories=categories,
                          category_id=category_id,
                          keyword=keyword,
                          current_area=current_area,
                          area_path=area_path,
                          now=datetime.now())

@supplier_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """添加供应商"""
    form = SupplierForm()

    # 获取供应商分类选项
    categories = SupplierCategory.query.all()
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [(c.id, c.name) for c in categories]

    if form.validate_on_submit():
        supplier = Supplier(
            name=form.name.data,
            category_id=form.category_id.data if form.category_id.data != 0 else None,
            contact_person=form.contact_person.data,
            phone=form.phone.data,
            email=form.email.data,
            address=form.address.data,
            business_license=form.business_license.data,
            tax_id=form.tax_id.data,
            bank_name=form.bank_name.data,
            bank_account=form.bank_account.data,
            rating=form.rating.data,
            status=form.status.data
        )
        db.session.add(supplier)

        # 添加审计日志
        log_activity(
            action='create',
            resource_type='Supplier',
            resource_id=supplier.id,
            details={
                'supplier_name': supplier.name,
                'contact_person': supplier.contact_person,
                'phone': supplier.phone
            }
        )

        db.session.commit()
        flash('供应商添加成功！', 'success')
        return redirect(url_for('supplier.index'))

    return render_template('supplier/form.html', form=form, title='添加供应商', now=datetime.now())

@supplier_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑供应商"""
    supplier = Supplier.query.get_or_404(id)
    form = SupplierForm(obj=supplier)

    # 获取供应商分类选项
    categories = SupplierCategory.query.all()
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [(c.id, c.name) for c in categories]

    if form.validate_on_submit():
        old_data = {
            'name': supplier.name,
            'category_id': supplier.category_id,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'business_license': supplier.business_license,
            'tax_id': supplier.tax_id,
            'bank_name': supplier.bank_name,
            'bank_account': supplier.bank_account,
            'rating': supplier.rating,
            'status': supplier.status
        }

        supplier.name = form.name.data
        supplier.category_id = form.category_id.data if form.category_id.data != 0 else None
        supplier.contact_person = form.contact_person.data
        supplier.phone = form.phone.data
        supplier.email = form.email.data
        supplier.address = form.address.data
        supplier.business_license = form.business_license.data
        supplier.tax_id = form.tax_id.data
        supplier.bank_name = form.bank_name.data
        supplier.bank_account = form.bank_account.data
        supplier.rating = form.rating.data
        supplier.status = form.status.data

        # 添加审计日志
        log_activity(
            action='update',
            resource_type='Supplier',
            resource_id=supplier.id,
            details={
                'old': old_data,
                'new': {
                    'name': supplier.name,
                    'category_id': supplier.category_id,
                    'contact_person': supplier.contact_person,
                    'phone': supplier.phone,
                    'email': supplier.email,
                    'address': supplier.address,
                    'business_license': supplier.business_license,
                    'tax_id': supplier.tax_id,
                    'bank_name': supplier.bank_name,
                    'bank_account': supplier.bank_account,
                    'rating': supplier.rating,
                    'status': supplier.status
                }
            }
        )

        db.session.commit()
        flash('供应商更新成功！', 'success')
        return redirect(url_for('supplier.index'))

    return render_template('supplier/form.html', form=form, supplier=supplier, title='编辑供应商', now=datetime.now())

@supplier_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看供应商详情"""
    supplier = Supplier.query.get_or_404(id)

    # 使用log_activity函数记录审计日志
    log_activity(
        action='view',
        resource_type='Supplier',
        resource_id=supplier.id,
        area_id=None
    )

    return render_template('supplier/view.html', supplier=supplier, title='供应商详情', now=datetime.now())

@supplier_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除供应商"""
    supplier = Supplier.query.get_or_404(id)

    # 检查是否有关联的产品
    if supplier.products.count() > 0:
        return jsonify({'success': 0, 'message': '该供应商有关联的产品，不能删除！'})

    # 检查是否有关联的采购订单
    if supplier.purchase_orders.count() > 0:
        return jsonify({'success': 0, 'message': '该供应商有关联的采购订单，不能删除！'})

    try:
        # 先删除与供应商关联的证书
        from app.models import SupplierCertificate
        SupplierCertificate.query.filter_by(supplier_id=supplier.id).delete()

        # 使用log_activity函数记录审计日志
        log_activity(
            action='delete',
            resource_type='Supplier',
            resource_id=supplier.id,
            area_id=None,
            details={
                'supplier_name': supplier.name,
                'contact_person': supplier.contact_person,
                'phone': supplier.phone
            }
        )

        db.session.delete(supplier)
        db.session.commit()

        return jsonify({'success': 1, 'message': '供应商删除成功！'})
    except Exception as e:
        db.session.rollback()
        print(f"删除供应商时出错: {e}")
        return jsonify({'success': 0, 'message': f'删除供应商时出错: {str(e)}'})

@supplier_bp.route('/api')
@login_required
def api_list():
    """供应商API"""
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    # 基本查询
    query = Supplier.query

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Supplier.name.like(f'%{keyword}%'))

    # 根据用户区域权限筛选供应商
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        query = query.join(SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct()

    suppliers = query.all()
    return jsonify([{
        'id': s.id,
        'name': s.name,
        'contact_person': s.contact_person,
        'phone': s.phone,
        'status': s.status,
        'category_name': s.category.name if s.category else None
    } for s in suppliers])
