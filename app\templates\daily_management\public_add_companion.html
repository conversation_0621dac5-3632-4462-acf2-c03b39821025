<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .container {
            max-width: 800px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .school-name {
            font-size: 24px;
            font-weight: bold;
            color: #4e73df;
        }
        .date-info {
            font-size: 18px;
            color: #666;
            margin-top: 10px;
        }
        .form-label {
            font-weight: 500;
        }
        .required-mark {
            color: #e74a3b;
        }
        .rating-container {
            display: flex;
            flex-direction: row;
        }
        .rating-star {
            font-size: 24px;
            color: #ccc;
            cursor: pointer;
            margin-right: 5px;
        }
        .rating-star.active {
            color: #f8ce0b;
        }
        .photo-preview {
            max-width: 100%;
            max-height: 200px;
            margin-top: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="school-name">{{ school.name }}</div>
            <div class="date-info">添加陪餐记录 - {{ log.log_date }}</div>
        </div>
        
        <form method="post" action="{{ url_for('daily_management.public_submit_companion', log_id=log.id) }}" enctype="multipart/form-data">
            {{ form.csrf_token }}
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="companion_name" class="form-label">陪餐人姓名 <span class="required-mark">*</span></label>
                    <input type="text" class="form-control" id="companion_name" name="companion_name" required>
                </div>
                <div class="col-md-6">
                    <label for="companion_role" class="form-label">陪餐人角色 <span class="required-mark">*</span></label>
                    <select class="form-select" id="companion_role" name="companion_role" required>
                        <option value="">请选择角色</option>
                        <option value="校长">校长</option>
                        <option value="副校长">副校长</option>
                        <option value="主任">主任</option>
                        <option value="教师">教师</option>
                        <option value="家长">家长</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="meal_type" class="form-label">餐次 <span class="required-mark">*</span></label>
                    <select class="form-select" id="meal_type" name="meal_type" required>
                        <option value="">请选择餐次</option>
                        <option value="breakfast">早餐</option>
                        <option value="lunch">午餐</option>
                        <option value="dinner">晚餐</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="dining_date" class="form-label">陪餐日期 <span class="required-mark">*</span></label>
                    <input type="date" class="form-control" id="dining_date" name="dining_date" value="{{ log.log_date|format_datetime('%Y-%m-%d') }}" required>
                </div>
                <div class="col-md-4">
                    <label for="dining_time" class="form-label">陪餐时间 <span class="required-mark">*</span></label>
                    <input type="time" class="form-control" id="dining_time" name="dining_time" value="12:00" required>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">口味评分</label>
                    <div class="rating-container" id="taste-rating">
                        <span class="rating-star" data-value="1">★</span>
                        <span class="rating-star" data-value="2">★</span>
                        <span class="rating-star" data-value="3">★</span>
                        <span class="rating-star" data-value="4">★</span>
                        <span class="rating-star" data-value="5">★</span>
                    </div>
                    <input type="hidden" name="taste_rating" id="taste_rating_input" value="0">
                </div>
                <div class="col-md-4">
                    <label class="form-label">卫生评分</label>
                    <div class="rating-container" id="hygiene-rating">
                        <span class="rating-star" data-value="1">★</span>
                        <span class="rating-star" data-value="2">★</span>
                        <span class="rating-star" data-value="3">★</span>
                        <span class="rating-star" data-value="4">★</span>
                        <span class="rating-star" data-value="5">★</span>
                    </div>
                    <input type="hidden" name="hygiene_rating" id="hygiene_rating_input" value="0">
                </div>
                <div class="col-md-4">
                    <label class="form-label">服务评分</label>
                    <div class="rating-container" id="service-rating">
                        <span class="rating-star" data-value="1">★</span>
                        <span class="rating-star" data-value="2">★</span>
                        <span class="rating-star" data-value="3">★</span>
                        <span class="rating-star" data-value="4">★</span>
                        <span class="rating-star" data-value="5">★</span>
                    </div>
                    <input type="hidden" name="service_rating" id="service_rating_input" value="0">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="comments" class="form-label">评价意见</label>
                <textarea class="form-control" id="comments" name="comments" rows="3"></textarea>
            </div>
            
            <div class="mb-3">
                <label for="suggestions" class="form-label">改进建议</label>
                <textarea class="form-control" id="suggestions" name="suggestions" rows="3"></textarea>
            </div>
            
            <div class="mb-3">
                <label for="photos" class="form-label">照片上传</label>
                <input class="form-control" type="file" id="photos" name="photos" multiple accept="image/*">
                <div class="form-text">可以选择多张照片上传，支持jpg、jpeg、png格式</div>
                <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
            </div>
            
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">提交陪餐记录</button>
            </div>
        </form>
        
        <div class="footer">
            <p>{{ school.name }} 食堂管理系统 &copy; {{ now.year }}</p>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 星级评分
        function setupRating(containerId, inputId) {
            const container = document.getElementById(containerId);
            const input = document.getElementById(inputId);
            const stars = container.querySelectorAll('.rating-star');
            
            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    input.value = value;
                    
                    // 更新星星显示
                    stars.forEach(s => {
                        if (parseInt(s.getAttribute('data-value')) <= value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
                
                star.addEventListener('mouseover', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    
                    // 临时更新星星显示
                    stars.forEach(s => {
                        if (parseInt(s.getAttribute('data-value')) <= value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
                
                container.addEventListener('mouseout', function() {
                    const value = parseInt(input.value);
                    
                    // 恢复星星显示
                    stars.forEach(s => {
                        if (parseInt(s.getAttribute('data-value')) <= value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
            });
        }
        
        // 照片预览
        document.getElementById('photos').addEventListener('change', function(e) {
            const previewsDiv = document.getElementById('photo-previews');
            previewsDiv.innerHTML = '';
            
            for (const file of this.files) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.className = 'photo-preview me-2 mb-2';
                    previewsDiv.appendChild(img);
                }
                reader.readAsDataURL(file);
            }
        });
        
        // 初始化星级评分
        document.addEventListener('DOMContentLoaded', function() {
            setupRating('taste-rating', 'taste_rating_input');
            setupRating('hygiene-rating', 'hygiene_rating_input');
            setupRating('service-rating', 'service_rating_input');
        });
    </script>
</body>
</html>
