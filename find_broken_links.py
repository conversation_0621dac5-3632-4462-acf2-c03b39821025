"""
查找项目中的断开链接
"""

import os
import re
from urllib.parse import urlparse, urljoin
import sys

def is_valid_url(url):
    """检查URL是否有效"""
    parsed = urlparse(url)
    return bool(parsed.netloc) and bool(parsed.scheme)

def find_links_in_file(file_path):
    """查找文件中的所有链接"""
    links = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 查找所有href属性
            href_pattern = re.compile(r'href=["\']([^"\']+)["\']')
            for match in href_pattern.finditer(content):
                href = match.group(1)
                links.append(('href', href))
            
            # 查找所有src属性
            src_pattern = re.compile(r'src=["\']([^"\']+)["\']')
            for match in src_pattern.finditer(content):
                src = match.group(1)
                links.append(('src', src))
            
            # 查找所有action属性
            action_pattern = re.compile(r'action=["\']([^"\']+)["\']')
            for match in action_pattern.finditer(content):
                action = match.group(1)
                links.append(('action', action))
    
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
    
    return links

def check_link_problems(link_type, link, file_path):
    """检查链接是否存在问题"""
    problems = []
    
    # 跳过锚点链接和JavaScript链接
    if link.startswith('#') or link.startswith('javascript:'):
        return problems
    
    # 跳过数据URI
    if link.startswith('data:'):
        return problems
    
    # 跳过有效的URL
    if is_valid_url(link):
        return problems
    
    # 检查空链接
    if not link:
        problems.append(f"空{link_type}链接")
        return problems
    
    # 检查相对路径
    if link.startswith('/'):
        # 检查是否是静态文件路径
        if link.startswith('/static/'):
            # 检查静态文件是否存在
            static_path = os.path.join('app', link[1:])
            if not os.path.exists(static_path):
                problems.append(f"静态文件不存在: {link}")
        
        # 检查是否是上传文件路径
        elif link.startswith('/uploads/'):
            # 上传文件可能是动态生成的，不检查
            pass
        
        # 其他路径可能是路由
        else:
            # 这里我们只是记录，因为路由是动态的，无法静态检查
            pass
    
    # 检查相对路径（不以/开头）
    else:
        # 尝试根据文件路径解析相对路径
        base_dir = os.path.dirname(file_path)
        absolute_path = os.path.normpath(os.path.join(base_dir, link))
        
        # 检查文件是否存在
        if not os.path.exists(absolute_path) and not 'url_for' in link:
            problems.append(f"相对路径文件不存在: {link}")
    
    return problems

def find_broken_links(directory):
    """查找目录中的所有断开链接"""
    all_problems = {}
    
    for root, dirs, files in os.walk(directory):
        # 跳过某些目录
        if 'venv' in root or '__pycache__' in root:
            continue
        
        for file in files:
            # 只检查HTML文件
            if not file.endswith('.html'):
                continue
            
            file_path = os.path.join(root, file)
            links = find_links_in_file(file_path)
            
            file_problems = []
            for link_type, link in links:
                problems = check_link_problems(link_type, link, file_path)
                file_problems.extend(problems)
            
            if file_problems:
                all_problems[file_path] = file_problems
    
    return all_problems

if __name__ == "__main__":
    directory = "app"
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    
    print(f"查找目录 {directory} 中的断开链接...")
    problems = find_broken_links(directory)
    
    if problems:
        print(f"发现 {len(problems)} 个文件中存在链接问题:")
        for file_path, file_problems in problems.items():
            print(f"\n{file_path}:")
            for problem in file_problems:
                print(f"  - {problem}")
    else:
        print("未发现链接问题。")
