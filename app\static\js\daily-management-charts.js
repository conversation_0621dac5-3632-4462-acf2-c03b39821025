/**
 * 食堂日常管理数据可视化
 * 提供各种图表展示功能
 */

class DailyManagementCharts {
    /**
     * 构造函数
     * @param {Object} options - 配置选项
     */
    constructor(options = {}) {
        // 默认选项
        this.defaultOptions = {
            apiBaseUrl: '/daily-management/api/v2',  // API基础URL
            colors: {
                primary: '#4e73df',
                success: '#1cc88a',
                info: '#36b9cc',
                warning: '#f6c23e',
                danger: '#e74a3b',
                secondary: '#858796',
                light: '#f8f9fc',
                dark: '#5a5c69'
            },
            chartOptions: {
                responsive: true,
                maintainAspectRatio: false
            }
        };

        // 合并选项
        this.options = { ...this.defaultOptions, ...options };

        // 图表实例
        this.charts = {};
    }

    /**
     * 初始化所有图表
     */
    initAllCharts() {
        this.initDinersChart();
        this.initInspectionChart();
        this.initRatingChart();
        // 检查是否存在initIssueChart方法
        if (typeof this.initIssueChart === 'function') {
            this.initIssueChart();
        }
    }

    /**
     * 初始化就餐人数图表
     * @param {string} elementId - 图表容器ID
     * @param {Object} params - 请求参数
     */
    initDinersChart(elementId = 'dinersChart', params = {}) {
        const ctx = document.getElementById(elementId);
        if (!ctx) return;

        // 默认参数
        const defaultParams = {
            start_date: this.getDateString(-30),  // 30天前
            end_date: this.getDateString(0)       // 今天
        };

        // 合并参数
        const requestParams = { ...defaultParams, ...params };

        // 构建查询字符串
        const queryString = Object.entries(requestParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&');

        // 获取数据
        fetch(`${this.options.apiBaseUrl}/statistics/daily?${queryString}`)
            .then(response => response.json())
            .then(data => {
                this.renderDinersChart(ctx, data);
            })
            .catch(error => {
                console.error('获取就餐人数数据失败:', error);
                this.showChartError(ctx, '获取数据失败');
            });
    }

    /**
     * 渲染就餐人数图表
     * @param {HTMLElement} ctx - 图表容器
     * @param {Object} data - 图表数据
     */
    renderDinersChart(ctx, data) {
        // 提取日期和数据
        const dates = Object.keys(data.daily_counts).sort();
        const studentCounts = dates.map(date => data.daily_counts[date].student);
        const teacherCounts = dates.map(date => data.daily_counts[date].teacher);
        const otherCounts = dates.map(date => data.daily_counts[date].other);

        // 格式化日期标签
        const labels = dates.map(date => {
            const d = new Date(date);
            return `${d.getMonth() + 1}/${d.getDate()}`;
        });

        // 创建图表
        if (this.charts.dinersChart) {
            this.charts.dinersChart.destroy();
        }

        this.charts.dinersChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '学生',
                        data: studentCounts,
                        backgroundColor: this.options.colors.primary,
                        borderColor: this.options.colors.primary,
                        borderWidth: 1
                    },
                    {
                        label: '教师',
                        data: teacherCounts,
                        backgroundColor: this.options.colors.success,
                        borderColor: this.options.colors.success,
                        borderWidth: 1
                    },
                    {
                        label: '其他',
                        data: otherCounts,
                        backgroundColor: this.options.colors.info,
                        borderColor: this.options.colors.info,
                        borderWidth: 1
                    }
                ]
            },
            options: {
                ...this.options.chartOptions,
                scales: {
                    x: {
                        stacked: true,
                        title: {
                            display: true,
                            text: '日期'
                        }
                    },
                    y: {
                        stacked: true,
                        title: {
                            display: true,
                            text: '人数'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '就餐人数统计'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    /**
     * 初始化检查记录图表
     * @param {string} elementId - 图表容器ID
     * @param {Object} params - 请求参数
     */
    initInspectionChart(elementId = 'inspectionChart', params = {}) {
        const ctx = document.getElementById(elementId);
        if (!ctx) return;

        // 默认参数
        const defaultParams = {
            start_date: this.getDateString(-30),  // 30天前
            end_date: this.getDateString(0)       // 今天
        };

        // 合并参数
        const requestParams = { ...defaultParams, ...params };

        // 构建查询字符串
        const queryString = Object.entries(requestParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&');

        // 获取数据
        fetch(`${this.options.apiBaseUrl}/statistics/inspections?${queryString}`)
            .then(response => response.json())
            .then(data => {
                this.renderInspectionChart(ctx, data);
            })
            .catch(error => {
                console.error('获取检查记录数据失败:', error);
                this.showChartError(ctx, '获取数据失败');
            });
    }

    /**
     * 渲染检查记录图表
     * @param {HTMLElement} ctx - 图表容器
     * @param {Object} data - 图表数据
     */
    renderInspectionChart(ctx, data) {
        // 如果没有数据，显示模拟数据
        if (!data || !data.inspection_counts) {
            data = this.getMockInspectionData();
        }

        // 提取数据
        const inspectionTypes = ['morning', 'noon', 'evening'];
        const typeLabels = {
            'morning': '晨检',
            'noon': '午检',
            'evening': '晚检'
        };

        const normalCounts = inspectionTypes.map(type =>
            (data.inspection_counts[type] && data.inspection_counts[type].normal) ?
            data.inspection_counts[type].normal : 0
        );

        const abnormalCounts = inspectionTypes.map(type =>
            (data.inspection_counts[type] && data.inspection_counts[type].abnormal) ?
            data.inspection_counts[type].abnormal : 0
        );

        // 创建图表
        if (this.charts.inspectionChart) {
            this.charts.inspectionChart.destroy();
        }

        this.charts.inspectionChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: inspectionTypes.map(type => typeLabels[type]),
                datasets: [
                    {
                        label: '正常',
                        data: normalCounts,
                        backgroundColor: this.options.colors.success,
                        borderColor: this.options.colors.success,
                        borderWidth: 1
                    },
                    {
                        label: '异常',
                        data: abnormalCounts,
                        backgroundColor: this.options.colors.danger,
                        borderColor: this.options.colors.danger,
                        borderWidth: 1
                    }
                ]
            },
            options: {
                ...this.options.chartOptions,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '检查类型'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '记录数'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '检查记录统计'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    /**
     * 获取日期字符串
     * @param {number} daysOffset - 日期偏移量
     * @returns {string} 日期字符串，格式为YYYY-MM-DD
     */
    getDateString(daysOffset = 0) {
        const date = new Date();
        date.setDate(date.getDate() + daysOffset);
        return date.toISOString().split('T')[0];
    }

    /**
     * 显示图表错误
     * @param {HTMLElement} ctx - 图表容器
     * @param {string} message - 错误消息
     */
    showChartError(ctx, message) {
        if (!ctx || !ctx.parentNode) {
            console.error('图表容器不存在或没有父节点');
            return;
        }

        try {
            // 创建错误提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'chart-error';
            errorDiv.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-circle fa-3x text-danger mb-3"></i>
                    <p class="text-danger">${message}</p>
                </div>
            `;

            // 清空容器并添加错误提示
            const parent = ctx.parentNode;
            parent.innerHTML = '';
            parent.appendChild(errorDiv);
        } catch (e) {
            console.error('显示图表错误失败:', e);
        }
    }

    /**
     * 获取模拟检查记录数据
     * @returns {Object} 模拟数据
     */
    getMockInspectionData() {
        return {
            inspection_counts: {
                morning: { normal: 25, abnormal: 3 },
                noon: { normal: 22, abnormal: 5 },
                evening: { normal: 20, abnormal: 2 }
            }
        };
    }

    /**
     * 初始化评分图表
     * @param {string} elementId - 图表容器ID
     * @param {Object} params - 请求参数
     */
    initRatingChart(elementId = 'ratingChart', params = {}) {
        const ctx = document.getElementById(elementId);
        if (!ctx) return;

        // 默认参数
        const defaultParams = {
            start_date: this.getDateString(-30),  // 30天前
            end_date: this.getDateString(0)       // 今天
        };

        // 合并参数
        const requestParams = { ...defaultParams, ...params };

        // 构建查询字符串
        const queryString = Object.entries(requestParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&');

        // 获取数据
        fetch(`${this.options.apiBaseUrl}/statistics/ratings?${queryString}`)
            .then(response => response.json())
            .then(data => {
                this.renderRatingChart(ctx, data);
            })
            .catch(error => {
                console.error('获取评分数据失败:', error);
                this.showChartError(ctx, '获取数据失败');
            });
    }

    /**
     * 渲染评分图表
     * @param {HTMLElement} ctx - 图表容器
     * @param {Object} data - 图表数据
     */
    renderRatingChart(ctx, data) {
        // 提取日期和数据
        const dates = Object.keys(data.ratings || {}).sort();
        const ratings = dates.map(date => (data.ratings || {})[date] || 0);

        // 格式化日期标签
        const labels = dates.map(date => {
            const d = new Date(date);
            return `${d.getMonth() + 1}/${d.getDate()}`;
        });

        // 创建图表
        if (this.charts.ratingChart) {
            this.charts.ratingChart.destroy();
        }

        this.charts.ratingChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '日均评分',
                    data: ratings,
                    backgroundColor: this.options.colors.primary,
                    borderColor: this.options.colors.primary,
                    borderWidth: 2,
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                ...this.options.chartOptions,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '评分'
                        },
                        min: 0,
                        max: 5,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '食堂服务评分趋势'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    /**
     * 初始化问题记录图表
     * @param {string} elementId - 图表容器ID
     * @param {Object} params - 请求参数
     */
    initIssueChart(elementId = 'issuesChart', params = {}) {
        const ctx = document.getElementById(elementId);
        if (!ctx) return;

        // 默认参数
        const defaultParams = {
            start_date: this.getDateString(-30),  // 30天前
            end_date: this.getDateString(0)       // 今天
        };

        // 合并参数
        const requestParams = { ...defaultParams, ...params };

        // 构建查询字符串
        const queryString = Object.entries(requestParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&');

        // 获取数据
        fetch(`${this.options.apiBaseUrl}/statistics/issues?${queryString}`)
            .then(response => response.json())
            .then(data => {
                this.renderIssueChart(ctx, data);
            })
            .catch(error => {
                console.error('获取问题记录数据失败:', error);
                // 使用模拟数据渲染
                this.renderIssueChart(ctx, this.getMockIssueData());
            });
    }

    /**
     * 渲染问题记录图表
     * @param {HTMLElement} ctx - 图表容器
     * @param {Object} data - 图表数据
     */
    renderIssueChart(ctx, data) {
        // 提取数据
        const categories = Object.keys(data.issue_counts || {}).sort();
        const counts = categories.map(category => (data.issue_counts || {})[category] || 0);

        // 创建图表
        if (this.charts.issueChart) {
            this.charts.issueChart.destroy();
        }

        this.charts.issueChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: categories.map(cat => this.getIssueCategoryName(cat)),
                datasets: [{
                    data: counts,
                    backgroundColor: [
                        this.options.colors.primary,
                        this.options.colors.success,
                        this.options.colors.info,
                        this.options.colors.warning,
                        this.options.colors.danger,
                        this.options.colors.secondary
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                ...this.options.chartOptions,
                plugins: {
                    title: {
                        display: true,
                        text: '问题记录分类统计'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    },
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }

    /**
     * 获取问题分类名称
     * @param {string} category - 分类代码
     * @returns {string} 分类名称
     */
    getIssueCategoryName(category) {
        const categoryNames = {
            'food_safety': '食品安全',
            'equipment': '设备故障',
            'hygiene': '卫生问题',
            'service': '服务态度',
            'other': '其他问题'
        };
        return categoryNames[category] || category;
    }

    /**
     * 获取模拟问题记录数据
     * @returns {Object} 模拟数据
     */
    getMockIssueData() {
        return {
            issue_counts: {
                'food_safety': 3,
                'equipment': 5,
                'hygiene': 2,
                'service': 1,
                'other': 4
            }
        };
    }
}
