{% extends 'base.html' %}

{% block title %}批次编辑器 (步骤2) - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
  .batch-editor-container {
    margin-top: 20px;
  }
  .batch-card {
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
  }
  .batch-header {
    background-color: #f8f9fa;
    padding: 10px;
    cursor: pointer;
  }
  .batch-body {
    padding: 15px;
  }
  .supplier-select {
    max-width: 300px;
  }
  .action-buttons {
    margin-top: 20px;
  }
  .batch-table th, .batch-table td {
    vertical-align: middle;
  }
  .batch-group {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
  }
  .batch-group-header {
    background-color: #e9ecef;
    padding: 10px 15px;
    font-weight: bold;
  }
  .batch-items {
    padding: 15px;
  }
  .form-row {
    margin-bottom: 15px;
  }

  /* 新增样式 */
  .highlight-box {
    background-color: #f8f9fa;
    border-left: 4px solid #dc3545;
    padding: 15px;
    margin-bottom: 20px;
  }

  .batch-table th.bg-light {
    background-color: #f8f9fa !important;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .batch-row:hover {
    background-color: #f8f9fa;
  }

  .batch-row td {
    padding: 12px 8px;
  }

  .step-indicator {
    display: flex;
    margin-bottom: 20px;
  }

  .step {
    flex: 1;
    text-align: center;
    padding: 10px;
    position: relative;
  }

  .step.active {
    font-weight: bold;
    color: #4e73df;
  }

  .step.completed {
    color: #1cc88a;
  }

  .step:not(:last-child):after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 100%;
    height: 2px;
    background-color: #e3e6f0;
    z-index: -1;
  }

  .step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    background-color: #f8f9fc;
    border: 1px solid #e3e6f0;
    margin-bottom: 5px;
  }

  .step.active .step-number {
    background-color: #4e73df;
    color: white;
    border-color: #4e73df;
  }

  .step.completed .step-number {
    background-color: #1cc88a;
    color: white;
    border-color: #1cc88a;
  }

  .document-upload {
    border: 2px dashed #e3e6f0;
    padding: 15px;
    text-align: center;
    margin-bottom: 15px;
    border-radius: 5px;
    background-color: #f8f9fc;
  }

  .document-upload:hover {
    border-color: #4e73df;
    background-color: #eef1ff;
  }

  .document-preview {
    max-width: 100%;
    max-height: 150px;
    margin-top: 10px;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
  }

  .date-input-group {
    position: relative;
  }

  .date-input-group .form-control {
    padding-right: 40px;
  }

  .date-input-group .calendar-icon {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #4e73df;
    pointer-events: none;
  }

  .batch-group-header {
    background-color: #4e73df;
    color: white;
    padding: 10px 15px;
    font-weight: bold;
    border-radius: 5px 5px 0 0;
  }

  .document-list {
    margin-top: 10px;
  }

  .document-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    margin-bottom: 5px;
    background-color: #f8f9fc;
  }

  .document-item i {
    margin-right: 10px;
    color: #4e73df;
  }

  .document-item .document-name {
    flex-grow: 1;
  }

  .document-item .document-actions {
    margin-left: 10px;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">批次编辑器 - 步骤2：日期与单据</h6>
          <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
              <div class="dropdown-header">批次操作:</div>
              <a class="dropdown-item" href="#" id="selectAllBatches"><i class="fas fa-check-square fa-sm fa-fw mr-2 text-gray-400"></i>全选</a>
              <a class="dropdown-item" href="#" id="deselectAllBatches"><i class="far fa-square fa-sm fa-fw mr-2 text-gray-400"></i>取消全选</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="#" id="groupByIngredient"><i class="fas fa-object-group fa-sm fa-fw mr-2 text-gray-400"></i>按食材分组</a>
              <a class="dropdown-item" href="#" id="groupBySupplier"><i class="fas fa-building fa-sm fa-fw mr-2 text-gray-400"></i>按供应商分组</a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <!-- 步骤指示器 -->
          <div class="step-indicator">
            <div class="step completed">
              <div class="step-number">1</div>
              <div>基本信息</div>
            </div>
            <div class="step active">
              <div class="step-number">2</div>
              <div>日期与单据</div>
            </div>
          </div>

          <div class="highlight-box">
            <h5><i class="fas fa-info-circle text-danger"></i> 步骤2：日期与单据绑定</h5>
            <p>在此步骤中，请完成以下操作：</p>
            <ol>
              <li>为每个批次设置<strong>生产日期</strong>和<strong>过期日期</strong></li>
              <li>上传相关<strong>检验检疫证明</strong>和<strong>入库单据</strong></li>
              <li>可以按供应商分组批量设置日期和上传单据</li>
              <li>完成后点击<strong>保存</strong>按钮完成整个入库流程</li>
            </ol>
            <p class="mb-0 text-danger"><i class="fas fa-exclamation-triangle"></i> 注意：日期和单据是必填项，请确保每个批次都有完整信息。</p>
          </div>

          <!-- 批量操作表单 -->
          <form id="batchEditForm" method="post" action="{{ url_for('stock_in.save_batch_edit_step2', stock_in_id=stock_in.id) }}" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <!-- 批量操作工具栏 -->
            <div class="row mb-4">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="bulkProductionDate"><strong>批量设置生产日期:</strong></label>
                  <div class="input-group date-input-group">
                    <input type="date" class="form-control" id="bulkProductionDate" value="{{ now.strftime('%Y-%m-%d') }}">
                    <div class="calendar-icon">
                      <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="input-group-append">
                      <button class="btn btn-primary" type="button" id="applyBulkProductionDate">应用</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="bulkExpiryDate"><strong>批量设置过期日期:</strong></label>
                  <div class="input-group date-input-group">
                    <input type="date" class="form-control" id="bulkExpiryDate" value="{{ (now + timedelta(days=30)).strftime('%Y-%m-%d') }}">
                    <div class="calendar-icon">
                      <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="input-group-append">
                      <button class="btn btn-primary" type="button" id="applyBulkExpiryDate">应用</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label><strong>批量上传单据:</strong></label>
                  <div class="input-group">
                    <div class="custom-file">
                      <input type="file" class="custom-file-input" id="bulkDocumentUpload" accept=".pdf,.jpg,.jpeg,.png">
                      <label class="custom-file-label" for="bulkDocumentUpload">选择文件</label>
                    </div>
                    <div class="input-group-append">
                      <button class="btn btn-primary" type="button" id="applyBulkDocument">上传</button>
                    </div>
                  </div>
                  <small class="form-text text-muted">支持PDF、JPG、PNG格式</small>
                </div>
              </div>
            </div>

            <!-- 批次列表 -->
            <div class="table-responsive">
              <table class="table table-bordered batch-table" id="batchTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th width="5%">
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="selectAll">
                        <label class="custom-control-label" for="selectAll"></label>
                      </div>
                    </th>
                    <th width="15%">食材</th>
                    <th width="15%">供应商</th>
                    <th width="15%" class="bg-light">生产日期</th>
                    <th width="15%" class="bg-light">过期日期</th>
                    <th width="35%" class="bg-light">检验检疫证明/入库单据</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in stock_in_items %}
                  <tr class="batch-row" data-ingredient-id="{{ item.ingredient_id }}" data-supplier-id="{{ item.supplier_id }}">
                    <td>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input batch-checkbox" id="batch{{ loop.index }}" name="selected_items[]" value="{{ item.id }}">
                        <label class="custom-control-label" for="batch{{ loop.index }}"></label>
                      </div>
                    </td>
                    <td>
                      {{ item.ingredient.name }}
                      <small class="text-muted d-block">批次: {{ item.batch_number }}</small>
                      <small class="text-muted d-block">{{ item.quantity }} {{ item.unit }} × {{ item.unit_price }}元</small>
                    </td>
                    <td>
                      {% if item.supplier %}
                        {{ item.supplier.name }}
                      {% else %}
                        <span class="text-danger">未设置</span>
                      {% endif %}
                    </td>
                    <td>
                      <div class="date-input-group">
                        <input type="date" class="form-control" name="production_date_{{ item.id }}"
                               value="{{ item.production_date.strftime('%Y-%m-%d') if item.production_date else now.strftime('%Y-%m-%d') }}" required>
                        <div class="calendar-icon">
                          <i class="fas fa-calendar-alt"></i>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="date-input-group">
                        <input type="date" class="form-control" name="expiry_date_{{ item.id }}"
                               value="{{ item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else (now + timedelta(days=30)).strftime('%Y-%m-%d') }}" required>
                        <div class="calendar-icon">
                          <i class="fas fa-calendar-alt"></i>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="document-upload" id="dropzone_{{ item.id }}">
                        <input type="file" name="documents_{{ item.id }}[]" class="document-input" id="documents_{{ item.id }}" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                        <label for="documents_{{ item.id }}" class="mb-0">
                          <i class="fas fa-upload fa-2x mb-2 text-primary"></i>
                          <div>点击或拖拽文件上传</div>
                          <small class="text-muted">支持PDF、JPG、PNG格式</small>
                        </label>
                      </div>

                      <div class="document-list" id="document_list_{{ item.id }}">
                        {% for doc in item.documents %}
                        <div class="document-item">
                          <i class="{% if doc.file_path.endswith('.pdf') %}fas fa-file-pdf{% else %}fas fa-file-image{% endif %}"></i>
                          <div class="document-name">{{ doc.file_name }}</div>
                          <div class="document-actions">
                            <a href="{{ doc.file_path }}" target="_blank" class="btn btn-sm btn-info">
                              <i class="fas fa-eye"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-danger delete-document" data-document-id="{{ doc.id }}">
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </div>
                        {% endfor %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons text-center">
              <a href="{{ url_for('stock_in.batch_editor_step1', id=stock_in.id) }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-left"></i> 返回上一步
              </a>
              <button type="submit" class="btn btn-success btn-lg ml-2">
                <i class="fas fa-save"></i> 保存并完成
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
  $(document).ready(function() {
    // 全选/取消全选
    $("#selectAll").change(function() {
      $(".batch-checkbox").prop('checked', $(this).prop('checked'));
    });

    // 批量设置生产日期
    $("#applyBulkProductionDate").click(function() {
      var date = $("#bulkProductionDate").val();
      if (!date) {
        alert("请先选择日期");
        return;
      }

      $(".batch-checkbox:checked").each(function() {
        var itemId = $(this).val();
        $("input[name='production_date_" + itemId + "']").val(date);
      });
    });

    // 批量设置过期日期
    $("#applyBulkExpiryDate").click(function() {
      var date = $("#bulkExpiryDate").val();
      if (!date) {
        alert("请先选择日期");
        return;
      }

      $(".batch-checkbox:checked").each(function() {
        var itemId = $(this).val();
        $("input[name='expiry_date_" + itemId + "']").val(date);
      });
    });

    // 下拉菜单操作
    $("#selectAllBatches").click(function(e) {
      e.preventDefault();
      $(".batch-checkbox").prop('checked', true);
      $("#selectAll").prop('checked', true);
    });

    $("#deselectAllBatches").click(function(e) {
      e.preventDefault();
      $(".batch-checkbox").prop('checked', false);
      $("#selectAll").prop('checked', false);
    });

    // 按食材分组
    $("#groupByIngredient").click(function(e) {
      e.preventDefault();
      groupTable("ingredient-id");
    });

    // 按供应商分组
    $("#groupBySupplier").click(function(e) {
      e.preventDefault();
      groupTable("supplier-id");
    });

    // 分组函数
    function groupTable(groupBy) {
      var rows = $(".batch-row").detach();
      var groups = {};

      rows.each(function() {
        var key = $(this).data(groupBy);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(this);
      });

      var tbody = $("#batchTable tbody");
      tbody.empty();

      $.each(groups, function(key, groupRows) {
        var groupHeader = $("<tr>").addClass("table-secondary");
        var headerText = "";

        if (groupBy === "ingredient-id") {
          headerText = "食材: " + $(groupRows[0]).find("td:eq(1)").text().trim().split('\n')[0] + " (" + groupRows.length + "个批次)";
        } else if (groupBy === "supplier-id") {
          headerText = "供应商: " + $(groupRows[0]).find("td:eq(2)").text().trim() + " (" + groupRows.length + "个批次)";
        }

        groupHeader.append($("<td>").attr("colspan", 6).text(headerText));
        tbody.append(groupHeader);

        $.each(groupRows, function(i, row) {
          tbody.append(row);
        });
      });
    }

    // 文件上传预览
    $(".document-input").change(function(e) {
      var files = e.target.files;
      var itemId = $(this).attr('id').replace('documents_', '');
      var documentList = $("#document_list_" + itemId);

      // 清空预览
      documentList.empty();

      // 添加新文件预览
      for (var i = 0; i < files.length; i++) {
        var file = files[i];
        var isImage = file.type.match('image.*');
        var isPdf = file.type === 'application/pdf';

        if (isImage || isPdf) {
          var documentItem = $('<div class="document-item"></div>');
          var icon = isImage ? '<i class="fas fa-file-image"></i>' : '<i class="fas fa-file-pdf"></i>';

          documentItem.append(icon);
          documentItem.append('<div class="document-name">' + file.name + '</div>');
          documentItem.append('<div class="document-actions"><button type="button" class="btn btn-sm btn-danger remove-file"><i class="fas fa-times"></i></button></div>');

          documentList.append(documentItem);
        }
      }
    });

    // 删除已上传文件
    $(document).on('click', '.delete-document', function() {
      var documentId = $(this).data('document-id');
      var documentItem = $(this).closest('.document-item');

      if (confirm('确定要删除这个文件吗？')) {
        $.ajax({
          url: "{{ url_for('stock_in.delete_batch_document') }}",
          type: "POST",
          data: {
            document_id: documentId,
            csrf_token: "{{ csrf_token() }}"
          },
          success: function(response) {
            if (response.success) {
              documentItem.remove();
            } else {
              alert('删除文件失败: ' + response.message);
            }
          },
          error: function() {
            alert('删除文件请求失败');
          }
        });
      }
    });

    // 移除未上传的文件
    $(document).on('click', '.remove-file', function() {
      $(this).closest('.document-item').remove();
    });

    // 批量上传文件
    $("#applyBulkDocument").click(function() {
      var fileInput = $("#bulkDocumentUpload")[0];
      if (fileInput.files.length === 0) {
        alert("请先选择文件");
        return;
      }

      // 获取选中的批次
      var selectedItems = $(".batch-checkbox:checked");
      if (selectedItems.length === 0) {
        alert("请至少选择一个批次");
        return;
      }

      // 创建一个FormData对象
      var formData = new FormData();
      for (var i = 0; i < fileInput.files.length; i++) {
        formData.append('documents[]', fileInput.files[i]);
      }

      // 添加选中的批次ID
      selectedItems.each(function() {
        formData.append('item_ids[]', $(this).val());
      });

      formData.append('csrf_token', "{{ csrf_token() }}");

      // 发送AJAX请求
      $.ajax({
        url: "{{ url_for('stock_in.bulk_upload_documents', stock_in_id=stock_in.id) }}",
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
          if (response.success) {
            alert('文件上传成功');
            // 刷新页面以显示新上传的文件
            location.reload();
          } else {
            alert('文件上传失败: ' + response.message);
          }
        },
        error: function() {
          alert('文件上传请求失败');
        }
      });
    });

    // 表单提交前验证
    $('#batchEditForm').submit(function(e) {
      var selectedItems = $('.batch-checkbox:checked').length;

      if (selectedItems === 0) {
        e.preventDefault();
        alert('请至少选择一个批次进行编辑');
        return false;
      }

      var hasEmptyFields = false;

      $('.batch-checkbox:checked').each(function() {
        var itemId = $(this).val();
        var row = $(this).closest('tr');

        var productionDate = row.find('input[name="production_date_' + itemId + '"]').val();
        var expiryDate = row.find('input[name="expiry_date_' + itemId + '"]').val();
        var hasDocuments = row.find('#document_list_' + itemId + ' .document-item').length > 0;

        if (!productionDate || !expiryDate) {
          hasEmptyFields = true;
          return false;
        }

        if (!hasDocuments) {
          // 检查是否有新上传的文件
          var fileInput = row.find('input[type="file"]')[0];
          if (!fileInput || fileInput.files.length === 0) {
            alert('请为每个批次上传至少一个检验检疫证明或入库单据');
            hasEmptyFields = true;
            return false;
          }
        }
      });

      if (hasEmptyFields) {
        e.preventDefault();
        alert('请为选中的批次填写完整的日期信息并上传相关单据');
        return false;
      }

      return true;
    });

    // 自定义文件输入框显示选中的文件名
    $('.custom-file-input').on('change', function() {
      var fileName = $(this).val().split('\\').pop();
      $(this).next('.custom-file-label').html(fileName);
    });
  });
</script>
{% endblock %}
