"""
文件处理工具函数
"""

def allowed_file(filename, allowed_extensions):
    """
    检查文件扩展名是否在允许的扩展名列表中
    
    :param filename: 文件名
    :param allowed_extensions: 允许的扩展名列表，如 ['jpg', 'png', 'gif']
    :return: 如果文件扩展名在允许列表中，返回 True，否则返回 False
    """
    if not filename:
        return False
        
    # 获取文件扩展名
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in [ext.lower() for ext in allowed_extensions]
