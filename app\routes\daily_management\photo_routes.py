"""
照片管理相关路由
"""

import os
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, render_template, abort, url_for
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from PIL import Image
from app import db
from app.models_daily_management import Photo, InspectionRecord
from app.models import AdministrativeArea
from sqlalchemy import text
import io
import traceback
import qrcode
from io import BytesIO
import base64

photo_bp = Blueprint('photo', __name__)

@photo_bp.route('/upload/<int:school_id>/<string:period>', methods=['GET'])
def public_upload_page(school_id, period):
    """公开上传页面 - 员工使用"""
    try:
        # 验证时间段
        if period not in ['morning', 'noon', 'evening']:
            abort(404)
            
        # 获取学校信息
        school = AdministrativeArea.query.get_or_404(school_id)
        
        # 获取今日记录
        today = datetime.now().date()
        log = DailyLog.query.filter_by(
            school_id=school_id,
            log_date=today
        ).first()
        
        if not log:
            # 创建今日记录
            log = DailyLog(
                school_id=school_id,
                log_date=today,
                status='active'
            )
            db.session.add(log)
            db.session.commit()
        
        # 获取历史照片
        history_photos = Photo.query.join(InspectionRecord).filter(
            InspectionRecord.daily_log_id == log.id,
            InspectionRecord.inspection_type == period
        ).order_by(Photo.upload_time.desc()).all()
        
        return render_template(
            'daily_management/public_upload_photo.html',
            school=school,
            log=log,
            period=period,
            history_photos=history_photos,
            now=datetime.now()
        )
        
    except Exception as e:
        current_app.logger.error(f"访问上传页面失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        abort(500)

@photo_bp.route('/check/<int:school_id>', methods=['GET'])
@login_required
def check_photos_page(school_id):
    """检查页面 - 管理员使用"""
    try:
        # 获取学校信息
        school = AdministrativeArea.query.get_or_404(school_id)
        
        # 获取日期范围
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        if not start_date:
            start_date = datetime.now().date()
        if not end_date:
            end_date = start_date
            
        # 获取照片记录
        photos = Photo.query.join(InspectionRecord).join(DailyLog).filter(
            DailyLog.school_id == school_id,
            DailyLog.log_date.between(start_date, end_date)
        ).order_by(Photo.upload_time.desc()).all()
        
        return render_template(
            'daily_management/check_photos.html',
            school=school,
            photos=photos,
            start_date=start_date,
            end_date=end_date,
            now=datetime.now()
        )
        
    except Exception as e:
        current_app.logger.error(f"访问检查页面失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        abort(500)

@photo_bp.route('/api/v2/photos/upload/<int:school_id>/<string:period>', methods=['POST'])
def upload_photo(school_id, period):
    """上传照片API"""
    try:
        if 'photo' not in request.files:
            return jsonify({'error': '没有文件'}), 400
            
        photo_file = request.files['photo']
        if not photo_file.filename:
            return jsonify({'error': '没有选择文件'}), 400
            
        # 获取今日记录
        today = datetime.now().date()
        log = DailyLog.query.filter_by(
            school_id=school_id,
            log_date=today
        ).first()
        
        if not log:
            log = DailyLog(
                school_id=school_id,
                log_date=today,
                status='active'
            )
            db.session.add(log)
            db.session.commit()
            
        # 获取或创建检查记录
        inspection = InspectionRecord.query.filter_by(
            daily_log_id=log.id,
            inspection_type=period
        ).first()
        
        if not inspection:
            inspection = InspectionRecord(
                daily_log_id=log.id,
                inspection_type=period,
                inspection_item=f'{period}卫生检查',
                status='normal'
            )
            db.session.add(inspection)
            db.session.commit()
            
        # 处理照片上传
        try:
            photo = handle_photo_upload(photo_file, 'inspection', inspection.id)
            
            return jsonify({
                'success': True,
                'id': photo.id,
                'file_path': photo.file_path,
                'message': '照片上传成功'
            })
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"上传照片失败: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({'error': f'上传失败: {str(e)}'}), 500
            
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"处理上传请求失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': f'处理失败: {str(e)}'}), 500

@photo_bp.route('/api/v2/photos/rate', methods=['POST'])
@login_required
def rate_photo():
    """评分照片API"""
    try:
        data = request.json
        if not data or 'photo_id' not in data or 'rating' not in data:
            return jsonify({'error': '缺少必要参数'}), 400
            
        photo_id = data['photo_id']
        rating = data['rating']
        comment = data.get('comment', '')
        
        if not isinstance(rating, int) or rating < 1 or rating > 5:
            return jsonify({'error': '评分必须是1-5之间的整数'}), 400
            
        # 更新照片评分
        sql = text("""
        UPDATE photos
        SET rating = :rating,
            comment = :comment,
            rated_by = :rated_by,
            rated_at = :rated_at
        WHERE id = :photo_id
        """)
        
        result = db.session.execute(sql, {
            'photo_id': photo_id,
            'rating': rating,
            'comment': comment,
            'rated_by': current_user.id,
            'rated_at': datetime.now()
        })
        
        if result.rowcount == 0:
            return jsonify({'error': '照片不存在'}), 404
            
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '评分成功',
            'photo_id': photo_id,
            'rating': rating
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"评分照片失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': f'评分失败: {str(e)}'}), 500

def handle_photo_upload(photo_file, reference_type, reference_id):
    """处理照片上传"""
    if not photo_file or not photo_file.filename:
        raise ValueError('没有文件')
        
    # 确保文件名安全
    filename = secure_filename(photo_file.filename)
    
    # 生成唯一文件名
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    unique_filename = f"{timestamp}_{filename}"
    
    # 确定保存路径
    upload_folder = os.path.join(
        current_app.static_folder,
        'uploads',
        'daily_management',
        reference_type
    )
    
    # 确保目录存在
    os.makedirs(upload_folder, exist_ok=True)
    
    # 处理图片
    img = Image.open(photo_file)
    
    # 调整大小为800x600，保持宽高比
    img.thumbnail((800, 600))
    
    # 保存处理后的图片
    file_path = os.path.join(upload_folder, unique_filename)
    img.save(file_path)
    
    # 相对路径（用于数据库存储）
    relative_path = f"/static/uploads/daily_management/{reference_type}/{unique_filename}"
    
    # 创建照片记录
    photo = Photo(
        reference_id=reference_id,
        reference_type=reference_type,
        file_name=unique_filename,
        file_path=relative_path,
        upload_time=datetime.now()
    )
    
    db.session.add(photo)
    db.session.commit()
    
    return photo

# 公开评分照片页面
@photo_bp.route('/photos/public/rate/<int:photo_id>')
def public_rate_photo_page(photo_id):
    """公开评分照片页面"""
    try:
        # 获取照片信息
        photo = Photo.query.get_or_404(photo_id)
        
        # 获取学校信息
        school = AdministrativeArea.query.get(photo.reference_id)
        if not school:
            abort(404)

        return render_template(
            'daily_management/public_rate_photo.html',
            photo=photo,
            school=school,
            now=datetime.now()
        )

    except Exception as e:
        current_app.logger.error(f"访问照片评分页面失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        abort(500)

# 公开评分照片API
@photo_bp.route('/api/v2/photos/public/rate', methods=['POST'])
def public_rate_photo():
    """公开评分照片API"""
    try:
        data = request.json
        if not data or 'photo_id' not in data or 'rating' not in data:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        photo_id = data['photo_id']
        rating = data['rating']

        # 验证评分范围
        if not isinstance(rating, int) or rating < 1 or rating > 5:
            return jsonify({'success': False, 'error': '评分必须是1-5之间的整数'}), 400

        # 更新照片评分
        sql = text("""
        UPDATE photos
        SET rating = :rating
        WHERE id = :photo_id
        """)

        result = db.session.execute(sql, {'photo_id': photo_id, 'rating': rating})
        if result.rowcount == 0:
            return jsonify({'success': False, 'error': '照片不存在'}), 404

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '评分成功',
            'photo_id': photo_id,
            'rating': rating
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"公开评分照片失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'error': f'评分失败: {str(e)}'}), 500
