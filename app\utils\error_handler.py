"""
错误处理工具模块

提供统一的错误处理和日志记录功能。
"""

import logging
from flask import current_app, jsonify
import traceback
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

def log_error(e, context=None):
    """
    记录错误日志
    
    Args:
        e: 异常对象
        context: 上下文信息，如操作类型、相关ID等
    
    Returns:
        str: 错误消息
    """
    error_message = str(e)
    error_traceback = traceback.format_exc()
    
    # 构建日志消息
    log_message = f"错误: {error_message}"
    if context:
        log_message += f"\n上下文: {context}"
    log_message += f"\n堆栈跟踪: {error_traceback}"
    
    # 记录日志
    current_app.logger.error(log_message)
    
    return error_message

def handle_db_error(e, operation=None, entity=None):
    """
    处理数据库错误
    
    Args:
        e: 异常对象
        operation: 操作类型，如'create', 'update', 'delete'
        entity: 实体类型，如'inspection', 'daily_log'
    
    Returns:
        tuple: (错误消息, HTTP状态码)
    """
    # 记录错误
    context = f"操作: {operation}, 实体: {entity}" if operation and entity else None
    error_message = log_error(e, context)
    
    # 处理特定类型的错误
    if isinstance(e, IntegrityError):
        if 'UNIQUE' in str(e) or 'unique' in str(e):
            return "记录已存在，违反唯一约束", 409
        elif 'FOREIGN KEY' in str(e) or 'foreign key' in str(e):
            return "关联记录不存在，违反外键约束", 400
        else:
            return f"数据完整性错误: {error_message}", 400
    elif isinstance(e, SQLAlchemyError):
        return f"数据库错误: {error_message}", 500
    else:
        return f"未知错误: {error_message}", 500

def api_error_response(message, status_code=400, details=None):
    """
    生成API错误响应
    
    Args:
        message: 错误消息
        status_code: HTTP状态码
        details: 错误详情
    
    Returns:
        tuple: (JSON响应, HTTP状态码)
    """
    response = {
        'success': False,
        'message': message,
        'timestamp': datetime.now().isoformat()
    }
    
    if details:
        response['details'] = details
        
    return jsonify(response), status_code

def safe_db_operation(operation_func, error_message="数据库操作失败", *args, **kwargs):
    """
    安全执行数据库操作
    
    Args:
        operation_func: 要执行的数据库操作函数
        error_message: 错误时的消息
        *args, **kwargs: 传递给operation_func的参数
    
    Returns:
        operation_func的返回值或None(如果出错)
    """
    from app import db
    
    try:
        result = operation_func(*args, **kwargs)
        db.session.commit()
        return result
    except Exception as e:
        db.session.rollback()
        log_error(e, error_message)
        return None

class DatabaseOperationError(Exception):
    """数据库操作错误异常"""
    def __init__(self, message, status_code=500, details=None):
        self.message = message
        self.status_code = status_code
        self.details = details
        super().__init__(self.message)

def db_operation_error_handler(f):
    """
    数据库操作错误处理装饰器
    
    用法示例:
    @app.route('/api/resource', methods=['POST'])
    @login_required
    @db_operation_error_handler
    def create_resource():
        # 这里的代码如果抛出DatabaseOperationError，会被自动处理
        ...
    """
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except DatabaseOperationError as e:
            return api_error_response(e.message, e.status_code, e.details)
        except SQLAlchemyError as e:
            message, status_code = handle_db_error(e)
            return api_error_response(message, status_code)
        except Exception as e:
            log_error(e)
            return api_error_response(f"服务器错误: {str(e)}", 500)
    
    return decorated_function
