from flask import current_app
from app.models import PurchaseOrder, PurchaseOrderItem, StockIn, StockInItem, StockInDocument, IngredientInspection
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from datetime import datetime
import qrcode
from io import BytesIO

# 注册中文字体
def register_fonts():
    """注册中文字体"""
    try:
        # 使用应用字体目录中的字体文件
        font_path = os.path.join(current_app.root_path, 'static', 'fonts', 'simsun.ttf')

        if not os.path.exists(font_path):
            current_app.logger.error(f"Font file not found: {font_path}")
            raise FileNotFoundError(f"Font file not found: {font_path}")

        # 注册字体
        pdfmetrics.registerFont(TTFont('SimSun', font_path))
        current_app.logger.info(f"Successfully registered SimSun font from: {font_path}")

    except Exception as e:
        current_app.logger.error(f"Font registration failed: {str(e)}")
        raise

# 创建PDF保存目录
def create_pdf_dir(subdir='purchase_orders'):
    """创建PDF保存目录"""
    try:
        pdf_dir = os.path.join(current_app.root_path, 'static', 'pdf', subdir)
        if not os.path.exists(pdf_dir):
            os.makedirs(pdf_dir)
            current_app.logger.info(f"Created PDF directory: {pdf_dir}")
        return pdf_dir
    except Exception as e:
        current_app.logger.error(f"Failed to create PDF directory: {str(e)}")
        raise

def generate_purchase_order_pdf(order_id):
    """生成采购总单PDF"""
    try:
        # 注册字体
        register_fonts()

        # 获取采购订单信息
        order = PurchaseOrder.query.get_or_404(order_id)
        if not order:
            current_app.logger.error(f"Order not found: {order_id}")
            raise ValueError(f"Order not found: {order_id}")

        order_items = PurchaseOrderItem.query.filter_by(order_id=order_id).all()
        if not order_items:
            current_app.logger.warning(f"No items found for order: {order_id}")

        # 创建PDF保存目录
        pdf_dir = create_pdf_dir()

        # 生成PDF文件名
        filename = f"{order.area.name}第{order_id}批次食堂食材采购总单_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)

        current_app.logger.info(f"Generating PDF: {pdf_path}")

        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=1*cm,
            leftMargin=1*cm,
            topMargin=1*cm,
            bottomMargin=1*cm
        )

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建中文样式
        chinese_style = ParagraphStyle(
            'ChineseStyle',
            fontName='SimSun',
            fontSize=12,
            leading=14,
            alignment=1  # 居中
        )

        chinese_style_left = ParagraphStyle(
            'ChineseStyleLeft',
            fontName='SimSun',
            fontSize=12,
            leading=14,
            alignment=0  # 左对齐
        )

        chinese_style_title = ParagraphStyle(
            'ChineseStyleTitle',
            fontName='SimSun',
            fontSize=16,
            leading=18,
            alignment=1  # 居中
        )

        # 创建文档内容
        content = []

        # 添加标题
        title = Paragraph(f"{order.area.name}第{order_id}批次食堂食材采购总单", chinese_style_title)
        content.append(title)
        content.append(Spacer(1, 0.5*cm))

        # 添加订单信息
        order_info = [
            [Paragraph("订单编号:", chinese_style_left), Paragraph(order.order_number, chinese_style_left),
             Paragraph("供应商:", chinese_style_left), Paragraph(order.supplier.name, chinese_style_left)],
            [Paragraph("订单日期:", chinese_style_left), Paragraph(order.order_date.strftime("%Y-%m-%d"), chinese_style_left),
             Paragraph("预计送达日期:", chinese_style_left), Paragraph(order.expected_delivery_date.strftime("%Y-%m-%d") if order.expected_delivery_date else "", chinese_style_left)],
            [Paragraph("负责人:", chinese_style_left), Paragraph(order.creator.real_name or order.creator.username, chinese_style_left),
             Paragraph("总金额:", chinese_style_left), Paragraph(f"¥{order.total_amount:.2f}", chinese_style_left)]
        ]

        order_info_table = Table(order_info, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
        order_info_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        content.append(order_info_table)
        content.append(Spacer(1, 0.5*cm))

        # 添加订单明细
        data = [["序号", "食材名称", "数量", "单位", "单价", "金额", "备注"]]

        for i, item in enumerate(order_items, 1):
            data.append([
                str(i),
                item.ingredient.name,
                f"{item.quantity:.2f}",
                item.unit,
                f"¥{item.unit_price:.2f}",
                f"¥{item.total_price:.2f}",
                item.notes or ""
            ])

        # 添加合计行
        data.append([
            "",
            "合计",
            "",
            "",
            "",
            f"¥{order.total_amount:.2f}",
            ""
        ])

        # 创建表格
        table = Table(data, colWidths=[1.5*cm, 5*cm, 2*cm, 2*cm, 2*cm, 2*cm, 3*cm])
        table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -2), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (2, 1), (5, -1), 'RIGHT'),
            ('LINEBELOW', (0, -1), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
            ('FONT', (0, -1), (-1, -1), 'SimSun-Bold'),
        ]))

        content.append(table)
        content.append(Spacer(1, 1*cm))

        # 添加签名栏
        signature = [
            [Paragraph("采购人签名:", chinese_style_left), "", Paragraph("供应商签名:", chinese_style_left), ""],
            [Paragraph("日期:", chinese_style_left), "", Paragraph("日期:", chinese_style_left), ""]
        ]

        signature_table = Table(signature, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
        signature_table.setStyle(TableStyle([
            ('LINEBELOW', (1, 0), (1, 0), 0.5, colors.black),
            ('LINEBELOW', (3, 0), (3, 0), 0.5, colors.black),
            ('LINEBELOW', (1, 1), (1, 1), 0.5, colors.black),
            ('LINEBELOW', (3, 1), (3, 1), 0.5, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        content.append(signature_table)

        # 生成PDF
        doc.build(content)
        current_app.logger.info(f"Successfully generated PDF: {pdf_path}")

        # 返回相对路径
        return os.path.join('pdf', 'purchase_orders', filename)

    except Exception as e:
        current_app.logger.error(f"Failed to generate PDF for order {order_id}: {str(e)}")
        raise

def generate_supplier_order_pdf(order_id):
    """生成供应商采购单PDF"""
    # 注册字体
    register_fonts()

    # 获取采购订单信息
    order = PurchaseOrder.query.get_or_404(order_id)
    order_items = PurchaseOrderItem.query.filter_by(order_id=order_id).all()

    # 创建PDF保存目录
    pdf_dir = create_pdf_dir()

    # 生成PDF文件名
    filename = f"{order.supplier.name}采购单_{order.order_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
    pdf_path = os.path.join(pdf_dir, filename)

    # 创建PDF文档
    doc = SimpleDocTemplate(
        pdf_path,
        pagesize=A4,
        rightMargin=1*cm,
        leftMargin=1*cm,
        topMargin=1*cm,
        bottomMargin=1*cm
    )

    # 获取样式
    styles = getSampleStyleSheet()

    # 创建中文样式
    chinese_style = ParagraphStyle(
        'ChineseStyle',
        fontName='SimSun',
        fontSize=12,
        leading=14,
        alignment=1  # 居中
    )

    chinese_style_left = ParagraphStyle(
        'ChineseStyleLeft',
        fontName='SimSun',
        fontSize=12,
        leading=14,
        alignment=0  # 左对齐
    )

    chinese_style_title = ParagraphStyle(
        'ChineseStyleTitle',
        fontName='SimSun',
        fontSize=16,
        leading=18,
        alignment=1  # 居中
    )

    # 创建文档内容
    content = []

    # 添加标题
    title = Paragraph(f"{order.supplier.name}采购单", chinese_style_title)
    content.append(title)
    content.append(Spacer(1, 0.5*cm))

    # 添加订单信息
    order_info = [
        [Paragraph("订单编号:", chinese_style_left), Paragraph(order.order_number, chinese_style_left),
         Paragraph("采购单位:", chinese_style_left), Paragraph(order.area.name, chinese_style_left)],
        [Paragraph("订单日期:", chinese_style_left), Paragraph(order.order_date.strftime("%Y-%m-%d"), chinese_style_left),
         Paragraph("预计送达日期:", chinese_style_left), Paragraph(order.expected_delivery_date.strftime("%Y-%m-%d") if order.expected_delivery_date else "", chinese_style_left)],
        [Paragraph("联系人:", chinese_style_left), Paragraph(order.creator.real_name or order.creator.username, chinese_style_left),
         Paragraph("总金额:", chinese_style_left), Paragraph(f"¥{order.total_amount:.2f}", chinese_style_left)]
    ]

    order_info_table = Table(order_info, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
    order_info_table.setStyle(TableStyle([
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))

    content.append(order_info_table)
    content.append(Spacer(1, 0.5*cm))

    # 添加订单明细
    data = [["序号", "食材名称", "规格", "数量", "单位", "单价", "金额"]]

    for i, item in enumerate(order_items, 1):
        data.append([
            str(i),
            item.ingredient.name,
            item.product.specification if hasattr(item, 'product') and item.product else "",
            f"{item.quantity:.2f}",
            item.unit,
            f"¥{item.unit_price:.2f}",
            f"¥{item.total_price:.2f}"
        ])

    # 添加合计行
    data.append([
        "",
        "合计",
        "",
        "",
        "",
        "",
        f"¥{order.total_amount:.2f}"
    ])

    # 创建表格
    table = Table(data, colWidths=[1.5*cm, 4*cm, 3*cm, 2*cm, 2*cm, 2*cm, 2*cm])
    table.setStyle(TableStyle([
        ('FONT', (0, 0), (-1, -1), 'SimSun'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -2), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ALIGN', (3, 1), (6, -1), 'RIGHT'),
        ('LINEBELOW', (0, -1), (-1, -1), 1, colors.black),
        ('FONTSIZE', (0, -1), (-1, -1), 12),
        ('FONT', (0, -1), (-1, -1), 'SimSun-Bold'),
    ]))

    content.append(table)
    content.append(Spacer(1, 1*cm))

    # 添加签名栏
    signature = [
        [Paragraph("采购方签名:", chinese_style_left), "", Paragraph("供应商签名:", chinese_style_left), ""],
        [Paragraph("日期:", chinese_style_left), "", Paragraph("日期:", chinese_style_left), ""]
    ]

    signature_table = Table(signature, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
    signature_table.setStyle(TableStyle([
        ('LINEBELOW', (1, 0), (1, 0), 0.5, colors.black),
        ('LINEBELOW', (3, 0), (3, 0), 0.5, colors.black),
        ('LINEBELOW', (1, 1), (1, 1), 0.5, colors.black),
        ('LINEBELOW', (3, 1), (3, 1), 0.5, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))

    content.append(signature_table)

    # 添加备注
    if order.notes:
        content.append(Spacer(1, 0.5*cm))
        content.append(Paragraph("备注:", chinese_style_left))
        content.append(Paragraph(order.notes, chinese_style_left))

    # 生成PDF
    doc.build(content)

    # 返回相对路径
    return os.path.join('static', 'pdf', 'purchase_orders', filename)

# 生成二维码
def generate_qrcode(data, size=100):
    """生成二维码"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer)
        buffer.seek(0)

        return buffer
    except Exception as e:
        current_app.logger.error(f"Failed to generate QR code: {str(e)}")
        raise

# 生成入库报告
def generate_stock_in_report(stock_in_id):
    """生成入库单报告"""
    try:
        # 注册字体
        register_fonts()

        # 获取入库单信息
        stock_in = StockIn.query.get_or_404(stock_in_id)
        if not stock_in:
            current_app.logger.error(f"Stock in not found: {stock_in_id}")
            raise ValueError(f"Stock in not found: {stock_in_id}")

        # 获取入库明细
        stock_in_items = StockInItem.query.filter_by(stock_in_id=stock_in_id).all()
        if not stock_in_items:
            current_app.logger.warning(f"No items found for stock in: {stock_in_id}")

        # 创建PDF保存目录
        pdf_dir = create_pdf_dir('stock_ins')

        # 生成PDF文件名
        filename = f"入库单_{stock_in.stock_in_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)

        current_app.logger.info(f"Generating PDF: {pdf_path}")

        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=1*cm,
            leftMargin=1*cm,
            topMargin=1*cm,
            bottomMargin=1*cm
        )

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建中文样式
        chinese_style = ParagraphStyle(
            'ChineseStyle',
            fontName='SimSun',
            fontSize=12,
            leading=14,
            alignment=1  # 居中
        )

        chinese_style_left = ParagraphStyle(
            'ChineseStyleLeft',
            fontName='SimSun',
            fontSize=12,
            leading=14,
            alignment=0  # 左对齐
        )

        chinese_style_title = ParagraphStyle(
            'ChineseStyleTitle',
            fontName='SimSun',
            fontSize=16,
            leading=18,
            alignment=1  # 居中
        )

        # 创建文档内容
        content = []

        # 添加标题
        title = Paragraph("入库单", chinese_style_title)
        content.append(title)
        content.append(Spacer(1, 0.5*cm))

        # 添加入库单信息
        stock_in_info = [
            [Paragraph("入库单号:", chinese_style_left), Paragraph(stock_in.stock_in_number, chinese_style_left),
             Paragraph("仓库:", chinese_style_left), Paragraph(stock_in.warehouse.name, chinese_style_left)],
            [Paragraph("入库日期:", chinese_style_left), Paragraph(stock_in.stock_in_date.strftime("%Y-%m-%d"), chinese_style_left),
             Paragraph("入库类型:", chinese_style_left), Paragraph(stock_in.stock_in_type, chinese_style_left)],
            [Paragraph("供应商:", chinese_style_left), Paragraph(stock_in.supplier.name if stock_in.supplier else "-", chinese_style_left),
             Paragraph("操作人:", chinese_style_left), Paragraph(stock_in.operator.real_name or stock_in.operator.username, chinese_style_left)],
            [Paragraph("审批人:", chinese_style_left), Paragraph(stock_in.approver.real_name or stock_in.approver.username if stock_in.approver else "-", chinese_style_left),
             Paragraph("状态:", chinese_style_left), Paragraph(stock_in.status, chinese_style_left)]
        ]

        stock_in_info_table = Table(stock_in_info, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
        stock_in_info_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        content.append(stock_in_info_table)
        content.append(Spacer(1, 0.5*cm))

        # 添加入库明细
        data = [["序号", "食材名称", "存储位置", "批次号", "数量", "单位", "生产日期", "过期日期", "单价"]]

        for i, item in enumerate(stock_in_items, 1):
            data.append([
                str(i),
                item.ingredient.name,
                f"{item.storage_location.name} ({item.storage_location.location_code})",
                item.batch_number,
                f"{item.quantity:.2f}",
                item.unit,
                item.production_date.strftime("%Y-%m-%d") if item.production_date else "-",
                item.expiry_date.strftime("%Y-%m-%d") if item.expiry_date else "-",
                f"¥{item.unit_price:.2f}" if item.unit_price else "-"
            ])

        # 创建表格
        table = Table(data, colWidths=[1.5*cm, 4*cm, 3*cm, 2.5*cm, 2*cm, 1.5*cm, 2*cm, 2*cm, 2*cm])
        table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (4, 1), (8, -1), 'RIGHT'),
        ]))

        content.append(table)
        content.append(Spacer(1, 1*cm))

        # 添加签名栏
        signature = [
            [Paragraph("操作人签名:", chinese_style_left), "", Paragraph("审批人签名:", chinese_style_left), ""],
            [Paragraph("日期:", chinese_style_left), "", Paragraph("日期:", chinese_style_left), ""]
        ]

        signature_table = Table(signature, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
        signature_table.setStyle(TableStyle([
            ('LINEBELOW', (1, 0), (1, 0), 0.5, colors.black),
            ('LINEBELOW', (3, 0), (3, 0), 0.5, colors.black),
            ('LINEBELOW', (1, 1), (1, 1), 0.5, colors.black),
            ('LINEBELOW', (3, 1), (3, 1), 0.5, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        content.append(signature_table)

        # 生成PDF
        doc.build(content)

        return pdf_path

    except Exception as e:
        current_app.logger.error(f"Failed to generate stock in report: {str(e)}")
        raise
