"""
添加用户自定义食谱字段的数据库迁移脚本
此脚本用于向recipes表添加is_user_defined和priority字段
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入应用和数据库
from app import create_app, db
from sqlalchemy import text

def upgrade():
    """执行数据库升级"""
    print("开始添加用户自定义食谱字段...")

    # 创建应用上下文
    app = create_app()

    with app.app_context():
        try:
            # 检查字段是否已存在
            inspector = db.inspect(db.engine)
            try:
                columns = [c['name'] for c in inspector.get_columns('recipes')]
                print(f"当前表字段: {columns}")
            except Exception as e:
                print(f"获取表字段时出错: {e}")
                columns = []

            # 添加is_user_defined字段
            if 'is_user_defined' not in columns:
                try:
                    db.session.execute(text("ALTER TABLE recipes ADD is_user_defined BIT NOT NULL DEFAULT 0"))
                    print("已添加字段: is_user_defined")
                except Exception as e:
                    print(f"添加is_user_defined字段时出错: {e}")

            # 添加priority字段
            if 'priority' not in columns:
                try:
                    db.session.execute(text("ALTER TABLE recipes ADD priority INT NOT NULL DEFAULT 0"))
                    print("已添加字段: priority")
                except Exception as e:
                    print(f"添加priority字段时出错: {e}")

            # 提交更改
            db.session.commit()
            print("用户自定义食谱字段添加完成！")

        except Exception as e:
            print(f"执行迁移时出错: {e}")
            db.session.rollback()

def downgrade():
    """执行数据库降级（删除添加的字段）"""
    print("开始移除用户自定义食谱字段...")

    # 创建应用上下文
    app = create_app()

    with app.app_context():
        try:
            # SQL Server支持直接删除列
            db.session.execute(text("""
            IF EXISTS (SELECT * FROM sys.columns WHERE Name = 'is_user_defined' AND Object_ID = Object_ID('recipes'))
            BEGIN
                ALTER TABLE recipes DROP COLUMN is_user_defined;
                PRINT '已删除字段: is_user_defined';
            END
            """))

            db.session.execute(text("""
            IF EXISTS (SELECT * FROM sys.columns WHERE Name = 'priority' AND Object_ID = Object_ID('recipes'))
            BEGIN
                ALTER TABLE recipes DROP COLUMN priority;
                PRINT '已删除字段: priority';
            END
            """))

            db.session.commit()
            print("用户自定义食谱字段移除完成！")

        except Exception as e:
            print(f"执行降级时出错: {e}")
            db.session.rollback()

if __name__ == '__main__':
    # 如果直接运行此脚本，则执行升级
    upgrade()
