<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>递归修复测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background-color: #e9f7fe;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 10px;
            pointer-events: none;
        }
        
        .upload-area:hover .upload-icon {
            color: #007bff;
        }
        
        .upload-area p {
            pointer-events: none;
            margin: 0;
        }
        
        .upload-area input[type="file"] {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            opacity: 0 !important;
            cursor: pointer !important;
            z-index: 2 !important;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>递归修复测试页面</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>文件上传测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="dropZone">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <p>点击或拖拽文件到此处上传</p>
                            <p class="text-muted">支持 PDF, JPG, PNG 格式文件</p>
                            <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png">
                        </div>
                        
                        <div id="fileList" class="mt-3"></div>
                        
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary" id="saveDocuments">保存文档</button>
                            <button type="button" class="btn btn-secondary" id="testButton">测试按钮</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>调试控制</h6>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-info btn-sm btn-block" onclick="checkBindings()">检查事件绑定</button>
                        <button type="button" class="btn btn-warning btn-sm btn-block" onclick="cleanupBindings()">清理重复绑定</button>
                        <button type="button" class="btn btn-success btn-sm btn-block" onclick="rebindEvents()">重新绑定事件</button>
                        <button type="button" class="btn btn-danger btn-sm btn-block" onclick="triggerRecursion()">触发递归测试</button>
                        
                        <hr>
                        <div id="debugInfo" class="small"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
        <h6>调试信息</h6>
        <div id="debugOutput" class="small"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="debug_recursion.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('页面加载完成，开始初始化...');
            
            // 使用安全的事件绑定
            initSafeEventBindings();
            
            // 更新调试信息
            updateDebugInfo();
        });
        
        function initSafeEventBindings() {
            console.log('初始化安全事件绑定...');
            
            // 使用递归调试器的安全绑定方法
            window.RecursionDebugger.safeJQueryBind('#dropZone', 'click', 'fileUpload', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('点击上传区域');
                document.getElementById('fileInput').click();
            });
            
            window.RecursionDebugger.safeJQueryBind('#fileInput', 'change', 'fileUpload', function(e) {
                console.log('文件选择变化', this.files);
                if (this.files && this.files.length > 0) {
                    handleFiles(this.files);
                }
            });
            
            window.RecursionDebugger.safeJQueryBind('#saveDocuments', 'click', 'saveAction', function(e) {
                console.log('保存文档');
                alert('文档保存功能');
            });
            
            window.RecursionDebugger.safeJQueryBind('#testButton', 'click', 'testAction', function(e) {
                console.log('测试按钮点击');
                updateDebugInfo();
            });
            
            // 使用事件委托
            window.RecursionDebugger.safeJQueryDelegate(document, '.remove-file', 'click', 'removeFile', function(e) {
                console.log('删除文件');
                $(this).closest('.alert').remove();
            });
        }
        
        function handleFiles(files) {
            console.log('处理文件:', files);
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileItem = document.createElement('div');
                fileItem.className = 'alert alert-info d-flex justify-content-between align-items-center';
                
                const fileIcon = file.type.includes('pdf') ? 'fa-file-pdf' : 'fa-file-image';
                const fileSize = file.size > 1024 * 1024 ? 
                    (file.size / (1024 * 1024)).toFixed(2) + ' MB' : 
                    (file.size / 1024).toFixed(2) + ' KB';
                
                fileItem.innerHTML = `
                    <div>
                        <i class="fas ${fileIcon} mr-2"></i>
                        ${file.name} (${fileSize})
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                fileList.appendChild(fileItem);
            }
        }
        
        function checkBindings() {
            window.RecursionDebugger.checkExistingBindings();
            updateDebugInfo();
        }
        
        function cleanupBindings() {
            window.RecursionDebugger.cleanupDuplicateBindings();
            updateDebugInfo();
        }
        
        function rebindEvents() {
            cleanupBindings();
            setTimeout(function() {
                initSafeEventBindings();
                updateDebugInfo();
            }, 100);
        }
        
        function triggerRecursion() {
            console.log('触发递归测试...');
            // 故意创建一个可能导致递归的情况
            for (let i = 0; i < 10; i++) {
                $('#testButton').trigger('click');
            }
        }
        
        function updateDebugInfo() {
            const debugInfo = window.RecursionDebugger.getDebugInfo();
            const debugOutput = document.getElementById('debugOutput');
            const debugInfoDiv = document.getElementById('debugInfo');
            
            const info = `
                <strong>调用栈深度:</strong> ${debugInfo.callStackDepth}<br>
                <strong>事件绑定计数:</strong><br>
                ${Object.entries(debugInfo.eventBindingCounts).map(([key, count]) => 
                    `&nbsp;&nbsp;${key}: ${count}`
                ).join('<br>')}<br>
                <strong>更新时间:</strong> ${new Date(debugInfo.timestamp).toLocaleTimeString()}
            `;
            
            debugOutput.innerHTML = info;
            debugInfoDiv.innerHTML = info;
        }
        
        // 定期更新调试信息
        setInterval(updateDebugInfo, 2000);
    </script>
</body>
</html>
