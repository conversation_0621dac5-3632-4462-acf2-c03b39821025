# 文件上传和CSP问题解决方案

## 问题描述

项目中遇到了两个主要问题：

1. **Content Security Policy (CSP) 错误**：浏览器阻止了 JavaScript 中 'eval' 的使用
2. **文件上传功能问题**：点击文件上传按钮后没有弹出文件选择对话框

## 解决方案

### 1. CSP (Content Security Policy) 修复

#### 问题原因
- 浏览器的默认CSP策略阻止了某些JavaScript功能的执行
- 特别是 `eval()` 函数和动态脚本执行被阻止

#### 解决方法
在 `app/__init__.py` 中添加了适当的CSP头：

```python
# 添加Content Security Policy头，允许必要的JavaScript功能
csp_policy = (
    "default-src 'self'; "
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
    "style-src 'self' 'unsafe-inline'; "
    "img-src 'self' data: blob:; "
    "font-src 'self' data:; "
    "connect-src 'self'; "
    "media-src 'self'; "
    "object-src 'none'; "
    "base-uri 'self'; "
    "form-action 'self'"
)
response.headers['Content-Security-Policy'] = csp_policy
```

#### CSP策略说明
- `script-src 'self' 'unsafe-inline' 'unsafe-eval'`：允许同源脚本、内联脚本和eval函数
- `style-src 'self' 'unsafe-inline'`：允许同源样式和内联样式
- `img-src 'self' data: blob:`：允许同源图片、data URL和blob URL
- `font-src 'self' data:`：允许同源字体和data URL字体
- `connect-src 'self'`：允许同源AJAX请求
- `object-src 'none'`：禁止嵌入对象
- `base-uri 'self'`：限制base标签的URL
- `form-action 'self'`：限制表单提交到同源

### 2. 文件上传功能修复

#### 问题原因
- Bootstrap自定义文件上传组件的CSS和JavaScript冲突
- 文件输入元素被隐藏或无法正确触发点击事件
- z-index层级问题导致点击事件无法传递

#### 解决方法

##### 2.1 创建通用修复脚本
创建了 `app/static/js/file-upload-fix.js` 文件，包含：

- 自动检测和修复所有文件上传元素
- 支持Bootstrap自定义文件上传和标准文件上传
- 动态监听DOM变化，修复新添加的文件上传元素
- 添加必要的CSS样式确保正确显示和交互

##### 2.2 修复具体页面
在 `app/templates/trace_document/upload.html` 中：

- 重写了文件选择的JavaScript逻辑
- 添加了专门的CSS样式
- 确保文件输入元素正确响应点击事件
- 改进了文件名显示和验证逻辑

##### 2.3 全局引入修复脚本
在 `app/templates/base.html` 中引入了修复脚本：

```html
<script src="{{ url_for('static', filename='js/file-upload-fix.js') }}?v=1.0.0"></script>
```

## 技术细节

### 文件上传修复的关键点

1. **CSS样式修复**：
   ```css
   .custom-file-input {
       position: absolute !important;
       opacity: 0 !important;
       width: 100% !important;
       height: 100% !important;
       cursor: pointer !important;
       z-index: 1 !important;
   }
   ```

2. **JavaScript事件处理**：
   ```javascript
   // 点击容器时触发文件选择
   container.addEventListener('click', function(e) {
       e.preventDefault();
       e.stopPropagation();
       fileInput.click();
   });
   ```

3. **动态监听**：
   ```javascript
   const observer = new MutationObserver(function(mutations) {
       // 监听DOM变化，自动修复新添加的文件上传元素
   });
   ```

### CSP安全考虑

虽然允许了 `unsafe-eval` 和 `unsafe-inline`，但这是为了兼容现有的JavaScript库和框架。在生产环境中，建议：

1. 逐步移除对 `eval()` 的依赖
2. 将内联脚本移到外部文件
3. 使用nonce或hash来允许特定的内联脚本
4. 定期审查和更新CSP策略

## 测试验证

### 文件上传测试
1. 访问文件上传页面
2. 点击"选择文件"按钮
3. 确认文件选择对话框正常弹出
4. 选择文件后确认文件名正确显示
5. 提交表单确认文件正常上传

### CSP测试
1. 打开浏览器开发者工具
2. 查看Console标签
3. 确认没有CSP相关的错误信息
4. 测试页面的JavaScript功能正常工作

## 维护建议

1. **定期检查**：定期检查是否有新的文件上传组件需要修复
2. **CSP优化**：逐步优化CSP策略，提高安全性
3. **兼容性测试**：在不同浏览器中测试文件上传功能
4. **性能监控**：监控修复脚本对页面性能的影响

## 特定页面修复

### 入库单创建页面修复

针对 `http://127.0.0.1:5000/stock-in/create-from-purchase-order/40` 页面的特殊修复：

#### 修复内容
1. **文件上传区域样式优化**：
   - 改进了 `.upload-area` 的CSS样式
   - 确保文件输入元素正确覆盖整个拖拽区域
   - 添加了 `pointer-events: none` 防止子元素干扰点击事件

2. **JavaScript事件处理改进**：
   - 修复了点击拖拽区域触发文件选择的逻辑
   - 添加了详细的控制台日志用于调试
   - 改进了文件验证和错误处理

3. **文件处理功能增强**：
   - 添加了文件类型和大小验证
   - 改进了文件显示格式（MB/KB自动转换）
   - 增强了用户反馈和错误提示

4. **上传功能模拟**：
   - 实现了完整的文件上传流程模拟
   - 添加了上传进度指示
   - 提供了实际AJAX上传的代码示例

#### 测试方法
使用专门的测试脚本：
```bash
python test_stock_in_upload.py
```

## 相关文件

- `app/__init__.py`：CSP策略配置
- `app/static/js/file-upload-fix.js`：通用文件上传修复脚本
- `app/templates/base.html`：全局模板，引入修复脚本
- `app/templates/trace_document/upload.html`：示例修复页面
- `app/templates/stock_in/create_from_purchase_order.html`：入库单创建页面修复
- `test_stock_in_upload.py`：入库单页面专用测试脚本
- `docs/file-upload-csp-fix.md`：本文档
