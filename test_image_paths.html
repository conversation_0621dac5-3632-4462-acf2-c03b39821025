<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片路径测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-item h3 { margin-top: 0; color: #333; }
        .test-item img { max-width: 300px; max-height: 200px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>图片路径测试</h1>
    
    <div class="test-item">
        <h3>测试1: 直接访问图片URL</h3>
        <p>URL: <a href="http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/20250525164358_1.png" target="_blank">http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/20250525164358_1.png</a></p>
        <img src="http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/20250525164358_1.png" 
             alt="测试图片1" 
             onload="this.nextElementSibling.innerHTML='<span class=&quot;success&quot;>✓ 图片加载成功</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=&quot;error&quot;>✗ 图片加载失败</span>'">
        <p>加载状态: <span>加载中...</span></p>
    </div>
    
    <div class="test-item">
        <h3>测试2: 第二张图片</h3>
        <p>URL: <a href="http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/20250525170921_2.png" target="_blank">http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/20250525170921_2.png</a></p>
        <img src="http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/20250525170921_2.png" 
             alt="测试图片2" 
             onload="this.nextElementSibling.innerHTML='<span class=&quot;success&quot;>✓ 图片加载成功</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=&quot;error&quot;>✗ 图片加载失败</span>'">
        <p>加载状态: <span>加载中...</span></p>
    </div>
    
    <div class="test-item">
        <h3>测试3: 错误的路径（用于测试错误处理）</h3>
        <p>URL: <a href="http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/nonexistent.png" target="_blank">http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/nonexistent.png</a></p>
        <img src="http://127.0.0.1:5000/static/uploads/stock_in_docs/stock_in_76/nonexistent.png" 
             alt="不存在的图片" 
             onload="this.nextElementSibling.innerHTML='<span class=&quot;success&quot;>✓ 图片加载成功</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=&quot;error&quot;>✗ 图片加载失败（预期结果）</span>'">
        <p>加载状态: <span>加载中...</span></p>
    </div>
    
    <script>
        // 添加一些调试信息
        console.log('页面加载完成，开始测试图片路径');
        
        // 监听所有图片的加载事件
        document.querySelectorAll('img').forEach((img, index) => {
            img.addEventListener('load', function() {
                console.log(`图片 ${index + 1} 加载成功:`, this.src);
            });
            
            img.addEventListener('error', function() {
                console.log(`图片 ${index + 1} 加载失败:`, this.src);
            });
        });
    </script>
</body>
</html>
