from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from sqlalchemy import text
import os
import uuid
from datetime import datetime, timedelta
import json  # 用于处理JSON数据

from app import db
from flask_login import login_required
from app.utils.file_utils import allowed_file

# 创建蓝图
bp = Blueprint('daily_management_enhanced_api', __name__, url_prefix='/daily-management')

# 照片API路由
@bp.route('/image-api/photos/<reference_type>/<int:reference_id>', methods=['GET', 'POST'])
@login_required
def handle_photos(reference_type, reference_id):
    """处理照片上传和获取"""
    if request.method == 'GET':
        # 获取照片列表
        try:
            sql = text("""
                SELECT id, file_name, file_path, description, rating, upload_time
                FROM photos
                WHERE reference_type = :reference_type AND reference_id = :reference_id
                ORDER BY upload_time DESC
            """)

            result = db.session.execute(sql, {
                'reference_type': reference_type,
                'reference_id': reference_id
            })

            photos = []
            for row in result:
                photos.append({
                    'id': row.id,
                    'file_name': row.file_name,
                    'file_path': row.file_path,
                    'description': row.description,
                    'rating': row.rating,
                    'upload_time': row.upload_time.strftime('%Y-%m-%d %H:%M:%S') if row.upload_time else None
                })

            return jsonify({
                'success': True,
                'photos': photos
            })

        except Exception as e:
            current_app.logger.error(f"获取照片失败: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    elif request.method == 'POST':
        # 上传照片
        try:
            # 检查是否有文件
            if 'photo' not in request.files:
                return jsonify({
                    'success': False,
                    'error': '没有上传文件'
                }), 400

            file = request.files['photo']

            # 检查文件名
            if file.filename == '':
                return jsonify({
                    'success': False,
                    'error': '没有选择文件'
                }), 400

            # 检查文件类型
            if not allowed_file(file.filename, ['jpg', 'jpeg', 'png', 'gif']):
                return jsonify({
                    'success': False,
                    'error': '不支持的文件类型'
                }), 400

            # 生成安全的文件名
            filename = secure_filename(file.filename)
            # 添加UUID前缀，避免文件名冲突
            unique_filename = f"{uuid.uuid4().hex}_{filename}"

            # 确保上传目录存在
            upload_folder = os.path.join(
                current_app.static_folder,
                'uploads',
                'daily_management',
                reference_type
            )
            os.makedirs(upload_folder, exist_ok=True)

            # 保存文件
            file_path = os.path.join(upload_folder, unique_filename)
            file.save(file_path)

            # 获取相对路径，用于数据库存储和前端显示
            relative_path = f"/static/uploads/daily_management/{reference_type}/{unique_filename}"

            # 获取描述
            description = request.form.get('description', '')

            # 插入数据库
            sql = text("""
                INSERT INTO photos (reference_type, reference_id, file_name, file_path, description, upload_time)
                OUTPUT inserted.id
                VALUES (:reference_type, :reference_id, :file_name, :file_path, :description, :upload_time)
            """)

            result = db.session.execute(sql, {
                'reference_type': reference_type,
                'reference_id': reference_id,
                'file_name': filename,
                'file_path': relative_path,
                'description': description,
                'upload_time': datetime.now().replace(microsecond=0)
            })

            photo_id = result.fetchone()[0]
            db.session.commit()

            return jsonify({
                'success': True,
                'id': photo_id,
                'file_path': relative_path
            })

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"上传照片失败: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

@bp.route('/image-api/photos/<int:photo_id>', methods=['DELETE'])
@login_required
def delete_photo(photo_id):
    """删除照片"""
    try:
        # 获取照片信息
        sql = text("""
            SELECT file_path
            FROM photos
            WHERE id = :photo_id
        """)

        result = db.session.execute(sql, {'photo_id': photo_id})
        photo = result.fetchone()

        if not photo:
            return jsonify({
                'success': False,
                'error': '照片不存在'
            }), 404

        # 删除文件
        file_path = os.path.join(current_app.root_path, '..', photo.file_path.lstrip('/'))
        if os.path.exists(file_path):
            os.remove(file_path)

        # 从数据库中删除
        sql = text("""
            DELETE FROM photos
            WHERE id = :photo_id
        """)

        db.session.execute(sql, {'photo_id': photo_id})
        db.session.commit()

        return jsonify({
            'success': True
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除照片失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/image-api/photos/<int:photo_id>/rating', methods=['POST'])
@login_required
def update_photo_rating(photo_id):
    """更新照片评分"""
    try:
        # 获取评分
        data = request.get_json()
        if not data or 'rating' not in data:
            return jsonify({
                'success': False,
                'error': '缺少评分参数'
            }), 400

        rating = data['rating']

        # 验证评分范围
        if not isinstance(rating, int) or rating < 1 or rating > 5:
            return jsonify({
                'success': False,
                'error': '评分必须是1-5之间的整数'
            }), 400

        # 更新评分
        sql = text("""
            UPDATE photos
            SET rating = :rating
            WHERE id = :photo_id
        """)

        db.session.execute(sql, {
            'photo_id': photo_id,
            'rating': rating
        })
        db.session.commit()

        return jsonify({
            'success': True
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新照片评分失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 数据统计API路由
@bp.route('/api/v2/statistics/daily', methods=['GET'])
@login_required
def get_daily_statistics():
    """获取每日统计数据"""
    try:
        # 获取日期范围
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        # 查询每日就餐人数
        sql = text("""
            SELECT
                CONVERT(VARCHAR(10), log_date, 120) AS date,
                student_count,
                teacher_count,
                other_count
            FROM daily_logs
            WHERE log_date BETWEEN :start_date AND :end_date
            ORDER BY log_date
        """)

        result = db.session.execute(sql, {
            'start_date': start_date,
            'end_date': end_date
        })

        # 处理结果
        daily_counts = {}
        for row in result:
            daily_counts[row.date] = {
                'student': row.student_count,
                'teacher': row.teacher_count,
                'other': row.other_count,
                'total': row.student_count + row.teacher_count + row.other_count
            }

        return jsonify({
            'success': True,
            'daily_counts': daily_counts,
            'date_range': {
                'start': start_date,
                'end': end_date
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取每日统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/api/v2/statistics/inspections', methods=['GET'])
@login_required
def get_inspection_statistics():
    """获取检查记录统计数据"""
    try:
        # 获取日期范围
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        # 查询检查记录
        sql = text("""
            SELECT
                ir.inspection_type,
                ir.status,
                COUNT(*) AS count
            FROM inspection_records ir
            JOIN daily_logs dl ON ir.daily_log_id = dl.id
            WHERE dl.log_date BETWEEN :start_date AND :end_date
            GROUP BY ir.inspection_type, ir.status
        """)

        result = db.session.execute(sql, {
            'start_date': start_date,
            'end_date': end_date
        })

        # 处理结果
        inspection_counts = {
            'morning': {'normal': 0, 'abnormal': 0},
            'noon': {'normal': 0, 'abnormal': 0},
            'evening': {'normal': 0, 'abnormal': 0}
        }

        for row in result:
            inspection_type = row.inspection_type
            status = 'normal' if row.status == 'normal' else 'abnormal'
            count = row.count

            if inspection_type in inspection_counts:
                inspection_counts[inspection_type][status] = count

        return jsonify({
            'success': True,
            'inspection_counts': inspection_counts,
            'date_range': {
                'start': start_date,
                'end': end_date
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取检查记录统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
