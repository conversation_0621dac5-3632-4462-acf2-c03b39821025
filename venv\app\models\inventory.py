from app import db
from datetime import datetime
from sqlalchemy.dialects.mssql import DATETIME2

class Warehouse(db.Model):
    """仓库模型"""
    __tablename__ = 'warehouses'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(255))
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    status = db.Column(db.String(20), default='active')  # active, inactive
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0))

    # 关联
    manager = db.relationship('User', backref='managed_warehouses')
    storage_locations = db.relationship('StorageLocation', backref='warehouse', lazy='dynamic')

    def __repr__(self):
        return f'<Warehouse {self.name}>'

class StorageLocation(db.Model):
    """存储位置模型"""
    __tablename__ = 'storage_locations'

    id = db.Column(db.Integer, primary_key=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(255))
    temperature = db.Column(db.Float)  # 存储温度
    humidity = db.Column(db.Float)  # 存储湿度
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0))

    def __repr__(self):
        return f'<StorageLocation {self.name}>'

class Inventory(db.Model):
    """库存模型"""
    __tablename__ = 'inventories'

    id = db.Column(db.Integer, primary_key=True)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    location_id = db.Column(db.Integer, db.ForeignKey('storage_locations.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    quantity = db.Column(db.Numeric(10, 2), nullable=False)  # 当前库存量
    unit = db.Column(db.String(20), nullable=False)  # 计量单位
    min_quantity = db.Column(db.Numeric(10, 2))  # 最小库存量
    max_quantity = db.Column(db.Numeric(10, 2))  # 最大库存量
    batch_number = db.Column(db.String(50), nullable=True)
    production_date = db.Column(db.Date, nullable=True)
    expiry_date = db.Column(db.Date, nullable=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)
    status = db.Column(db.String(20), default='正常', nullable=False)  # 正常/已用完/已过期
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关联
    ingredient = db.relationship('Ingredient', backref='inventories')
    location = db.relationship('StorageLocation', backref='inventories')
    warehouse = db.relationship('Warehouse', backref='inventories')
    supplier = db.relationship('Supplier', backref='inventories')

    def __repr__(self):
        return f'<Inventory {self.ingredient_id}>'

class InventoryAlert(db.Model):
    """库存预警模型"""
    __tablename__ = 'inventory_alerts'

    id = db.Column(db.Integer, primary_key=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    alert_type = db.Column(db.String(20), nullable=False)  # 'low_stock', 'expiring', 'expired'
    threshold = db.Column(db.Float)  # 预警阈值
    current_value = db.Column(db.Float)  # 当前值
    status = db.Column(db.String(20), nullable=False, default='active')  # active, acknowledged, resolved
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)
    acknowledged_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    acknowledged_at = db.Column(DATETIME2(precision=1))

    # 关系
    warehouse = db.relationship('Warehouse', backref='inventory_alerts')
    ingredient = db.relationship('Ingredient', backref='inventory_alerts')
    acknowledger = db.relationship('User', foreign_keys=[acknowledged_by], backref='acknowledged_alerts')

    def __repr__(self):
        return f'<InventoryAlert {self.id}: {self.alert_type}>'

class StockIn(db.Model):
    """入库记录模型"""
    __tablename__ = 'stock_ins'

    id = db.Column(db.Integer, primary_key=True)
    stock_in_number = db.Column(db.String(50), nullable=False, unique=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    delivery_id = db.Column(db.Integer, db.ForeignKey('supplier_deliveries.id'), nullable=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=True)  # 关联采购订单
    stock_in_date = db.Column(DATETIME2(precision=1), nullable=False)
    stock_in_type = db.Column(db.String(20), nullable=False)  # 采购入库/调拨入库/退货入库
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    inspector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='待审核')  # 待审核/已审核/已入库/已取消
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    operator = db.relationship('User', foreign_keys=[operator_id], backref='operated_stock_ins')
    inspector = db.relationship('User', foreign_keys=[inspector_id], backref='inspected_stock_ins')
    delivery = db.relationship('SupplierDelivery')
    purchase_order = db.relationship('PurchaseOrder', backref='stock_ins')  # 关联采购订单
    stock_in_items = db.relationship('StockInItem', backref='stock_in', lazy='dynamic')

    def __repr__(self):
        return f'<StockIn {self.id}>'

class StockInItem(db.Model):
    """入库明细表"""
    __tablename__ = 'stock_in_items'

    id = db.Column(db.Integer, primary_key=True)
    stock_in_id = db.Column(db.Integer, db.ForeignKey('stock_ins.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    batch_number = db.Column(db.String(50), nullable=False)  # 批次号
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    production_date = db.Column(db.Date, nullable=False)
    expiry_date = db.Column(db.Date, nullable=False)
    storage_location_id = db.Column(db.Integer, db.ForeignKey('storage_locations.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)
    purchase_order_item_id = db.Column(db.Integer, db.ForeignKey('purchase_order_items.id'), nullable=True)  # 关联采购订单明细
    quality_check_result = db.Column(db.String(20), nullable=True)  # 合格/不合格
    quality_check_notes = db.Column(db.Text, nullable=True)
    quality_status = db.Column(db.String(20), default='良好', nullable=False)  # 质量状态：良好/一般/较差
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    ingredient = db.relationship('Ingredient')
    storage_location = db.relationship('StorageLocation')
    supplier = db.relationship('Supplier')
    purchase_order_item = db.relationship('PurchaseOrderItem', backref='stock_in_items')

    def __repr__(self):
        return f'<StockInItem {self.id}>'

class StockOut(db.Model):
    """出库记录模型"""
    __tablename__ = 'stock_outs'

    id = db.Column(db.Integer, primary_key=True)
    stock_out_number = db.Column(db.String(50), nullable=False, unique=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    consumption_plan_id = db.Column(db.Integer, db.ForeignKey('consumption_plans.id'), nullable=True)
    stock_out_date = db.Column(DATETIME2(precision=1), nullable=False)
    stock_out_type = db.Column(db.String(20), nullable=False)  # 消耗出库/报废出库/调拨出库
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='待审核')  # 待审核/已审核/已出库/已取消
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    operator = db.relationship('User', foreign_keys=[operator_id], backref='operated_stock_outs')
    recipient = db.relationship('User', foreign_keys=[recipient_id], backref='received_stock_outs')
    consumption_plan = db.relationship('ConsumptionPlan')  # 移除backref以避免冲突
    items = db.relationship('StockOutItem', backref='stock_out', lazy='dynamic')

    def __repr__(self):
        return f'<StockOut {self.id}>'

class StockOutItem(db.Model):
    """出库明细表"""
    __tablename__ = 'stock_out_items'

    id = db.Column(db.Integer, primary_key=True)
    stock_out_id = db.Column(db.Integer, db.ForeignKey('stock_outs.id'), nullable=False)
    inventory_id = db.Column(db.Integer, db.ForeignKey('inventories.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    batch_number = db.Column(db.String(50), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    inventory = db.relationship('Inventory', backref='stock_out_items')
    ingredient = db.relationship('Ingredient', backref='stock_out_items')

    def __repr__(self):
        return f'<StockOutItem {self.id}>'