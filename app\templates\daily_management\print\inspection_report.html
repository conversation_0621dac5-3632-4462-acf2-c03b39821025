{% extends 'daily_management/print/base_print.html' %}

{% block title %}食堂检查记录 - {{ log.log_date|format_datetime('%Y-%m-%d') }}{% endblock %}

{% block document_title %}食堂检查记录{% endblock %}

{% block document_subtitle %}{{ log.log_date|format_datetime('%Y年%m月%d日') }}{% endblock %}

{% block document_info %}
<div class="info-row">
    <div class="info-label">日期：</div>
    <div class="info-value">{{ log.log_date|format_datetime('%Y-%m-%d') }}</div>
</div>
<div class="info-row">
    <div class="info-label">管理员：</div>
    <div class="info-value">{{ log.manager or '未设置' }}</div>
</div>
<div class="info-row">
    <div class="info-label">天气：</div>
    <div class="info-value">{{ log.weather or '未记录' }}</div>
</div>
<div class="info-row">
    <div class="info-label">就餐人数：</div>
    <div class="info-value">学生：{{ log.student_count or 0 }}人，教师：{{ log.teacher_count or 0 }}人，其他：{{ log.other_count or 0 }}人，共{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}人</div>
</div>
{% endblock %}

{% block content %}
<!-- 晨检记录 -->
<div class="section-title">晨检记录</div>
{% if morning_inspections %}
<table>
    <thead>
        <tr>
            <th width="20%">检查项目</th>
            <th width="50%">检查内容</th>
            <th width="15%">检查人</th>
            <th width="15%">检查时间</th>
        </tr>
    </thead>
    <tbody>
        {% for inspection in morning_inspections %}
        <tr>
            <td>{{ inspection.inspection_item }}</td>
            <td>{{ inspection.description }}</td>
            <td>{{ inspection.inspector.real_name if inspection.inspector else '未知' }}</td>
            <td>{{ inspection.inspection_time|format_datetime('%H:%M') if inspection.inspection_time else '' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<p>暂无晨检记录</p>
{% endif %}

<!-- 午检记录 -->
<div class="section-title">午检记录</div>
{% if noon_inspections %}
<table>
    <thead>
        <tr>
            <th width="20%">检查项目</th>
            <th width="50%">检查内容</th>
            <th width="15%">检查人</th>
            <th width="15%">检查时间</th>
        </tr>
    </thead>
    <tbody>
        {% for inspection in noon_inspections %}
        <tr>
            <td>{{ inspection.inspection_item }}</td>
            <td>{{ inspection.description }}</td>
            <td>{{ inspection.inspector.real_name if inspection.inspector else '未知' }}</td>
            <td>{{ inspection.inspection_time|format_datetime('%H:%M') if inspection.inspection_time else '' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<p>暂无午检记录</p>
{% endif %}

<!-- 晚检记录 -->
<div class="section-title">晚检记录</div>
{% if evening_inspections %}
<table>
    <thead>
        <tr>
            <th width="20%">检查项目</th>
            <th width="50%">检查内容</th>
            <th width="15%">检查人</th>
            <th width="15%">检查时间</th>
        </tr>
    </thead>
    <tbody>
        {% for inspection in evening_inspections %}
        <tr>
            <td>{{ inspection.inspection_item }}</td>
            <td>{{ inspection.description }}</td>
            <td>{{ inspection.inspector.real_name if inspection.inspector else '未知' }}</td>
            <td>{{ inspection.inspection_time|format_datetime('%H:%M') if inspection.inspection_time else '' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<p>暂无晚检记录</p>
{% endif %}

<!-- 检查照片 -->
{% set has_photos = false %}
{% for inspection_list in [morning_inspections, noon_inspections, evening_inspections] %}
    {% for inspection in inspection_list %}
        {% if inspection.photos and inspection.photos|length > 0 %}
            {% set has_photos = true %}
        {% endif %}
    {% endfor %}
{% endfor %}

{% if has_photos %}
<div class="section-title">检查照片</div>
<div class="photo-container">
    {% for inspection_list in [morning_inspections, noon_inspections, evening_inspections] %}
        {% for inspection in inspection_list %}
            {% if inspection.photos and inspection.photos|length > 0 %}
                {% for photo in inspection.photos %}
                <div class="photo-item">
                    <img src="{{ url_for('static', filename=photo.file_path) }}" alt="检查照片">
                    <div class="photo-caption">
                        {{ inspection.inspection_type|replace('morning', '晨检')|replace('noon', '午检')|replace('evening', '晚检') }} -
                        {{ inspection.inspection_item }}
                    </div>
                </div>
                {% endfor %}
            {% endif %}
        {% endfor %}
    {% endfor %}
</div>
{% endif %}
{% endblock %}

{% block signature %}
<div class="signature-item">
    <div class="signature-line"></div>
    <div>检查人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>食堂负责人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>学校负责人</div>
</div>
{% endblock %}
