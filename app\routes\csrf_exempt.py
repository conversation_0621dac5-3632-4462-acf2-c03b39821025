from flask import Blueprint, current_app, redirect, url_for, request, flash, render_template
from flask_login import login_required, current_user
from app import csrf, db
from app.models import PurchaseOrder, StockIn, StockInItem, Warehouse, Inventory
from datetime import datetime, timedelta
import uuid

# 创建一个CSRF豁免的蓝图
csrf_exempt_bp = Blueprint('csrf_exempt', __name__)

# 豁免整个蓝图的CSRF保护
csrf.exempt(csrf_exempt_bp)

@csrf_exempt_bp.route('/stock-in/create-from-purchase-exempt/<int:purchase_order_id>', methods=['GET', 'POST'])
@login_required
def create_from_purchase_exempt(purchase_order_id):
    """从采购订单创建入库单（CSRF豁免版本）"""
    # 获取采购订单
    purchase_order = PurchaseOrder.query.get_or_404(purchase_order_id)

    # 移除权限检查，允许任何登录用户操作

    # 移除状态检查，允许任何状态的采购订单创建入库单

    # 获取该区域的默认仓库
    warehouse = Warehouse.query.filter_by(area_id=purchase_order.area_id, status='正常').first()

    if not warehouse:
        flash('未找到可用的仓库，请先创建仓库', 'danger')
        return redirect(url_for('warehouse.create'))

    if request.method == 'POST':
        # 获取表单数据
        stock_in_date = request.form.get('stock_in_date')
        notes = request.form.get('notes', '')

        # 验证数据
        if not stock_in_date:
            flash('请填写入库日期', 'danger')
            return redirect(url_for('csrf_exempt.create_from_purchase_exempt', purchase_order_id=purchase_order_id))

        # 生成入库单号，使用系统自带令牌格式
        stock_in_number = f"RK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        try:
            # 创建入库单
            stock_in = StockIn(
                stock_in_number=stock_in_number,
                warehouse_id=warehouse.id,
                stock_in_type='采购入库',
                stock_in_date=datetime.strptime(stock_in_date, '%Y-%m-%d'),
                operator_id=current_user.id,
                purchase_order_id=purchase_order.id,
                status='已入库',  # 直接设置为已入库状态
                inspector_id=current_user.id,  # 自动审核通过
                notes=f'从采购订单 {purchase_order.order_number} 创建\n{notes}'
            )

            db.session.add(stock_in)
            db.session.flush()  # 获取ID

            # 从采购订单导入食材
            items = purchase_order.order_items

            for item in items:
                # 计算默认的生产日期和过期日期
                production_date = datetime.now().date()
                # 默认保质期为30天，如果食材有保质期信息则使用食材的保质期
                shelf_life = item.ingredient.shelf_life or 30
                expiry_date = production_date + timedelta(days=shelf_life)

                # 生成批次号，使用系统自带令牌格式
                batch_number = f"B{datetime.now().strftime('%Y%m%d%H%M%S')}"

                # 创建入库明细
                stock_in_item = StockInItem(
                    stock_in_id=stock_in.id,
                    ingredient_id=item.ingredient_id,
                    purchase_order_item_id=item.id,
                    batch_number=batch_number,
                    quantity=item.quantity,
                    unit=item.unit,
                    production_date=production_date,
                    expiry_date=expiry_date,
                    unit_price=item.unit_price,
                    supplier_id=purchase_order.supplier_id,
                    quality_status='良好',
                    notes='自动创建的入库明细'
                )

                db.session.add(stock_in_item)

                # 更新库存
                inventory = Inventory.query.filter_by(
                    ingredient_id=item.ingredient_id,
                    warehouse_id=warehouse.id
                ).first()

                if inventory:
                    # 更新现有库存
                    inventory.quantity += item.quantity
                    inventory.updated_at = datetime.now()
                else:
                    # 创建新库存记录
                    inventory = Inventory(
                        ingredient_id=item.ingredient_id,
                        warehouse_id=warehouse.id,
                        quantity=item.quantity,
                        unit=item.unit,
                        batch_number=batch_number,
                        production_date=production_date,
                        expiry_date=expiry_date,
                        supplier_id=purchase_order.supplier_id,
                        status='正常',
                        created_by=current_user.id,
                        notes=f'由入库单 {stock_in_number} 创建'
                    )
                    db.session.add(inventory)

            # 更新采购订单状态为已入库
            purchase_order.status = '已入库'
            purchase_order.delivery_date = datetime.now()

            # 提交事务
            db.session.commit()

            flash('采购订单已成功入库', 'success')
            return redirect(url_for('stock_in.view', id=stock_in.id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"从采购订单创建入库单时出错: {str(e)}")
            flash(f'从采购订单创建入库单时出错: {str(e)}', 'danger')
            return redirect(url_for('csrf_exempt.create_from_purchase_exempt', purchase_order_id=purchase_order_id))

    # GET请求，显示创建表单
    # 获取采购订单
    purchase_order = PurchaseOrder.query.get_or_404(purchase_order_id)

    # 获取该区域的默认仓库
    warehouse = Warehouse.query.filter_by(area_id=purchase_order.area_id, status='正常').first()

    if not warehouse:
        flash('未找到可用的仓库，请先创建仓库', 'danger')
        return redirect(url_for('warehouse.create'))

    # 直接渲染模板，不再重定向
    # 获取当前日期
    today = datetime.now().strftime('%Y-%m-%d')

    return render_template('stock_in/create_from_purchase.html',
                          purchase_order=purchase_order,
                          warehouse=warehouse,
                          today=today,
                          title='从采购订单创建入库单（CSRF豁免版本）')
