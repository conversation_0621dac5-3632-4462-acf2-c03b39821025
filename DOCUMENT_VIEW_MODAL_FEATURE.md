# 文档查看模态框功能实现

## 🎯 **功能需求**

用户要求：
- **模态框查看**：将文档"查看"功能改为弹出模态框显示
- **风格一致**：与"查看关联食材"模态框保持相同风格
- **避免新窗口**：不再在新浏览器标签页中打开文档

## 🔧 **完整实现方案**

### **1. 模态框结构设计**

#### **双面板布局**
```html
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-body p-0">
            <div class="row no-gutters">
                <!-- 左侧：文档信息面板 (25%) -->
                <div class="col-md-3 document-info-panel">
                    <!-- 文档详细信息 -->
                </div>
                
                <!-- 右侧：文档预览面板 (75%) -->
                <div class="col-md-9 document-preview-panel">
                    <!-- 文档内容预览 -->
                </div>
            </div>
        </div>
    </div>
</div>
```

#### **信息面板内容**
- 📋 **文档类型**：带颜色标识的徽章
- 📄 **文件名**：可换行显示的完整文件名
- 🕒 **上传时间**：格式化的时间显示
- 🏢 **关联供应商**：供应商名称或"无"
- 🥬 **关联食材**：以徽章形式显示关联的食材
- 📝 **备注信息**：如果有备注则显示
- 🔽 **操作按钮**：下载和新窗口打开

#### **预览面板功能**
- 🖼️ **图片预览**：支持 JPG, PNG, GIF, BMP, WEBP
- 📄 **PDF预览**：内嵌iframe显示PDF内容
- 📁 **其他文件**：显示文件类型图标和下载提示

### **2. 智能文件类型识别**

```html
{% set file_ext = doc.file_path.split('.')[-1].lower() %}
{% if file_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] %}
    <!-- 图片预览 -->
    <img src="{{ url_for('static', filename=doc.file_path|fix_path) }}" 
         class="img-fluid" 
         style="max-height: 70vh; border: 1px solid #ddd; border-radius: 4px;">
{% elif file_ext == 'pdf' %}
    <!-- PDF预览 -->
    <iframe src="{{ url_for('static', filename=doc.file_path|fix_path) }}" 
            style="width: 100%; height: 70vh; border: 1px solid #ddd;">
    </iframe>
{% else %}
    <!-- 其他文件类型 -->
    <div class="text-center py-5">
        <i class="fas fa-file-alt fa-5x text-muted"></i>
        <h5>无法预览此文件类型</h5>
        <p>文件类型：{{ file_ext.upper() }}</p>
    </div>
{% endif %}
```

### **3. 响应式设计**

#### **桌面端布局**
- 🖥️ **模态框宽度**：95%屏幕宽度
- 📊 **信息面板**：25%宽度，固定在左侧
- 🖼️ **预览面板**：75%宽度，显示文档内容
- 📏 **预览高度**：70vh，确保内容完整显示

#### **移动端适配**
- 📱 **模态框宽度**：98%屏幕宽度
- 📋 **信息面板**：100%宽度，位于顶部
- 🖼️ **预览面板**：100%宽度，位于底部
- 📏 **预览高度**：50vh，适应小屏幕

### **4. 按钮功能更新**

#### **查看按钮改造**
```html
<!-- 修改前：新窗口打开 -->
<a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" target="_blank" class="btn btn-sm btn-primary">
    <i class="fas fa-eye"></i> 查看
</a>

<!-- 修改后：模态框显示 -->
<button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#viewDocModal{{ doc.id }}">
    <i class="fas fa-eye"></i> 查看
</button>
```

#### **保留的功能**
- ✅ **下载按钮**：直接下载文档
- ✅ **删除按钮**：删除文档（待审核状态）
- ✅ **新窗口打开**：在信息面板中提供选项

## 🎨 **视觉设计特色**

### **1. 配色方案**
- 🔵 **主色调**：蓝色（#007bff）- 与系统保持一致
- 🔍 **信息面板**：浅灰背景（#f8f9fa）
- 🖼️ **预览面板**：白色背景，突出内容
- 🏷️ **徽章颜色**：根据文档类型自动分配

### **2. 交互效果**
- 🎭 **阴影效果**：预览内容添加轻微阴影
- 🔄 **过渡动画**：模态框淡入淡出效果
- 🎯 **悬停效果**：按钮悬停状态变化
- 📱 **触摸友好**：移动端优化的点击区域

### **3. 图标使用**
- 👁️ **查看图标**：`fas fa-eye`
- 📄 **文档图标**：`fas fa-file-alt`
- 📥 **下载图标**：`fas fa-download`
- 🔗 **外链图标**：`fas fa-external-link-alt`
- ℹ️ **信息图标**：`fas fa-info-circle`

## 🚀 **功能特性**

### **1. 多格式支持**
- 🖼️ **图片格式**：JPG, JPEG, PNG, GIF, BMP, WEBP
- 📄 **PDF文档**：内嵌预览，支持缩放和滚动
- 📁 **其他格式**：DOC, DOCX, XLS, XLSX等显示下载提示

### **2. 智能预览**
- 🔍 **自适应大小**：图片自动适应容器大小
- 📏 **高度限制**：最大70vh，防止内容过长
- 🖱️ **交互支持**：PDF可滚动，图片可缩放
- ⚡ **快速加载**：优化的加载性能

### **3. 用户体验**
- 🎯 **一键查看**：点击即可预览文档
- 📋 **信息完整**：显示所有相关信息
- 🔄 **操作便捷**：下载、删除等操作集中
- 📱 **移动友好**：完美适配各种设备

### **4. 安全性保证**
- 🔒 **路径安全**：使用fix_path过滤器
- 🛡️ **权限控制**：删除操作需要相应权限
- 🔐 **文件验证**：只显示允许的文件类型
- 🚫 **XSS防护**：模板自动转义用户输入

## 📊 **使用场景**

### **1. 图片文档查看**
- 📸 **送货单照片**：清晰预览送货单内容
- 🧾 **检验证明**：查看检验检疫证明图片
- 📋 **检测报告**：预览质量检测报告

### **2. PDF文档查看**
- 📄 **正式文档**：内嵌预览PDF格式文档
- 📊 **报告文件**：查看详细的检测报告
- 📋 **证书文件**：预览各类证书文档

### **3. 其他文件处理**
- 📁 **Office文档**：提供下载链接
- 🗂️ **压缩文件**：显示文件信息和下载选项
- 📝 **文本文件**：引导用户下载查看

## 🎉 **实现效果**

### **用户体验提升**
- ✅ **无需跳转**：在当前页面即可查看文档
- ✅ **信息集中**：文档信息和预览在同一界面
- ✅ **操作便捷**：所有相关操作集中在一个模态框
- ✅ **风格统一**：与系统其他模态框保持一致

### **功能完整性**
- ✅ **预览功能**：支持主流文件格式预览
- ✅ **下载功能**：保留原有下载功能
- ✅ **删除功能**：在模态框中也可以删除文档
- ✅ **新窗口选项**：仍可选择在新窗口打开

### **技术优势**
- ✅ **响应式设计**：完美适配各种设备
- ✅ **性能优化**：按需加载，不影响页面性能
- ✅ **代码复用**：与现有模态框风格一致
- ✅ **易于维护**：清晰的代码结构和注释

## 🎯 **总结**

**文档查看模态框功能已完美实现！**

### **主要特色**
1. 🎨 **美观界面**：双面板布局，信息和预览分离
2. 🔍 **智能预览**：根据文件类型自动选择预览方式
3. 📱 **响应式设计**：完美适配桌面端和移动端
4. ⚡ **用户友好**：无需跳转，一站式文档查看体验

### **技术亮点**
- 🏗️ **模块化设计**：清晰的组件结构
- 🎭 **视觉一致性**：与系统整体风格保持一致
- 🔧 **功能完整**：保留所有原有功能并增强体验
- 📈 **性能优化**：高效的加载和渲染机制

**用户现在可以享受更加流畅、美观的文档查看体验！** 🎊
