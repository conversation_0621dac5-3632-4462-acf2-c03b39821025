"""
食堂日常检查API路由

提供食堂日常检查相关的API接口，用于前端AJAX调用。
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models_daily_management import DailyLog, InspectionRecord, Photo
from app.services.daily_management_service import InspectionService
from app.utils.form_validator import validate_inspection_form
from app.utils.error_handler import api_error_response, db_operation_error_handler
from app.utils.datetime_helper import get_current_time, format_datetime
from app.utils.school_required import school_required
from sqlalchemy import text
from datetime import datetime
import traceback

inspection_api_bp = Blueprint('inspection_api', __name__, url_prefix='/api/inspections')

@inspection_api_bp.route('/', methods=['GET'])
@login_required
@school_required
def get_inspections(user_area=None):
    """获取检查记录列表"""
    try:
        # 获取查询参数
        daily_log_id = request.args.get('daily_log_id', type=int)
        inspection_type = request.args.get('inspection_type')

        if not daily_log_id:
            return api_error_response('缺少必要参数: daily_log_id', 400)

        # 验证日志是否存在
        log = DailyLog.query.get(daily_log_id)
        if not log:
            return api_error_response('日志不存在', 404)

        # 验证用户是否有权限访问该日志
        user_area = current_user.get_current_area()
        if log.area_id != user_area.id and not current_user.is_admin():
            return api_error_response('无权访问此日志', 403)

        # 查询检查记录
        query = InspectionRecord.query.filter_by(daily_log_id=daily_log_id)

        if inspection_type:
            if inspection_type not in ['morning', 'noon', 'evening']:
                return api_error_response('检查类型无效', 400)
            query = query.filter_by(inspection_type=inspection_type)

        inspections = query.all()

        # 构建响应数据
        result = []
        for inspection in inspections:
            # 获取照片
            photos = Photo.query.filter_by(
                reference_type='inspection',
                reference_id=inspection.id
            ).all()

            photo_urls = [f"/daily-management/photos/{photo.id}" for photo in photos]

            result.append({
                'id': inspection.id,
                'daily_log_id': inspection.daily_log_id,
                'inspection_type': inspection.inspection_type,
                'inspection_item': inspection.inspection_item,
                'status': inspection.status,
                'description': inspection.description,
                'inspector_id': inspection.inspector_id,
                'inspection_time': format_datetime(inspection.inspection_time),
                'photo_path': inspection.photo_path,
                'photos': photo_urls,
                'created_at': format_datetime(inspection.created_at)
            })

        return jsonify({
            'success': True,
            'data': result,
            'count': len(result)
        })

    except Exception as e:
        current_app.logger.error(f"获取检查记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'获取检查记录失败: {str(e)}', 500)

@inspection_api_bp.route('/<int:inspection_id>', methods=['GET'])
@login_required
def get_inspection(inspection_id):
    """获取单个检查记录详情"""
    try:
        inspection = InspectionService.get_inspection_by_id(inspection_id)

        if not inspection:
            return api_error_response('检查记录不存在', 404)

        # 验证用户是否有权限访问该检查记录
        log = DailyLog.query.get(inspection.daily_log_id)
        user_area = current_user.get_current_area()
        if log.area_id != user_area.id and not current_user.is_admin():
            return api_error_response('无权访问此检查记录', 403)

        # 获取照片
        photos = Photo.query.filter_by(
            reference_type='inspection',
            reference_id=inspection.id
        ).all()

        photo_urls = [f"/daily-management/photos/{photo.id}" for photo in photos]

        # 构建响应数据
        result = {
            'id': inspection.id,
            'daily_log_id': inspection.daily_log_id,
            'inspection_type': inspection.inspection_type,
            'inspection_item': inspection.inspection_item,
            'status': inspection.status,
            'description': inspection.description,
            'inspector_id': inspection.inspector_id,
            'inspection_time': format_datetime(inspection.inspection_time),
            'photo_path': inspection.photo_path,
            'photos': photo_urls,
            'created_at': format_datetime(inspection.created_at)
        }

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        current_app.logger.error(f"获取检查记录详情失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'获取检查记录详情失败: {str(e)}', 500)

@inspection_api_bp.route('/', methods=['POST'])
@login_required
@validate_inspection_form
@db_operation_error_handler
def create_inspection():
    """创建检查记录"""
    try:
        # 获取请求数据
        data = request.json if request.is_json else request.form.to_dict()

        # 验证日志是否存在
        daily_log_id = data.get('daily_log_id')
        log = DailyLog.query.get(daily_log_id)
        if not log:
            return api_error_response('日志不存在', 404)

        # 验证用户是否有权限访问该日志
        user_area = current_user.get_current_area()
        if log.area_id != user_area.id and not current_user.is_admin():
            return api_error_response('无权访问此日志', 403)

        # 添加检查人ID
        data['inspector_id'] = current_user.id

        # 创建检查记录
        inspection = InspectionService.create_inspection(data)

        # 构建响应数据
        result = {
            'id': inspection.id,
            'daily_log_id': inspection.daily_log_id,
            'inspection_type': inspection.inspection_type,
            'inspection_item': inspection.inspection_item,
            'status': inspection.status,
            'description': inspection.description,
            'inspector_id': inspection.inspector_id,
            'inspection_time': format_datetime(inspection.inspection_time),
            'photo_path': inspection.photo_path,
            'created_at': format_datetime(inspection.created_at)
        }

        return jsonify({
            'success': True,
            'message': '检查记录创建成功',
            'data': result
        }), 201

    except ValueError as e:
        return api_error_response(str(e), 400)
    except Exception as e:
        current_app.logger.error(f"创建检查记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'创建检查记录失败: {str(e)}', 500)

@inspection_api_bp.route('/<int:inspection_id>', methods=['PUT'])
@login_required
@db_operation_error_handler
def update_inspection(inspection_id):
    """更新检查记录"""
    try:
        # 获取请求数据
        data = request.json if request.is_json else request.form.to_dict()

        # 验证检查记录是否存在
        inspection = InspectionService.get_inspection_by_id(inspection_id)
        if not inspection:
            return api_error_response('检查记录不存在', 404)

        # 验证用户是否有权限访问该检查记录
        log = DailyLog.query.get(inspection.daily_log_id)
        user_area = current_user.get_current_area()
        if log.area_id != user_area.id and not current_user.is_admin():
            return api_error_response('无权访问此检查记录', 403)

        # 更新检查记录
        updated_inspection = InspectionService.update_inspection(inspection_id, data)

        # 构建响应数据
        result = {
            'id': updated_inspection.id,
            'daily_log_id': updated_inspection.daily_log_id,
            'inspection_type': updated_inspection.inspection_type,
            'inspection_item': updated_inspection.inspection_item,
            'status': updated_inspection.status,
            'description': updated_inspection.description,
            'inspector_id': updated_inspection.inspector_id,
            'inspection_time': format_datetime(updated_inspection.inspection_time),
            'photo_path': updated_inspection.photo_path,
            'created_at': format_datetime(updated_inspection.created_at)
        }

        return jsonify({
            'success': True,
            'message': '检查记录更新成功',
            'data': result
        })

    except ValueError as e:
        return api_error_response(str(e), 400)
    except Exception as e:
        current_app.logger.error(f"更新检查记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'更新检查记录失败: {str(e)}', 500)

@inspection_api_bp.route('/<int:inspection_id>', methods=['DELETE'])
@login_required
@db_operation_error_handler
def delete_inspection(inspection_id):
    """删除检查记录"""
    try:
        # 验证检查记录是否存在
        inspection = InspectionService.get_inspection_by_id(inspection_id)
        if not inspection:
            return api_error_response('检查记录不存在', 404)

        # 验证用户是否有权限访问该检查记录
        log = DailyLog.query.get(inspection.daily_log_id)
        user_area = current_user.get_current_area()
        if log.area_id != user_area.id and not current_user.is_admin():
            return api_error_response('无权访问此检查记录', 403)

        # 删除检查记录
        result = InspectionService.delete_inspection(inspection_id)

        if result:
            return jsonify({
                'success': True,
                'message': '检查记录删除成功'
            })
        else:
            return api_error_response('检查记录删除失败', 500)

    except Exception as e:
        current_app.logger.error(f"删除检查记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return api_error_response(f'删除检查记录失败: {str(e)}', 500)
