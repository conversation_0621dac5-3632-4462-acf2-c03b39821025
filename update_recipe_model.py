"""
更新Recipe模型的数据库架构
"""

from app import create_app, db
from app.models import Recipe
from sqlalchemy import inspect

def update_recipe_model():
    """更新Recipe模型的数据库架构"""
    app = create_app()
    
    with app.app_context():
        # 检查字段是否已存在
        inspector = inspect(db.engine)
        try:
            columns = [c['name'] for c in inspector.get_columns('recipes')]
            print(f"当前表字段: {columns}")
            
            # 检查是否需要更新
            if 'is_user_defined' not in columns or 'priority' not in columns:
                print("需要更新数据库架构...")
                
                # 创建临时表
                db.session.execute('CREATE TABLE recipes_new LIKE recipes')
                
                # 添加新字段
                if 'is_user_defined' not in columns:
                    db.session.execute('ALTER TABLE recipes_new ADD COLUMN is_user_defined BOOLEAN NOT NULL DEFAULT 0')
                    print("已添加字段: is_user_defined")
                
                if 'priority' not in columns:
                    db.session.execute('ALTER TABLE recipes_new ADD COLUMN priority INTEGER NOT NULL DEFAULT 0')
                    print("已添加字段: priority")
                
                # 复制数据
                db.session.execute('INSERT INTO recipes_new SELECT *, 0, 0 FROM recipes')
                
                # 重命名表
                db.session.execute('DROP TABLE recipes')
                db.session.execute('RENAME TABLE recipes_new TO recipes')
                
                db.session.commit()
                print("数据库架构更新完成！")
            else:
                print("数据库架构已经是最新的，无需更新。")
                
        except Exception as e:
            print(f"更新数据库架构时出错: {e}")
            db.session.rollback()

if __name__ == '__main__':
    update_recipe_model()
