"""
学校关联中间件

提供用于在请求处理前检查用户所属学校的中间件。
"""

from flask import request, g, flash, redirect, url_for
from flask_login import current_user
import re

class SchoolMiddleware:
    """学校关联中间件"""
    
    def __init__(self, app=None):
        self.app = app
        self.exempt_routes = []
        self.exempt_patterns = []
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化中间件"""
        self.app = app
        
        # 注册请求处理前的钩子函数
        app.before_request(self.check_school)
        
        # 默认豁免的路由
        self.exempt_routes = [
            '/',
            '/login',
            '/logout',
            '/static/',
            '/favicon.ico',
            '/admin/',
            '/api/',
            '/auth/',
            '/error/',
            '/main/',
        ]
        
        # 默认豁免的路由模式
        self.exempt_patterns = [
            r'^/static/.*$',
            r'^/favicon\.ico$',
            r'^/admin/.*$',
            r'^/api/.*$',
            r'^/auth/.*$',
            r'^/error/.*$',
            r'^/main/.*$',
        ]
        
        # 从配置中获取豁免的路由
        if app.config.get('SCHOOL_MIDDLEWARE_EXEMPT_ROUTES'):
            self.exempt_routes.extend(app.config.get('SCHOOL_MIDDLEWARE_EXEMPT_ROUTES'))
        
        # 从配置中获取豁免的路由模式
        if app.config.get('SCHOOL_MIDDLEWARE_EXEMPT_PATTERNS'):
            self.exempt_patterns.extend(app.config.get('SCHOOL_MIDDLEWARE_EXEMPT_PATTERNS'))
    
    def is_exempt(self, path):
        """检查路径是否豁免"""
        # 检查是否匹配豁免的路由
        for route in self.exempt_routes:
            if path.startswith(route):
                return True
        
        # 检查是否匹配豁免的路由模式
        for pattern in self.exempt_patterns:
            if re.match(pattern, path):
                return True
        
        return False
    
    def check_school(self):
        """检查用户所属学校"""
        # 如果用户未登录，跳过检查
        if not current_user.is_authenticated:
            return
        
        # 如果用户是管理员，跳过检查
        if current_user.is_admin():
            return
        
        # 如果路径豁免，跳过检查
        if self.is_exempt(request.path):
            return
        
        # 检查用户是否有关联的学校
        user_area = current_user.get_current_area()
        if not user_area:
            # 将用户重定向到首页
            flash('您没有关联到任何学校，无法访问此功能', 'danger')
            return redirect(url_for('main.index'))
        
        # 将用户所属学校添加到g对象中，方便视图函数使用
        g.user_area = user_area
    
    def exempt(self, route):
        """添加豁免的路由"""
        self.exempt_routes.append(route)
    
    def exempt_pattern(self, pattern):
        """添加豁免的路由模式"""
        self.exempt_patterns.append(pattern)
