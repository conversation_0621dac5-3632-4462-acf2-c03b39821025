#!/usr/bin/env python3
"""
测试入库单详情页面显示修改的脚本
"""

import requests
import sys
from bs4 import BeautifulSoup

def test_stock_in_details_display():
    """测试入库单详情页面的显示修改"""
    print("🧪 测试入库单详情页面显示修改...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76  # 测试用的入库单ID
    
    try:
        details_url = f"{base_url}/stock-in/{stock_in_id}/details"
        print(f"📤 测试入库单详情页面: {details_url}")
        
        response = requests.get(details_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 入库单详情页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找入库明细表格
            detail_table = soup.find('table', class_='table-bordered table-striped')
            
            if detail_table:
                print("✅ 找到入库明细表格")
                
                # 检查表头
                headers = detail_table.find('thead')
                if headers:
                    header_cells = headers.find_all('th')
                    header_texts = [th.get_text().strip() for th in header_cells]
                    
                    print(f"📋 表头内容: {header_texts}")
                    
                    # 检查期望的表头
                    expected_headers = ['序号', '食材名称', '批次号', '数量', '单位', '单价', '供应商']
                    
                    if header_texts == expected_headers:
                        print("✅ 表头正确：包含供应商列，不包含生产日期和过期日期")
                        
                        # 检查是否不包含生产日期和过期日期
                        if '生产日期' not in header_texts and '过期日期' not in header_texts:
                            print("✅ 成功移除生产日期和过期日期列")
                        else:
                            print("❌ 仍然包含生产日期或过期日期列")
                            return False
                            
                        # 检查是否包含供应商列
                        if '供应商' in header_texts:
                            print("✅ 成功添加供应商列")
                        else:
                            print("❌ 缺少供应商列")
                            return False
                            
                        # 检查列数是否正确
                        if len(header_texts) == 7:
                            print("✅ 列数正确（7列）")
                        else:
                            print(f"⚠️ 列数不正确，期望7列，实际{len(header_texts)}列")
                            return False
                            
                    else:
                        print(f"❌ 表头不正确")
                        print(f"期望: {expected_headers}")
                        print(f"实际: {header_texts}")
                        return False
                        
                else:
                    print("❌ 未找到表头")
                    return False
                    
                # 检查表格数据行
                tbody = detail_table.find('tbody')
                if tbody:
                    data_rows = tbody.find_all('tr')
                    
                    if data_rows:
                        # 检查第一行数据（如果存在）
                        first_row = data_rows[0]
                        cells = first_row.find_all('td')
                        
                        if len(cells) == 7:
                            print("✅ 数据行列数正确（7列）")
                            
                            # 检查最后一列是否是供应商信息
                            supplier_cell = cells[6].get_text().strip()
                            print(f"📋 供应商列内容: '{supplier_cell}'")
                            
                            if supplier_cell and supplier_cell != '-':
                                print("✅ 供应商信息显示正常")
                            else:
                                print("⚠️ 供应商信息为空或显示为'-'")
                                
                        elif len(cells) == 1 and '暂无入库明细' in cells[0].get_text():
                            print("ℹ️ 该入库单暂无明细数据")
                            
                            # 检查colspan是否正确
                            colspan = cells[0].get('colspan')
                            if colspan == '7':
                                print("✅ 空数据提示的colspan正确（7）")
                            else:
                                print(f"⚠️ 空数据提示的colspan不正确，期望7，实际{colspan}")
                                
                        else:
                            print(f"⚠️ 数据行列数不正确，期望7列，实际{len(cells)}列")
                            return False
                    else:
                        print("ℹ️ 表格中没有数据行")
                        
                else:
                    print("❌ 未找到表格数据部分")
                    return False
                    
            else:
                print("❌ 未找到入库明细表格")
                return False
                
            return True
            
        elif response.status_code == 302:
            print("⚠️ 重定向，可能需要登录")
            return False
        else:
            print(f"❌ 状态码: {response.status_code}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_document_modal_display():
    """测试文档关联食材模态框的显示"""
    print("\n🧪 测试文档关联食材模态框显示...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76
    
    try:
        details_url = f"{base_url}/stock-in/{stock_in_id}/details"
        response = requests.get(details_url, timeout=30)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找文档关联食材模态框中的表格
            modal_tables = soup.find_all('div', class_='modal-body')
            
            for modal_body in modal_tables:
                table = modal_body.find('table', class_='table-bordered table-hover')
                if table:
                    headers = table.find('thead')
                    if headers:
                        header_cells = headers.find_all('th')
                        header_texts = [th.get_text().strip() for th in header_cells]
                        
                        # 检查是否是文档关联食材的表格（包含食材名称、批次号等）
                        if '食材名称' in header_texts and '批次号' in header_texts:
                            print(f"📋 找到文档关联表格，表头: {header_texts}")
                            
                            # 检查是否不包含生产日期和过期日期
                            if '生产日期' not in header_texts and '过期日期' not in header_texts:
                                print("✅ 文档关联表格成功移除生产日期和过期日期")
                                
                                # 检查是否包含供应商
                                if '供应商' in header_texts:
                                    print("✅ 文档关联表格成功添加供应商列")
                                    return True
                                else:
                                    print("⚠️ 文档关联表格缺少供应商列")
                                    
                            else:
                                print("❌ 文档关联表格仍包含生产日期或过期日期")
                                return False
            
            print("ℹ️ 未找到文档关联食材表格（可能没有相关数据）")
            return True
            
        else:
            print(f"⚠️ 无法访问页面，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code in [200, 302]:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试入库单详情页面显示修改\n")
    
    # 检查服务器连接
    if not test_server_connection():
        print("\n❌ 无法连接到服务器，请先启动Flask应用")
        return False
    
    print()
    
    # 测试入库单详情页面显示
    details_test = test_stock_in_details_display()
    
    # 测试文档模态框显示
    modal_test = test_document_modal_display()
    
    # 输出结果
    print(f"\n{'='*60}")
    print("📊 测试结果")
    print('='*60)
    
    print(f"入库单详情页面显示: {'✅ 通过' if details_test else '❌ 失败'}")
    print(f"文档模态框显示: {'✅ 通过' if modal_test else '❌ 失败'}")
    
    if details_test and modal_test:
        print("\n🎉 入库单详情页面显示修改成功！")
        print("\n💡 修改内容:")
        print("1. ✅ 移除了生产日期和过期日期列")
        print("2. ✅ 添加了供应商列")
        print("3. ✅ 表格显示格式：序号、食材名称、批次号、数量、单位、单价、供应商")
        print("4. ✅ 文档关联食材表格也同步修改")
        return True
    else:
        print("\n⚠️ 部分测试失败，可能仍存在问题")
        if not details_test:
            print("- 入库单详情页面显示有问题")
        if not modal_test:
            print("- 文档模态框显示有问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
