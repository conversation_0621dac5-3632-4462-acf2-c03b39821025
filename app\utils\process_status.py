from datetime import datetime, date, timedelta

# 流程状态常量
STATUS_NOT_STARTED = 'not-started'
STATUS_IN_PROGRESS = 'in-progress'
STATUS_COMPLETED = 'completed'
STATUS_WARNING = 'warning'
STATUS_DANGER = 'danger'

def get_process_status(status, progress=None, total=None, deadline=None):
    """
    获取流程状态信息
    
    参数:
        status: 状态代码 (not-started, in-progress, completed, warning, danger)
        progress: 当前进度
        total: 总任务数
        deadline: 截止日期
    
    返回:
        包含状态信息的字典
    """
    status_text_map = {
        STATUS_NOT_STARTED: '未开始',
        STATUS_IN_PROGRESS: '进行中',
        STATUS_COMPLETED: '已完成',
        STATUS_WARNING: '需注意',
        STATUS_DANGER: '有问题'
    }
    
    result = {
        'status': status,
        'status_text': status_text_map.get(status, '未知状态')
    }
    
    # 添加进度信息
    if progress is not None and total is not None:
        result['progress'] = progress
        result['total'] = total
        result['percentage'] = (progress / total) * 100 if total > 0 else 0
        result['status_text'] = f"{result['status_text']} ({progress}/{total})"
    
    # 添加截止日期信息
    if deadline:
        today = date.today()
        days_left = (deadline - today).days
        
        if days_left < 0:
            result['deadline_text'] = f"已逾期 {abs(days_left)} 天"
            if status != STATUS_COMPLETED:
                result['status'] = STATUS_DANGER
        elif days_left == 0:
            result['deadline_text'] = "今日截止"
            if status != STATUS_COMPLETED:
                result['status'] = STATUS_WARNING
        elif days_left <= 2:
            result['deadline_text'] = f"还剩 {days_left} 天"
            if status != STATUS_COMPLETED:
                result['status'] = STATUS_WARNING
        else:
            result['deadline_text'] = f"还剩 {days_left} 天"
        
        # 更新状态文本
        if 'deadline_text' in result:
            result['status_text'] = f"{result['status_text']} ({result['deadline_text']})"
    
    return result

def get_menu_plan_status(current_week=None):
    """获取周菜单计划状态"""
    # 这里应该从数据库获取实际数据
    # 示例数据
    return get_process_status(
        STATUS_COMPLETED, 
        progress=5, 
        total=5, 
        deadline=date.today() - timedelta(days=2)
    )

def get_purchase_plan_status(current_week=None):
    """获取采购计划状态"""
    # 示例数据
    return get_process_status(
        STATUS_COMPLETED, 
        progress=8, 
        total=8, 
        deadline=date.today() - timedelta(days=1)
    )

def get_inspection_status(current_week=None):
    """获取入库检查状态"""
    # 示例数据
    return get_process_status(
        STATUS_IN_PROGRESS, 
        progress=3, 
        total=5, 
        deadline=date.today()
    )

def get_storage_in_status(current_week=None):
    """获取入库状态"""
    # 示例数据
    return get_process_status(
        STATUS_IN_PROGRESS, 
        progress=2, 
        total=5, 
        deadline=date.today() + timedelta(days=1)
    )

def get_consumption_plan_status(current_week=None):
    """获取消耗量计划状态"""
    # 示例数据
    return get_process_status(
        STATUS_NOT_STARTED, 
        progress=0, 
        total=5, 
        deadline=date.today() + timedelta(days=2)
    )

def get_storage_out_status(current_week=None):
    """获取出库状态"""
    # 示例数据
    return get_process_status(
        STATUS_NOT_STARTED, 
        progress=0, 
        total=5, 
        deadline=date.today() + timedelta(days=3)
    )

def get_inventory_status():
    """获取库存状态"""
    # 示例数据
    return get_process_status(
        STATUS_WARNING, 
        progress=None, 
        total=None, 
        deadline=None
    )

def get_samples_status(current_week=None):
    """获取留样记录状态"""
    # 示例数据
    return get_process_status(
        STATUS_COMPLETED, 
        progress=3, 
        total=3, 
        deadline=date.today() - timedelta(days=1)
    )

def get_tracing_status(current_week=None):
    """获取溯源状态"""
    # 示例数据
    return get_process_status(
        STATUS_NOT_STARTED, 
        progress=0, 
        total=0, 
        deadline=None
    )

def get_today_tasks():
    """获取今日待办任务"""
    # 示例数据
    return [
        "完成入库检查 (3/5)",
        "准备出库计划",
        "库存预警：大米库存不足"
    ]

def count_completed_steps(steps):
    """计算已完成的步骤数"""
    completed = 0
    for step in steps:
        if step['status'] == STATUS_COMPLETED:
            completed += 1
    return completed

def calculate_progress_percentage(steps):
    """计算整体进度百分比"""
    if not steps:
        return 0
    
    total_steps = len(steps)
    completed_steps = count_completed_steps(steps)
    
    return (completed_steps / total_steps) * 100
