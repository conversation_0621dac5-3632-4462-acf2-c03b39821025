#!/usr/bin/env python3
"""
快速测试路由端点修复的脚本
"""

import requests
import sys

def test_route_endpoints():
    """测试路由端点是否正确"""
    print("🧪 快速测试路由端点修复...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76  # 测试用的入库单ID
    
    # 测试批次编辑器页面是否能正常加载（不再有BuildError）
    try:
        editor_url = f"{base_url}/stock-in/{stock_in_id}/batch-editor-simplified"
        print(f"📤 测试批次编辑器页面: {editor_url}")
        
        response = requests.get(editor_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 批次编辑器页面加载成功，没有BuildError")
            
            # 检查页面是否包含JavaScript代码
            if "batch_editor_simplified_scripts.html" in response.text:
                print("✅ JavaScript脚本正确包含")
            
            # 检查是否还有错误的路由引用
            if "stock_in.details" in response.text:
                print("⚠️ 页面中仍然包含错误的路由引用")
                return False
            else:
                print("✅ 没有发现错误的路由引用")
                
            return True
            
        elif response.status_code == 302:
            print("⚠️ 重定向，可能需要登录，但没有BuildError")
            return True
        elif response.status_code == 500:
            print("❌ 服务器内部错误，可能仍有BuildError")
            return False
        else:
            print(f"⚠️ 状态码: {response.status_code}")
            return True
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_details_page():
    """测试详情页面是否存在"""
    print("\n🧪 测试入库单详情页面...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76
    
    try:
        details_url = f"{base_url}/stock-in/{stock_in_id}/details"
        print(f"📤 测试详情页面: {details_url}")
        
        response = requests.get(details_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 入库单详情页面存在且可访问")
            return True
        elif response.status_code == 302:
            print("⚠️ 重定向，可能需要登录，但页面存在")
            return True
        elif response.status_code == 404:
            print("❌ 详情页面不存在（404错误）")
            return False
        else:
            print(f"⚠️ 状态码: {response.status_code}")
            return True
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code in [200, 302]:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def main():
    """主测试函数"""
    print("🚀 快速测试路由端点修复\n")
    
    # 检查服务器连接
    if not test_server_connection():
        print("\n❌ 无法连接到服务器，请先启动Flask应用")
        return False
    
    print()
    
    # 测试批次编辑器页面
    editor_test = test_route_endpoints()
    
    # 测试详情页面
    details_test = test_details_page()
    
    # 输出结果
    print(f"\n{'='*50}")
    print("📊 测试结果")
    print('='*50)
    
    print(f"批次编辑器页面: {'✅ 通过' if editor_test else '❌ 失败'}")
    print(f"入库单详情页面: {'✅ 通过' if details_test else '❌ 失败'}")
    
    if editor_test and details_test:
        print("\n🎉 路由端点修复成功！")
        print("💡 现在可以正常访问批次编辑器页面，不会再出现BuildError")
        print("💡 保存后会正确重定向到入库单详情页面")
        return True
    else:
        print("\n⚠️ 仍有问题需要解决")
        if not editor_test:
            print("- 批次编辑器页面仍有问题")
        if not details_test:
            print("- 入库单详情页面不可访问")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
