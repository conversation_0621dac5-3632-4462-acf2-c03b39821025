from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Supplier, SupplierCertificate, SupplierSchoolRelation, AdministrativeArea
from app.forms.supplier import SupplierCertificateForm
from app.utils.log_activity import log_activity
import json
import os
from datetime import datetime, date
import uuid

supplier_certificate_bp = Blueprint('supplier_certificate', __name__)

@supplier_certificate_bp.route('/')
@login_required
def index():
    """供应商证书列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    supplier_id = request.args.get('supplier_id', type=int)
    status = request.args.get('status', '')
    keyword = request.args.get('keyword', '')

    query = SupplierCertificate.query

    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)

    if status:
        query = query.filter_by(status=status)

    if keyword:
        query = query.filter(
            db.or_(
                SupplierCertificate.certificate_type.LIKE(f'%{keyword}%'),
                SupplierCertificate.certificate_number.LIKE(f'%{keyword}%'),
                SupplierCertificate.issuing_authority.LIKE(f'%{keyword}%')
            )
        )

    # 更新证书状态
    today = date.today()
    certificates = SupplierCertificate.query.all()
    for cert in certificates:
        days_to_expiry = (cert.expiry_date - today).days
        if days_to_expiry < 0:
            new_status = '已过期'
        elif days_to_expiry <= 30:
            new_status = '即将过期'
        else:
            new_status = '有效'

        if cert.status != new_status:
            cert.status = new_status
            db.session.add(cert)

    db.session.commit()

    # 根据用户区域权限筛选证书
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商证书
        query = query.join(Supplier, SupplierCertificate.supplier_id == Supplier.id)\
                    .join(SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct()

    pagination = query.order_by(SupplierCertificate.id.desc()).paginate(page=page, per_page=per_page)
    certificates = pagination.items

    # 获取供应商列表，也需要根据用户区域权限筛选
    if current_user.is_admin():
        suppliers = Supplier.query.filter_by(status=1).all()  # 只显示合作中的供应商
    else:
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .filter(Supplier.status == 1)\
                    .distinct().all()

    # 获取当前用户的区域信息
    current_area = current_user.get_current_area()
    area_path = []
    if current_area:
        area_path = [current_area]
        ancestors = current_area.get_ancestors()
        area_path = ancestors + area_path

    return render_template('supplier/certificate_index.html',
                          certificates=certificates,
                          pagination=pagination,
                          suppliers=suppliers,
                          supplier_id=supplier_id,
                          status=status,
                          keyword=keyword,
                          current_area=current_area,
                          area_path=area_path,
                          title='供应商证书管理',
                          now=datetime.now())

@supplier_certificate_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """添加供应商证书"""
    form = SupplierCertificateForm()

    # 获取供应商选项
    suppliers = Supplier.query.filter_by(status=1).all()  # 只显示合作中的供应商
    form.supplier_id.choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in suppliers]

    if form.validate_on_submit():
        # 处理证书图片上传
        certificate_image = None
        if form.certificate_image.data:
            image_filename = str(uuid.uuid4()) + os.path.splitext(form.certificate_image.data.filename)[1]
            image_path = os.path.join('img', 'certificates', image_filename)
            os.makedirs(os.path.join(current_app.static_folder, 'img', 'certificates'), exist_ok=1)
            form.certificate_image.data.save(os.path.join(current_app.static_folder, image_path))
            certificate_image = image_path

        # 计算证书状态
        today = date.today()
        days_to_expiry = (form.expiry_date.data - today).days
        if days_to_expiry < 0:
            status = '已过期'
        elif days_to_expiry <= 30:
            status = '即将过期'
        else:
            status = '有效'

        certificate = SupplierCertificate(
            supplier_id=form.supplier_id.data,
            certificate_type=form.certificate_type.data,
            certificate_number=form.certificate_number.data,
            issue_date=form.issue_date.data,
            expiry_date=form.expiry_date.data,
            issuing_authority=form.issuing_authority.data,
            certificate_image=certificate_image,
            status=status
        )
        db.session.add(certificate)

        # 添加审计日志
        log_activity(
            action='create',
            resource_type='SupplierCertificate',
            resource_id=certificate.id,
            details={
                'supplier_id': certificate.supplier_id,
                'certificate_type': certificate.certificate_type,
                'certificate_number': certificate.certificate_number,
                'issue_date': certificate.issue_date.strftime('%Y-%m-%d'),
                'expiry_date': certificate.expiry_date.strftime('%Y-%m-%d'),
                'status': certificate.status
            }
        )

        db.session.commit()
        flash('供应商证书添加成功！', 'success')
        return redirect(url_for('supplier_certificate.index'))

    return render_template('supplier/certificate_form.html', form=form, title='添加供应商证书', now=datetime.now())

@supplier_certificate_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑供应商证书"""
    certificate = SupplierCertificate.query.get_or_404(id)
    form = SupplierCertificateForm(obj=certificate)

    # 获取供应商选项
    suppliers = Supplier.query.filter_by(status=1).all()  # 只显示合作中的供应商
    form.supplier_id.choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in suppliers]

    if form.validate_on_submit():
        old_data = {
            'supplier_id': certificate.supplier_id,
            'certificate_type': certificate.certificate_type,
            'certificate_number': certificate.certificate_number,
            'issue_date': certificate.issue_date.strftime('%Y-%m-%d'),
            'expiry_date': certificate.expiry_date.strftime('%Y-%m-%d'),
            'issuing_authority': certificate.issuing_authority,
            'status': certificate.status
        }

        # 处理证书图片上传
        if form.certificate_image.data:
            # 删除旧图片
            if certificate.certificate_image:
                old_image_path = os.path.join(current_app.static_folder, certificate.certificate_image)
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)

            # 保存新图片
            image_filename = str(uuid.uuid4()) + os.path.splitext(form.certificate_image.data.filename)[1]
            image_path = os.path.join('img', 'certificates', image_filename)
            os.makedirs(os.path.join(current_app.static_folder, 'img', 'certificates'), exist_ok=1)
            form.certificate_image.data.save(os.path.join(current_app.static_folder, image_path))
            certificate.certificate_image = image_path

        certificate.supplier_id = form.supplier_id.data
        certificate.certificate_type = form.certificate_type.data
        certificate.certificate_number = form.certificate_number.data
        certificate.issue_date = form.issue_date.data
        certificate.expiry_date = form.expiry_date.data
        certificate.issuing_authority = form.issuing_authority.data

        # 计算证书状态
        today = date.today()
        days_to_expiry = (certificate.expiry_date - today).days
        if days_to_expiry < 0:
            certificate.status = '已过期'
        elif days_to_expiry <= 30:
            certificate.status = '即将过期'
        else:
            certificate.status = '有效'

        # 添加审计日志
        log_activity(
            action='update',
            resource_type='SupplierCertificate',
            resource_id=certificate.id,
            details={
                'old': old_data,
                'new': {
                    'supplier_id': certificate.supplier_id,
                    'certificate_type': certificate.certificate_type,
                    'certificate_number': certificate.certificate_number,
                    'issue_date': certificate.issue_date.strftime('%Y-%m-%d'),
                    'expiry_date': certificate.expiry_date.strftime('%Y-%m-%d'),
                    'issuing_authority': certificate.issuing_authority,
                    'status': certificate.status
                }
            }
        )

        db.session.commit()
        flash('供应商证书更新成功！', 'success')
        return redirect(url_for('supplier_certificate.index'))

    return render_template('supplier/certificate_form.html', form=form, certificate=certificate, title='编辑供应商证书', now=datetime.now())

@supplier_certificate_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看供应商证书详情"""
    certificate = SupplierCertificate.query.get_or_404(id)

    # 添加审计日志
    log_activity(
        action='view',
        resource_type='SupplierCertificate',
        resource_id=certificate.id
    )

    return render_template('supplier/certificate_view.html', certificate=certificate, title='供应商证书详情', now=datetime.now())

@supplier_certificate_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除供应商证书"""
    certificate = SupplierCertificate.query.get_or_404(id)

    # 删除证书图片
    if certificate.certificate_image:
        image_path = os.path.join(current_app.static_folder, certificate.certificate_image)
        if os.path.exists(image_path):
            os.remove(image_path)

    # 添加审计日志
    log_activity(
        action='delete',
        resource_type='SupplierCertificate',
        resource_id=certificate.id,
        details={
            'supplier_id': certificate.supplier_id,
            'supplier_name': certificate.supplier.name,
            'certificate_type': certificate.certificate_type,
            'certificate_number': certificate.certificate_number,
            'issue_date': certificate.issue_date.strftime('%Y-%m-%d'),
            'expiry_date': certificate.expiry_date.strftime('%Y-%m-%d')
        }
    )

    db.session.delete(certificate)
    db.session.commit()

    return jsonify({'success': 1, 'message': '供应商证书删除成功！'})

@supplier_certificate_bp.route('/api/by_supplier/<int:supplier_id>')
@login_required
def api_by_supplier(supplier_id):
    """获取供应商的证书"""
    certificates = SupplierCertificate.query.filter_by(supplier_id=supplier_id).all()
    return jsonify([{
        'id': c.id,
        'certificate_type': c.certificate_type,
        'certificate_number': c.certificate_number,
        'issue_date': c.issue_date.strftime('%Y-%m-%d'),
        'expiry_date': c.expiry_date.strftime('%Y-%m-%d'),
        'issuing_authority': c.issuing_authority,
        'status': c.status
    } for c in certificates])

@supplier_certificate_bp.route('/api/expiring')
@login_required
def api_expiring():
    """获取即将过期的证书"""
    certificates = SupplierCertificate.query.filter_by(status='即将过期').all()
    return jsonify([{
        'id': c.id,
        'supplier_id': c.supplier_id,
        'supplier_name': c.supplier.name,
        'certificate_type': c.certificate_type,
        'certificate_number': c.certificate_number,
        'expiry_date': c.expiry_date.strftime('%Y-%m-%d'),
        'days_to_expiry': (c.expiry_date - date.today()).days
    } for c in certificates])
