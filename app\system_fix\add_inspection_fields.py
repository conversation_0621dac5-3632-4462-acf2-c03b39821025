from flask import Blueprint, render_template, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.models import IngredientCategory
from sqlalchemy import text

def add_inspection_fields():
    """添加食材分类表中的检验检疫相关字段"""
    try:
        # 检查字段是否已存在
        conn = db.engine.connect()
        result = conn.execute(text("""
            SELECT COUNT(*) AS count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'ingredient_categories'
            AND COLUMN_NAME = 'needs_inspection'
        """))
        row = result.fetchone()
        
        if row and row[0] > 0:
            # 字段已存在，不需要添加
            return "字段 'needs_inspection' 已存在，无需添加"
        
        # 添加 needs_inspection 字段
        conn.execute(text("""
            ALTER TABLE ingredient_categories
            ADD needs_inspection BIT NOT NULL DEFAULT 0
        """))
        
        # 添加 inspection_type 字段
        conn.execute(text("""
            ALTER TABLE ingredient_categories
            ADD inspection_type NVARCHAR(50) NULL
        """))
        
        conn.close()
        
        return "成功添加检验检疫相关字段"
    except Exception as e:
        return f"添加字段时出错: {str(e)}"
