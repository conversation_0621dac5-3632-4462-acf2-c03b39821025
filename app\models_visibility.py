"""
模块可见性模型
"""

from app import db
from datetime import datetime
from sqlalchemy.dialects.mssql import DATETIME2

class ModuleVisibility(db.Model):
    """模块可见性表"""
    __tablename__ = 'module_visibility'

    id = db.Column(db.Integer, primary_key=True)
    module_id = db.Column(db.String(50), nullable=False)  # 模块ID，对应menu.py中的id
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    is_visible = db.Column(db.Integer, nullable=False, default=1)  # 是否可见，1表示是，0表示否
    created_at = db.Column(DATETIME2(1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(1), default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    role = db.relationship('Role', backref=db.backref('module_visibilities', lazy=True))

    def __repr__(self):
        return f'<ModuleVisibility {self.module_id} for role {self.role_id}>'

    @staticmethod
    def get_visibility(module_id, role_id):
        """获取模块对特定角色的可见性"""
        try:
            # 使用原始SQL查询，避免SQLAlchemy的ORM层
            from sqlalchemy import text
            from flask import current_app

            sql = text("""
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = :module_id AND role_id = :role_id
            """)

            # 获取一个新的数据库连接
            with db.engine.connect() as conn:
                result = conn.execute(sql, {"module_id": module_id, "role_id": role_id})
                row = result.fetchone()

                if row:
                    # 将整数值转换为布尔值
                    return bool(row[0])
                return True  # 默认可见

        except Exception as e:
            # 记录错误但不中断应用
            if current_app:
                current_app.logger.error(f"获取模块可见性时出错: {str(e)}")
            return True  # 出错时默认可见

    @staticmethod
    def set_visibility(module_id, role_id, is_visible, user_id=None):
        """设置模块对特定角色的可见性"""
        # 确保 is_visible 是 SQL Server 可以接受的类型
        if is_visible is not None:
            # 将 is_visible 转换为整数 1 或 0
            is_visible = 1 if is_visible else 0

        visibility = ModuleVisibility.query.filter_by(module_id=module_id, role_id=role_id).first()
        if visibility:
            visibility.is_visible = is_visible
            visibility.updated_at = datetime.now().replace(microsecond=0)
        else:
            # 使用原始 SQL 插入记录，避免 SQLAlchemy 的类型转换
            from sqlalchemy import text
            sql = text("""
                INSERT INTO module_visibility (module_id, role_id, is_visible, created_at, updated_at)
                VALUES (:module_id, :role_id, :is_visible, :created_at, :updated_at)
            """)

            now = datetime.now().replace(microsecond=0)

            db.session.execute(sql, {
                'module_id': module_id,
                'role_id': role_id,
                'is_visible': is_visible,
                'created_at': now,
                'updated_at': now
            })

            # 获取刚刚插入的记录
            visibility = ModuleVisibility.query.filter_by(module_id=module_id, role_id=role_id).first()

        db.session.commit()
        return visibility

    @staticmethod
    def get_all_visibilities():
        """获取所有可见性设置"""
        return ModuleVisibility.query.all()

    @staticmethod
    def get_role_visibilities(role_id):
        """获取特定角色的所有可见性设置"""
        return ModuleVisibility.query.filter_by(role_id=role_id).all()

    @staticmethod
    def get_module_visibilities(module_id):
        """获取特定模块的所有可见性设置"""
        return ModuleVisibility.query.filter_by(module_id=module_id).all()
