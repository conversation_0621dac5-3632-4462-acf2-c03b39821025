from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.main import main_bp
from app.models import Supplier, Ingredient, Recipe, FoodSample, PurchaseOrder, MenuPlan, MenuRecipe, WeeklyMenu, WeeklyMenuRecipe
from datetime import datetime, date, timedelta
from app.utils.process_status import (
    get_menu_plan_status, get_purchase_plan_status, get_inspection_status,
    get_storage_in_status, get_consumption_plan_status, get_storage_out_status,
    get_inventory_status, get_samples_status, get_tracing_status,
    get_today_tasks, calculate_progress_percentage
)
from app.models_daily_management import DiningCompanion, Photo, DailyLog
from sqlalchemy import desc

@main_bp.route('/')
def index():
    """首页"""
    if current_user.is_authenticated:
        # 获取各环节状态
        menu_plan = get_menu_plan_status()
        purchase_plan = get_purchase_plan_status()
        inspection = get_inspection_status()
        storage_in = get_storage_in_status()
        consumption_plan = get_consumption_plan_status()
        storage_out = get_storage_out_status()
        inventory = get_inventory_status()
        samples = get_samples_status()
        tracing = get_tracing_status()

        # 获取今日待办任务
        today_tasks = get_today_tasks()

        # 计算整体进度
        all_steps = [menu_plan, purchase_plan, inspection, storage_in,
                    consumption_plan, storage_out, inventory, samples, tracing]
        progress_percentage = calculate_progress_percentage(all_steps)

        # 获取可用路由
        available_routes = []

        # 直接获取最近的陪餐记录（最多5条）
        recent_companions = []
        try:
            # 获取当前用户可访问的区域
            accessible_areas = current_user.get_accessible_areas()
            area_ids = [area.id for area in accessible_areas]

            # 获取这些区域的所有日志ID
            log_ids = []
            if area_ids:
                logs = DailyLog.query.filter(DailyLog.area_id.in_(area_ids)).all()
                log_ids = [log.id for log in logs]

            # 查询陪餐记录
            if log_ids:
                companions = DiningCompanion.query.filter(DiningCompanion.daily_log_id.in_(log_ids)).order_by(desc(DiningCompanion.dining_time)).limit(5).all()

                for companion in companions:
                    # 获取关联的日志
                    log = DailyLog.query.get(companion.daily_log_id)

                    # 检查是否有照片
                    has_photo = Photo.query.filter_by(
                        reference_id=companion.id,
                        reference_type='companion'
                    ).count() > 0

                    # 构建记录数据
                    record = {
                        'id': companion.id,
                        'name': companion.companion_name,
                        'role': companion.companion_role,
                        'time': companion.dining_time.strftime('%H:%M') if hasattr(companion.dining_time, 'strftime') else '',
                        'date': log.log_date.strftime('%Y-%m-%d') if log and hasattr(log.log_date, 'strftime') else '',
                        'meal_type': companion.meal_type,
                        'comments': companion.comments or '',
                        'has_photo': has_photo,
                        'area_name': log.area.name if log and hasattr(log, 'area') and log.area else '未知区域'
                    }
                    recent_companions.append(record)
        except Exception as e:
            current_app.logger.error(f"获取陪餐记录失败: {str(e)}")

        # 获取今日菜单数据
        today_menu = {
            '早餐': None,
            '午餐': None,
            '晚餐': None
        }

        try:
            today_date = date.today()

            # 从菜单计划获取今日菜单
            if area_ids:
                menu_plans = MenuPlan.query.filter(
                    MenuPlan.plan_date == today_date,
                    MenuPlan.area_id.in_(area_ids)
                ).all()

                for plan in menu_plans:
                    if plan.meal_type in today_menu:
                        recipes = []
                        for menu_recipe in MenuRecipe.query.filter_by(menu_plan_id=plan.id).all():
                            recipe = Recipe.query.get(menu_recipe.recipe_id)
                            if recipe:
                                recipes.append({
                                    'id': recipe.id,
                                    'name': recipe.name,
                                    'quantity': menu_recipe.planned_quantity
                                })

                        today_menu[plan.meal_type] = {
                            'id': plan.id,
                            'status': plan.status,
                            'expected_diners': plan.expected_diners,
                            'recipes': recipes
                        }

                # 如果某个餐次没有菜单计划，尝试从周菜单获取
                for meal_type in today_menu:
                    if today_menu[meal_type] is None:
                        # 获取当前是周几（0-6，0表示周一）
                        weekday = today_date.weekday()

                        # 查找包含当前日期的周菜单
                        week_start = today_date - timedelta(days=weekday)
                        week_end = week_start + timedelta(days=6)

                        weekly_menu = WeeklyMenu.query.filter(
                            WeeklyMenu.week_start <= today_date,
                            WeeklyMenu.week_end >= today_date,
                            WeeklyMenu.area_id.in_(area_ids),
                            WeeklyMenu.status == '已发布'
                        ).first()

                        if weekly_menu:
                            # 查找对应日期和餐次的菜谱
                            weekly_recipes = WeeklyMenuRecipe.query.filter(
                                WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                                WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                                WeeklyMenuRecipe.meal_type == meal_type
                            ).all()

                            if weekly_recipes:
                                recipes = []
                                for weekly_recipe in weekly_recipes:
                                    recipe = Recipe.query.get(weekly_recipe.recipe_id)
                                    if recipe:
                                        recipes.append({
                                            'id': recipe.id,
                                            'name': recipe.name,
                                            'quantity': weekly_recipe.quantity
                                        })

                                today_menu[meal_type] = {
                                    'id': None,
                                    'status': '计划中',
                                    'expected_diners': None,
                                    'recipes': recipes
                                }
        except Exception as e:
            current_app.logger.error(f"获取今日菜单失败: {str(e)}")

        # 使用改版后的食堂管理仪表盘，突出食堂日常管理功能
        return render_template('main/canteen_dashboard_new.html',
                              title='食堂管理仪表盘',
                              now=datetime.now(),
                              menu_plan=menu_plan,
                              purchase_plan=purchase_plan,
                              inspection=inspection,
                              storage_in=storage_in,
                              consumption_plan=consumption_plan,
                              storage_out=storage_out,
                              inventory=inventory,
                              samples=samples,
                              tracing=tracing,
                              progress_percentage=progress_percentage,
                              today_tasks=today_tasks,
                              available_routes=available_routes,
                              recent_companions=recent_companions,
                              today_menu=today_menu)
    return render_template('main/index.html', title='首页', now=datetime.now())

@main_bp.route('/suppliers')
@login_required
def suppliers():
    """供应商管理（重定向到新的供应商管理模块）"""
    return redirect(url_for('supplier.index'))

@main_bp.route('/ingredients')
@login_required
def ingredients():
    """食材管理"""
    page = request.args.get('page', 1, type=int)
    ingredients = Ingredient.query.order_by(Ingredient.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/ingredients.html', title='食材管理', ingredients=ingredients, now=datetime.now())

@main_bp.route('/recipes')
@login_required
def recipes():
    """食谱管理"""
    page = request.args.get('page', 1, type=int)
    recipes = Recipe.query.order_by(Recipe.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/recipes.html', title='食谱管理', recipes=recipes, now=datetime.now())

@main_bp.route('/food-samples')
@login_required
def food_samples():
    """留样管理"""
    page = request.args.get('page', 1, type=int)
    samples = FoodSample.query.order_by(FoodSample.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/food_samples.html', title='留样管理', samples=samples, now=datetime.now())

@main_bp.route('/purchase-orders')
@login_required
def purchase_orders():
    """采购订单"""
    page = request.args.get('page', 1, type=int)
    orders = PurchaseOrder.query.order_by(PurchaseOrder.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/purchase_orders.html', title='采购订单', orders=orders, now=datetime.now())
