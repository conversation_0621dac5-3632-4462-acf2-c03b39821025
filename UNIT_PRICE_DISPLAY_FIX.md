# 单价显示问题修复报告

## 问题确认

用户发现：
1. **保存功能正常**：在批次编辑器中编辑单价后，数据确实保存到了数据库
2. **显示问题**：每次打开批次编辑器时，单价字段没有显示数据库中已保存的值
3. **数据库完整**：`stock_in_items` 表中已经有 `unit_price` 字段

## 问题分析

### 数据库状态
- ✅ **数据库表结构正确**：`stock_in_items` 表包含 `unit_price` 字段
- ✅ **数据保存正常**：编辑器保存功能工作正常
- ✅ **数据存在**：数据库中确实有单价数据

### 模型定义状态
- ✅ **模型字段已定义**：`StockInItem` 模型中已有 `unit_price` 字段定义
- ✅ **字段类型正确**：`unit_price = db.Column(db.Float, nullable=1)`

### 问题根源
问题在于**数据读取和显示**环节，而不是保存环节。

## 修复方案

### 1. 添加调试日志

**文件**: `app/routes/stock_in.py`

在 `batch_editor_simplified` 函数中添加调试日志：

```python
# 获取所有入库明细
stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()

# 调试日志：检查单价字段
current_app.logger.info(f"批次编辑器 - 入库单ID: {id}")
for item in stock_in_items:
    current_app.logger.info(f"项目ID: {item.id}, 食材: {item.ingredient.name}, 单价: {item.unit_price}")
```

### 2. 检查模板渲染

**文件**: `app/templates/stock_in/batch_editor_simplified.html`

确认单价输入框的值绑定：

```html
<input type="number" class="form-control form-control-lg font-weight-bold text-danger"
       name="unit_price_{{ item.id }}" value="{{ item.unit_price }}" step="0.01" min="0"
       style="font-size: 1.2rem;" required>
```

### 3. 可能的问题和解决方案

#### 问题1：模型字段未正确加载
**现象**：`item.unit_price` 返回 `None`
**解决方案**：
- 重启Flask应用以确保模型更改生效
- 检查SQLAlchemy是否正确映射字段

#### 问题2：数据库连接问题
**现象**：查询返回的对象缺少某些字段
**解决方案**：
- 检查数据库连接配置
- 确认数据库表结构与模型定义一致

#### 问题3：模板渲染问题
**现象**：`{{ item.unit_price }}` 显示为空
**解决方案**：
- 在模板中添加调试输出：`{{ item.unit_price|default('NULL') }}`
- 检查Jinja2模板语法

#### 问题4：浏览器缓存
**现象**：页面显示旧的内容
**解决方案**：
- 强制刷新浏览器（Ctrl+F5）
- 清除浏览器缓存

## 调试步骤

### 1. 检查服务器日志
重启Flask应用后，访问批次编辑器页面，查看终端输出：
```
批次编辑器 - 入库单ID: 76
项目ID: XXX, 食材: XXX, 单价: XXX
```

### 2. 检查数据库数据
在数据库中直接查询：
```sql
SELECT id, ingredient_id, unit_price 
FROM stock_in_items 
WHERE stock_in_id = 76;
```

### 3. 检查模板输出
在批次编辑器模板中临时添加调试输出：
```html
<!-- 调试信息 -->
<div style="display:none;">
  单价调试: {{ item.unit_price|default('NULL') }}
</div>
```

### 4. 检查浏览器开发者工具
- 查看页面源代码中单价输入框的 `value` 属性
- 检查是否有JavaScript错误

## 预期结果

修复后的效果：

### 批次编辑器页面
- ✅ 单价输入框显示数据库中保存的值
- ✅ 如果数据库中单价为 `NULL`，显示空值
- ✅ 用户可以编辑单价并保存

### 入库单详情页面
- ✅ 单价列显示正确的数值
- ✅ 如果单价为空，显示 `-`

### 服务器日志
- ✅ 显示每个项目的单价值
- ✅ 可以确认数据是否正确加载

## 测试验证

### 完整测试流程
1. **重启应用**：确保模型更改生效
2. **访问编辑器**：`http://127.0.0.1:5000/stock-in/76/batch-editor-simplified`
3. **查看日志**：确认单价值被正确读取
4. **检查页面**：确认输入框显示正确的值
5. **编辑保存**：测试编辑和保存功能
6. **查看详情**：`http://127.0.0.1:5000/stock-in/76/details`

### 测试脚本
运行测试脚本验证：
```bash
python test_unit_price_display.py
```

## 常见解决方案

### 方案1：重启应用
最简单的解决方案，重启Flask应用：
```bash
python run.py
```

### 方案2：清除缓存
清除浏览器缓存并强制刷新页面

### 方案3：检查数据库
确认数据库中确实有单价数据：
```sql
UPDATE stock_in_items 
SET unit_price = 10.0 
WHERE stock_in_id = 76 AND unit_price IS NULL;
```

### 方案4：模板调试
在模板中添加详细的调试信息，确认数据传递

## 总结

单价显示问题的核心在于：
1. **数据库有数据** ✅
2. **模型有定义** ✅  
3. **保存功能正常** ✅
4. **读取显示有问题** ❌

通过添加调试日志和检查模板渲染，可以快速定位问题所在。最可能的解决方案是重启Flask应用，确保模型更改生效。

**关键检查点**：
- 服务器日志中的单价值
- 页面源代码中输入框的 `value` 属性
- 数据库中的实际数据
- 浏览器缓存状态
