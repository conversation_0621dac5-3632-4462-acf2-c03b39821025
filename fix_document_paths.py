#!/usr/bin/env python3
"""
修复入库单文档路径中的反斜杠问题
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def fix_document_paths():
    """修复数据库中文档路径的反斜杠问题"""
    print("🔧 开始修复入库单文档路径...")
    
    # 数据库连接配置
    # 请根据实际情况修改数据库连接字符串
    DATABASE_URL = "mssql+pyodbc://sa:123456@localhost/StudentsCMSSP?driver=ODBC+Driver+17+for+SQL+Server"
    
    try:
        # 创建数据库连接
        engine = create_engine(DATABASE_URL)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        print("✅ 数据库连接成功")
        
        # 查询所有包含反斜杠的文档路径
        query_sql = text("""
        SELECT id, file_path 
        FROM stock_in_documents 
        WHERE file_path LIKE '%\\%'
        """)
        
        result = session.execute(query_sql)
        documents = result.fetchall()
        
        if not documents:
            print("✅ 没有需要修复的文档路径")
            return True
        
        print(f"📋 找到 {len(documents)} 个需要修复的文档路径")
        
        # 修复每个文档的路径
        fixed_count = 0
        for doc_id, file_path in documents:
            # 将反斜杠替换为正斜杠
            fixed_path = file_path.replace('\\', '/')
            
            print(f"  修复文档 {doc_id}:")
            print(f"    原路径: {file_path}")
            print(f"    新路径: {fixed_path}")
            
            # 更新数据库
            update_sql = text("""
            UPDATE stock_in_documents 
            SET file_path = :new_path 
            WHERE id = :doc_id
            """)
            
            session.execute(update_sql, {
                'new_path': fixed_path,
                'doc_id': doc_id
            })
            
            fixed_count += 1
        
        # 提交更改
        session.commit()
        
        print(f"\n🎉 成功修复 {fixed_count} 个文档路径")
        
        # 验证修复结果
        verify_sql = text("""
        SELECT COUNT(*) 
        FROM stock_in_documents 
        WHERE file_path LIKE '%\\%'
        """)
        
        remaining_count = session.execute(verify_sql).scalar()
        
        if remaining_count == 0:
            print("✅ 所有文档路径已修复完成")
            return True
        else:
            print(f"⚠️ 还有 {remaining_count} 个文档路径未修复")
            return False
            
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        if 'session' in locals():
            session.rollback()
        return False
    finally:
        if 'session' in locals():
            session.close()

def test_document_access():
    """测试修复后的文档访问"""
    print("\n🧪 测试文档访问...")
    
    import requests
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试入库单详情页面
        response = requests.get(f"{base_url}/stock-in/76/details", timeout=10)
        
        if response.status_code == 200:
            print("✅ 入库单详情页面访问正常")
            
            # 检查页面中的文档链接
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找文档链接
            doc_links = soup.find_all('a', href=True)
            static_links = [link for link in doc_links if '/static/uploads/' in link.get('href', '')]
            
            if static_links:
                print(f"📋 找到 {len(static_links)} 个文档链接")
                
                for i, link in enumerate(static_links[:3]):  # 只测试前3个
                    href = link.get('href')
                    print(f"  测试链接 {i+1}: {href}")
                    
                    # 测试链接访问
                    try:
                        doc_response = requests.head(base_url + href, timeout=10)
                        if doc_response.status_code == 200:
                            print(f"    ✅ 文档可正常访问")
                        else:
                            print(f"    ❌ 文档访问失败，状态码: {doc_response.status_code}")
                    except Exception as e:
                        print(f"    ❌ 请求异常: {e}")
            else:
                print("ℹ️ 页面中没有找到文档链接")
        else:
            print(f"❌ 入库单详情页面访问失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

def check_file_existence():
    """检查文件是否存在于正确位置"""
    print("\n📁 检查文件存在性...")
    
    # 检查上传目录
    upload_dir = "app/static/uploads/stock_in_docs"
    
    if os.path.exists(upload_dir):
        print(f"✅ 上传目录存在: {upload_dir}")
        
        # 遍历子目录
        for subdir in os.listdir(upload_dir):
            subdir_path = os.path.join(upload_dir, subdir)
            
            if os.path.isdir(subdir_path):
                files = os.listdir(subdir_path)
                print(f"  📂 {subdir}: {len(files)} 个文件")
                
                # 显示前几个文件
                for file in files[:3]:
                    file_path = os.path.join(subdir_path, file)
                    file_size = os.path.getsize(file_path)
                    print(f"    📄 {file} ({file_size} bytes)")
    else:
        print(f"❌ 上传目录不存在: {upload_dir}")

def main():
    """主函数"""
    print("🚀 入库单文档路径修复工具\n")
    
    # 1. 修复数据库中的路径
    success = fix_document_paths()
    
    # 2. 检查文件存在性
    check_file_existence()
    
    # 3. 测试文档访问
    test_document_access()
    
    # 输出结果
    print(f"\n{'='*60}")
    print("📊 修复结果")
    print('='*60)
    
    if success:
        print("🎉 文档路径修复成功！")
        print("\n✅ 完成的操作:")
        print("  - 修复了数据库中的反斜杠路径")
        print("  - 验证了文件存在性")
        print("  - 测试了文档访问功能")
        
        print("\n💡 建议:")
        print("  - 重启Flask应用以确保更改生效")
        print("  - 清除浏览器缓存")
        print("  - 测试文档上传和访问功能")
    else:
        print("❌ 文档路径修复失败！")
        print("\n⚠️ 可能的问题:")
        print("  - 数据库连接失败")
        print("  - 权限不足")
        print("  - SQL语句执行错误")
        
        print("\n🔧 解决方案:")
        print("  - 检查数据库连接配置")
        print("  - 确认数据库用户权限")
        print("  - 手动执行SQL更新语句")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
