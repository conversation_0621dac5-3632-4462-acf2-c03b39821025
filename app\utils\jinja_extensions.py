"""
Jinja2 扩展

提供自定义的 Jinja2 扩展，用于增强模板功能。
"""

from jinja2 import nodes
from jinja2.ext import Extension
from jinja2.parser import Parser
from jinja2.lexer import Token
from app.utils.datetime_helper import ensure_datetime, safe_strftime

class SafeStrftimeExtension(Extension):
    """
    安全的 strftime 扩展

    自动将 .strftime() 调用转换为安全的日期时间格式化函数调用，
    避免 'str' object has no attribute 'strftime' 错误。
    """

    # 扩展标识符
    identifier = 'safe_strftime'

    # 优先级
    priority = 100

    def __init__(self, environment):
        """初始化扩展"""
        super(SafeStrftimeExtension, self).__init__(environment)

        # 保存原始的 getattr 函数
        self._original_getattr = environment.getattr

        # 替换 getattr 函数
        environment.getattr = self._safe_getattr

        # 添加过滤器
        environment.filters['safe_strftime'] = self._safe_strftime_filter

    def _safe_getattr(self, obj, attribute, *args, **kwargs):
        """
        安全的 getattr 函数

        如果属性是 'strftime'，则返回一个安全的 strftime 函数。
        """
        if attribute == 'strftime':
            # 返回一个包装函数，该函数接受格式字符串并返回格式化后的日期时间
            def safe_strftime_wrapper(format_str):
                return safe_strftime(obj, format_str)
            return safe_strftime_wrapper

        # 否则使用原始的 getattr 函数，传递所有参数
        return self._original_getattr(obj, attribute, *args, **kwargs)

    def _safe_strftime_filter(self, value, format_str='%Y-%m-%d %H:%M:%S'):
        """
        安全的 strftime 过滤器

        Args:
            value: 输入值，可以是 datetime 对象、date 对象、字符串或 None
            format_str: 格式化字符串

        Returns:
            str: 格式化后的字符串，如果输入无效则返回空字符串
        """
        return safe_strftime(value, format_str)

def register_extensions(app):
    """
    注册所有 Jinja2 扩展

    Args:
        app: Flask 应用实例
    """
    # 注册安全的 strftime 扩展
    app.jinja_env.add_extension(SafeStrftimeExtension)
