-- 强制删除并重建 daily_logs 表
-- 步骤1: 保存现有数据到临时表
SELECT * INTO #temp_daily_logs FROM daily_logs;
PRINT '已将数据保存到临时表';

-- 步骤2: 查找并删除所有引用 daily_logs 表的外键约束
DECLARE @constraint_name NVARCHAR(128);
DECLARE @table_name NVARCHAR(128);
DECLARE @sql NVARCHAR(500);

-- 创建临时表存储约束信息
CREATE TABLE #temp_constraints (
    constraint_name NVARCHAR(128),
    table_name NVARCHAR(128)
);

-- 查找所有引用 daily_logs 表的外键约束
INSERT INTO #temp_constraints
SELECT 
    f.name AS constraint_name,
    OBJECT_NAME(f.parent_object_id) AS table_name
FROM 
    sys.foreign_keys AS f
WHERE 
    OBJECT_NAME(f.referenced_object_id) = 'daily_logs';

-- 删除所有找到的外键约束
DECLARE constraint_cursor CURSOR FOR 
SELECT constraint_name, table_name FROM #temp_constraints;

OPEN constraint_cursor;
FETCH NEXT FROM constraint_cursor INTO @constraint_name, @table_name;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @sql = N'ALTER TABLE ' + QUOTENAME(@table_name) + 
               N' DROP CONSTRAINT ' + QUOTENAME(@constraint_name);
    EXEC sp_executesql @sql;
    PRINT '已删除约束: ' + @constraint_name + ' 从表: ' + @table_name;
    
    FETCH NEXT FROM constraint_cursor INTO @constraint_name, @table_name;
END

CLOSE constraint_cursor;
DEALLOCATE constraint_cursor;

-- 删除临时表
DROP TABLE #temp_constraints;
PRINT '已删除所有引用 daily_logs 表的外键约束';

-- 步骤3: 删除 daily_logs 表
DROP TABLE daily_logs;
PRINT '已删除 daily_logs 表';

-- 步骤4: 创建新的 daily_logs 表，使用 DATETIME2 类型
CREATE TABLE daily_logs (
    id INT PRIMARY KEY IDENTITY(1,1),
    log_date DATETIME2(1) NOT NULL,
    weather NVARCHAR(50) NULL,
    manager NVARCHAR(100) NULL,
    student_count INT DEFAULT 0,
    teacher_count INT DEFAULT 0,
    other_count INT DEFAULT 0,
    breakfast_menu NVARCHAR(MAX) NULL,
    lunch_menu NVARCHAR(MAX) NULL,
    dinner_menu NVARCHAR(MAX) NULL,
    food_waste FLOAT NULL,
    special_events NVARCHAR(MAX) NULL,
    operation_summary NVARCHAR(MAX) NULL,
    area_id INT NULL,
    created_by INT NULL,
    created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE()
);
PRINT '已创建新的 daily_logs 表';

-- 步骤5: 创建索引和约束
CREATE INDEX idx_daily_logs_log_date ON daily_logs(log_date);
CREATE INDEX idx_daily_logs_area_id ON daily_logs(area_id);
ALTER TABLE daily_logs ADD CONSTRAINT UQ_daily_logs_date_area UNIQUE (log_date, area_id);
ALTER TABLE daily_logs ADD CONSTRAINT FK_daily_logs_area_id FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
ALTER TABLE daily_logs ADD CONSTRAINT FK_daily_logs_created_by FOREIGN KEY (created_by) REFERENCES users(id);
PRINT '已添加索引和约束';

-- 步骤6: 恢复数据
SET IDENTITY_INSERT daily_logs ON;
INSERT INTO daily_logs (
    id, log_date, weather, manager, student_count, teacher_count, other_count,
    breakfast_menu, lunch_menu, dinner_menu, food_waste, special_events,
    operation_summary, area_id, created_by, created_at, updated_at
)
SELECT 
    id, 
    CONVERT(DATETIME2(1), log_date, 23), -- 转换日期格式
    weather, 
    manager, 
    student_count, 
    teacher_count, 
    other_count,
    breakfast_menu, 
    lunch_menu, 
    dinner_menu, 
    food_waste, 
    special_events,
    operation_summary, 
    area_id, 
    created_by, 
    created_at, 
    updated_at
FROM #temp_daily_logs;
SET IDENTITY_INSERT daily_logs OFF;
PRINT '已恢复数据到新表';

-- 步骤7: 重建引用 daily_logs 表的外键约束
-- inspection_records 表
ALTER TABLE inspection_records ADD CONSTRAINT FK_inspection_records_daily_log_id 
FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id);

-- dining_companions 表
ALTER TABLE dining_companions ADD CONSTRAINT FK_dining_companions_daily_log_id 
FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id);

-- canteen_training_records 表
ALTER TABLE canteen_training_records ADD CONSTRAINT FK_canteen_training_records_daily_log_id 
FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id);

-- special_events 表
ALTER TABLE special_events ADD CONSTRAINT FK_special_events_daily_log_id 
FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id);

-- issues 表
ALTER TABLE issues ADD CONSTRAINT FK_issues_daily_log_id 
FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id);

PRINT '已重建所有引用 daily_logs 表的外键约束';

-- 步骤8: 删除临时表
DROP TABLE #temp_daily_logs;
PRINT '已删除临时表';

PRINT '已成功完成 daily_logs 表的重建';
