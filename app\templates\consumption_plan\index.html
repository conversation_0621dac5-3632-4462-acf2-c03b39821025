{% extends 'base.html' %}

{% block title %}消耗计划管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 消耗计划流程引导 -->
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-clipboard-list"></i> 消耗计划 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle" style="font-size: 1.2rem; margin-right: 10px;"></i> <strong>提示：</strong> 消耗计划是连接入库和出库的重要环节，合理的消耗计划可以确保食材高效利用，减少浪费。
            </div>

            <div class="d-flex justify-content-between mt-3" style="margin-top: 10px;">
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-dolly"></i> 食材入库</p>
                    <small>已完成食材入库</small>
                    <div class="mt-2">
                        <a href="{{ url_for('stock_in.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回入库管理
                        </a>
                    </div>
                </div>
                <div style="flex: 1; padding: 0 10px; border-left: 1px solid #dee2e6; border-right: 1px solid #dee2e6;" class="bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="font-weight-bold"><i class="fas fa-clipboard-list"></i> 消耗计划</p>
                    <small>制定食材消耗计划</small>
                </div>
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-sign-out-alt"></i> 食材出库</p>
                    <small>执行食材出库操作</small>
                    <div class="mt-2">
                        <a href="{{ url_for('stock_out.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往出库管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>可以从已发布的菜单计划创建消耗计划，也可以直接创建消耗计划</li>
                    <li>消耗计划创建后需要审核，审核通过后才能执行</li>
                    <li>执行消耗计划会自动生成出库单，更新库存</li>
                    <li>消耗计划执行后可以进行溯源查询，追踪食材使用情况</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 顶部操作区 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">消耗计划操作</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('menu_plan.index', status='已发布') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-plus"></i> 从菜单计划创建
                            </a>
                            <small class="text-muted">从已发布的菜单计划创建消耗计划</small>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('consumption_plan.new') }}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle"></i> 直接创建消耗计划
                            </a>
                            <small class="text-muted">不关联菜单计划，直接创建消耗计划</small>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> 消耗计划流程：创建计划 → 审核计划 → 执行计划(生成出库单) → 完成出库
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 流程状态展示 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">消耗计划状态</h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col">
                            <div class="info-box bg-secondary">
                                <span class="info-box-icon"><i class="fas fa-pencil-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">计划中</span>
                                    <span class="info-box-number">{{ plan_counts.planning or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已审核</span>
                                    <span class="info-box-number">{{ plan_counts.approved or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-dolly"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已执行</span>
                                    <span class="info-box-number">{{ plan_counts.executed or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-ban"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已取消</span>
                                    <span class="info-box-number">{{ plan_counts.cancelled or 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索区 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">筛选条件</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form method="get" action="{{ url_for('consumption_plan.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>状态</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部状态</option>
                                        <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
                                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                                        <option value="已执行" {% if status == '已执行' %}selected{% endif %}>已执行</option>
                                        <option value="已取消" {% if status == '已取消' %}selected{% endif %}>已取消</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>开始日期</label>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>结束日期</label>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 消耗计划列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">消耗计划列表</h3>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>消耗日期</th>
                                <th>餐次</th>
                                <th>用餐人数</th>
                                <th>区域</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>创建人</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for consumption_plan in consumption_plans %}
                            <tr>
                                <td>{{ consumption_plan.id }}</td>
                                <td>
                                    {% if consumption_plan.consumption_date %}
                                        {{ consumption_plan.consumption_date|format_datetime('%Y-%m-%d') }}
                                    {% elif consumption_plan.menu_plan %}
                                        {{ consumption_plan.menu_plan.plan_date|format_datetime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if consumption_plan.meal_type %}
                                        {{ consumption_plan.meal_type }}
                                    {% elif consumption_plan.menu_plan %}
                                        {{ consumption_plan.menu_plan.meal_type }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if consumption_plan.menu_plan and consumption_plan.menu_plan.expected_diners %}
                                        {{ consumption_plan.menu_plan.expected_diners }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if consumption_plan.menu_plan and consumption_plan.menu_plan.area and consumption_plan.menu_plan.area.name %}
                                        {{ consumption_plan.menu_plan.area.name }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge badge-{{ consumption_plan.status|status_class }}">
                                        {{ consumption_plan.status }}
                                    </span>
                                </td>
                                <td>{{ consumption_plan.created_at|format_datetime }}</td>
                                <td>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('consumption_plan.view', id=consumption_plan.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>

                                        {% if consumption_plan.status == '计划中' %}
                                        <a href="{{ url_for('consumption_plan.edit', id=consumption_plan.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <button type="button" class="btn btn-sm btn-success"
                                                onclick="approveConfirm('{{ url_for('consumption_plan.approve', id=consumption_plan.id) }}')">
                                            <i class="fas fa-check"></i> 审核
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                                            <i class="fas fa-ban"></i> 取消
                                        </button>
                                        {% endif %}

                                        {% if consumption_plan.status == '已审核' %}
                                        <button type="button" class="btn btn-sm btn-warning"
                                                onclick="executeConfirm('{{ url_for('consumption_plan.execute', id=consumption_plan.id) }}')">
                                            <i class="fas fa-dolly"></i> 执行
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                                            <i class="fas fa-ban"></i> 取消
                                        </button>
                                        {% endif %}

                                        {% if consumption_plan.status == '已执行' %}
                                        <a href="{{ url_for('traceability.interface') }}?trace_type=consumption_plan&trace_id={{ consumption_plan.id }}"
                                           class="btn btn-sm btn-secondary">
                                            <i class="fas fa-search"></i> 溯源
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center">暂无消耗计划</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="card-footer clearfix">
                    {% if pagination.pages > 1 %}
                    <ul class="pagination pagination-sm m-0 float-right">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('consumption_plan.index', page=pagination.prev_num, status=status, start_date=start_date, end_date=end_date) }}">
                                «
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">«</span>
                        </li>
                        {% endif %}

                        {% for page in pagination.iter_pages() %}
                            {% if page %}
                                {% if page != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('consumption_plan.index', page=page, status=status, start_date=start_date, end_date=end_date) }}">
                                        {{ page }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('consumption_plan.index', page=pagination.next_num, status=status, start_date=start_date, end_date=end_date) }}">
                                »
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">»</span>
                        </li>
                        {% endif %}
                    </ul>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 确认审核
    function approveConfirm(url) {
        if (confirm('确定要审核该消耗计划吗？')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // 确认执行
    function executeConfirm(url) {
        if (confirm('确定要执行该消耗计划吗？执行后将生成出库单。')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // 确认取消
    function cancelConfirm(url) {
        if (confirm('确定要取消该消耗计划吗？此操作不可恢复。')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
