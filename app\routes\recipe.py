from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Recipe, RecipeCategory, RecipeIngredient, RecipeProcess, RecipeProcessIngredient
from app.utils import log_activity
from werkzeug.utils import secure_filename
import os
from datetime import datetime
from sqlalchemy import text

recipe_bp = Blueprint('recipe', __name__)

@recipe_bp.route('/')
@login_required
def index():
    """食谱列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    query = Recipe.query

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Recipe.name.like(f'%{keyword}%'))

    pagination = query.order_by(Recipe.id.desc()).paginate(page=page, per_page=per_page)
    recipes = pagination.items

    categories = RecipeCategory.query.all()

    return render_template('recipe/index.html',
                          recipes=recipes,
                          pagination=pagination,
                          categories=categories,
                          category_id=category_id,
                          keyword=keyword)

@recipe_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建食谱"""
    from app.forms.recipe import RecipeForm

    form = RecipeForm()

    # 动态加载分类选项
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [
        (c.id, c.name) for c in RecipeCategory.query.all()
    ]

    if form.validate_on_submit():
        # 处理图片上传
        main_image = None
        if form.main_image.data:
            file = form.main_image.data
            filename = secure_filename(file.filename)
            # 确保文件名唯一
            unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
            upload_folder = os.path.join(current_app.static_folder, 'uploads/recipes')

            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=True)

            file_path = os.path.join(upload_folder, unique_filename)
            file.save(file_path)

            # 存储相对路径
            main_image = f"uploads/recipes/{unique_filename}"

        # 使用原生SQL创建食谱记录

        # 准备参数
        category_id = form.category_id.data if form.category_id.data and form.category_id.data != 0 else None
        priority = 10 if form.is_user_defined.data else 0  # 用户自定义食谱优先级设为10

        # 获取分类名称
        category_name = None
        if category_id:
            category = RecipeCategory.query.get(category_id)
            if category:
                category_name = category.name

        # 使用原生SQL插入记录，不包含created_at和updated_at字段
        sql = text("""
            INSERT INTO recipes
            (name, category, category_id, meal_type, main_image, description, status, is_user_defined, priority, created_by)
            OUTPUT inserted.id
            VALUES
            (:name, :category, :category_id, :meal_type, :main_image, :description, :status, :is_user_defined, :priority, :created_by)
        """)

        params = {
            'name': form.name.data,
            'category': category_name,
            'category_id': category_id,
            'meal_type': form.meal_type.data,
            'main_image': main_image,
            'description': form.description.data,
            'status': form.status.data,
            'is_user_defined': 1 if form.is_user_defined.data else 0,  # 转换为整数
            'priority': priority,
            'created_by': current_user.id
        }

        try:
            # 执行SQL并获取新插入记录的ID
            result = db.session.execute(sql, params)
            recipe_id = result.fetchone()[0]

            # 创建一个Recipe对象用于后续操作
            recipe = Recipe.query.get(recipe_id)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建食谱失败: {str(e)}")
            flash(f'创建食谱失败: {str(e)}', 'danger')
            return render_template('recipe/create.html', form=form, recipe=None)

        # 处理食材关联
        ingredient_ids = request.form.getlist('ingredient_ids[]')
        ingredient_quantities = request.form.getlist('ingredient_quantities[]')
        ingredient_units = request.form.getlist('ingredient_units[]')

        if ingredient_ids:
            for i in range(len(ingredient_ids)):
                ingredient_id = ingredient_ids[i]
                quantity = ingredient_quantities[i] if i < len(ingredient_quantities) else 1
                unit = ingredient_units[i] if i < len(ingredient_units) else '份'

                recipe_ingredient = RecipeIngredient(
                    recipe_id=recipe.id,
                    ingredient_id=ingredient_id,
                    quantity=quantity,
                    unit=unit
                )
                db.session.add(recipe_ingredient)

        # 添加审计日志
        log_activity(
            action='create',
            resource_type='Recipe',
            resource_id=recipe.id,
            details=recipe.to_dict()
        )

        db.session.commit()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': '食谱创建成功！',
                'id': recipe.id
            })
        else:
            flash('食谱创建成功！', 'success')
            return redirect(url_for('recipe.view', id=recipe.id))

    # GET 请求，显示创建表单
    return render_template('recipe/create.html', form=form, recipe=None)

@recipe_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑食谱"""
    from app.forms.recipe import RecipeForm

    recipe = Recipe.query.get_or_404(id)
    recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe.id).all()

    form = RecipeForm(obj=recipe)

    # 动态加载分类选项
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [
        (c.id, c.name) for c in RecipeCategory.query.all()
    ]

    if form.validate_on_submit():
        try:
            old_data = recipe.to_dict()

            # 处理图片上传
            if form.main_image.data:
                file = form.main_image.data
                filename = secure_filename(file.filename)
                # 确保文件名唯一
                unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
                upload_folder = os.path.join(current_app.static_folder, 'uploads/recipes')

                # 确保上传目录存在
                os.makedirs(upload_folder, exist_ok=True)

                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)

                # 删除旧图片
                if recipe.main_image:
                    old_file_path = os.path.join(current_app.static_folder, recipe.main_image)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)

                # 存储相对路径
                recipe.main_image = f"uploads/recipes/{unique_filename}"

            # 使用原生SQL更新食谱信息

            # 准备参数
            category_id = form.category_id.data if form.category_id.data and form.category_id.data != 0 else None
            priority = 10 if form.is_user_defined.data else 0
            is_user_defined = 1 if form.is_user_defined.data else 0

            # 获取分类名称
            category_name = None
            if category_id:
                category = RecipeCategory.query.get(category_id)
                if category:
                    category_name = category.name

            # 使用原生SQL更新记录，不包含updated_at字段
            sql = text("""
                UPDATE recipes
                SET name = :name,
                    category = :category,
                    category_id = :category_id,
                    meal_type = :meal_type,
                    description = :description,
                    status = :status,
                    is_user_defined = :is_user_defined,
                    priority = :priority,
                    main_image = :main_image
                WHERE id = :id
            """)

            params = {
                'name': form.name.data,
                'category': category_name,
                'category_id': category_id,
                'meal_type': form.meal_type.data,
                'description': form.description.data,
                'status': form.status.data,
                'is_user_defined': is_user_defined,
                'priority': priority,
                'main_image': recipe.main_image,  # 使用已更新的main_image
                'id': recipe.id
            }

            try:
                # 执行SQL
                db.session.execute(sql, params)

                # 重新加载recipe对象以获取最新数据
                db.session.refresh(recipe)
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"更新食谱失败: {str(e)}")
                flash(f'更新食谱失败: {str(e)}', 'danger')
                return render_template('recipe/create.html', form=form, recipe=recipe, recipe_ingredients=recipe_ingredients)

            # 处理食材关联
            # 先删除现有的食材关联
            RecipeIngredient.query.filter_by(recipe_id=recipe.id).delete()

            # 添加新的食材关联
            ingredient_ids = request.form.getlist('ingredient_ids[]')
            ingredient_quantities = request.form.getlist('ingredient_quantities[]')
            ingredient_units = request.form.getlist('ingredient_units[]')

            if ingredient_ids:
                for i in range(len(ingredient_ids)):
                    ingredient_id = ingredient_ids[i]
                    quantity = ingredient_quantities[i] if i < len(ingredient_quantities) else 1
                    unit = ingredient_units[i] if i < len(ingredient_units) else '份'

                    recipe_ingredient = RecipeIngredient(
                        recipe_id=recipe.id,
                        ingredient_id=ingredient_id,
                        quantity=quantity,
                        unit=unit
                    )
                    db.session.add(recipe_ingredient)

            # 添加审计日志
            log_activity(
                action='update',
                resource_type='Recipe',
                resource_id=recipe.id,
                details={
                    'old': old_data,
                    'new': recipe.to_dict()
                }
            )

            db.session.commit()

            # 根据请求类型返回不同的响应
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'message': '食谱更新成功！',
                    'id': recipe.id
                })
            else:
                flash('食谱更新成功！', 'success')
                return redirect(url_for('recipe.view', id=recipe.id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新食谱异常: {str(e)}")

            # 根据请求类型返回不同的响应
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'操作异常: {str(e)}'}), 500
            else:
                flash(f'操作异常: {str(e)}', 'danger')
                return redirect(url_for('recipe.edit', id=recipe.id))

    # GET 请求，显示编辑表单
    return render_template('recipe/create.html',
                          form=form,
                          recipe=recipe,
                          recipe_ingredients=recipe_ingredients)

@recipe_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除食谱"""
    try:
        recipe = Recipe.query.get_or_404(id)

        # 检查是否有关联的留样记录
        if recipe.food_samples.count() > 0:
            return jsonify({'success': False, 'message': '该食谱已关联留样记录，不能删除！'})

        # 添加审计日志
        log_activity(
            action='delete',
            resource_type='Recipe',
            resource_id=recipe.id,
            details=recipe.to_dict()
        )

        try:
            # 删除图片
            if recipe.main_image:
                file_path = os.path.join(current_app.static_folder, recipe.main_image)
                if os.path.exists(file_path):
                    os.remove(file_path)
        except Exception as e:
            current_app.logger.error(f"删除食谱图片失败: {str(e)}")
            # 继续执行，不因图片删除失败而中断整个操作

        try:
            # 删除食谱的工序及其食材关联
            for process in recipe.processes:
                RecipeProcessIngredient.query.filter_by(process_id=process.id).delete()
            RecipeProcess.query.filter_by(recipe_id=recipe.id).delete()

            # 删除食谱的食材关联
            RecipeIngredient.query.filter_by(recipe_id=recipe.id).delete()

            db.session.delete(recipe)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除食谱数据失败: {str(e)}")
            return jsonify({'success': False, 'message': f'删除食谱失败: {str(e)}'})

        return jsonify({'success': True, 'message': '食谱删除成功！'})

    except Exception as e:
        current_app.logger.error(f"删除食谱异常: {str(e)}")
        return jsonify({'success': False, 'message': f'操作异常: {str(e)}'})

@recipe_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看食谱详情"""
    recipe = Recipe.query.get_or_404(id)

    # 获取食谱的工序和食材
    recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe.id).all()
    recipe_processes = RecipeProcess.query.filter_by(recipe_id=recipe.id).order_by(RecipeProcess.process_order).all()

    # 为每个工序获取其食材
    process_ingredients = {}
    for process in recipe_processes:
        process_ingredients[process.id] = RecipeProcessIngredient.query.filter_by(process_id=process.id).all()

    # 添加审计日志
    log_activity(
        action='view',
        resource_type='Recipe',
        resource_id=recipe.id
    )

    return render_template('recipe/view.html',
                          recipe=recipe,
                          recipe_ingredients=recipe_ingredients,
                          recipe_processes=recipe_processes,
                          process_ingredients=process_ingredients)

@recipe_bp.route('/api')
@login_required
def api_list():
    """食谱API"""
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    query = Recipe.query

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Recipe.name.like(f'%{keyword}%'))

    recipes = query.all()
    return jsonify([recipe.to_dict() for recipe in recipes])
