/**
 * 陪餐记录图片上传器初始化
 */
$(document).ready(function() {
    const container = document.getElementById('photos');
    if (!container) return;

    // 从 data 属性获取配置
    const companionId = container.dataset.companionId;
    const apiBaseUrl = container.dataset.apiBaseUrl;

    // 初始化图片上传器
    const uploader = new EnhancedImageUploader('photos', {
        referenceType: 'companion',
        referenceId: companionId,
        apiBaseUrl: apiBaseUrl,
        showRating: false,
        maxFiles: 10,
        maxFileSize: 5,
        dropZoneText: '拖拽图片到此处上传，或点击选择图片',
        uploadButtonText: '选择图片',
        loadExisting: true
    });

    // 删除照片功能
    $('.delete-photo').click(function() {
        if (confirm('确定要删除这张照片吗？')) {
            var photoId = $(this).data('photo-id');
            // 这里可以添加AJAX请求删除照片
            // 暂时简单处理，刷新页面
            $(this).closest('.col-md-3').remove();
        }
    });
}); 