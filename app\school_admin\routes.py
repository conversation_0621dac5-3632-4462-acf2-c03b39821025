from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.school_admin import school_admin_bp
from app.school_admin.forms import SchoolUserForm
from app.models import User, Role, UserRole, AdministrativeArea
from app.utils.decorators import check_permission
from app.utils.school_required import school_required
from app.utils.log_activity import log_activity
from datetime import datetime
from app.services.school_user_service import SchoolUserService

@school_admin_bp.route('/users')
@login_required
@school_required  # 使用school_required装饰器确保用户有关联学校
@check_permission('user', 'view')
def users(user_area=None):
    """学校用户管理"""
    # 获取用户所属学校
    school_area = user_area or current_user.get_current_area()

    # 获取学校下的所有子区域ID
    sub_area_ids = [area.id for area in school_area.children]
    all_area_ids = [school_area.id] + sub_area_ids

    # 查询属于这些区域的用户
    page = request.args.get('page', 1, type=int)
    users = User.query.filter(User.area_id.in_(all_area_ids)).order_by(User.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )

    # 获取可用角色（排除系统管理员和超级管理员）
    roles = Role.query.filter(~Role.name.in_(['系统管理员', '超级管理员'])).all()

    return render_template('school_admin/users.html',
                          title='学校用户管理',
                          users=users,
                          roles=roles,
                          school=school_area,
                          now=datetime.now())

@school_admin_bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('user', 'create')
def add_user(user_area=None):
    """添加学校用户"""
    form = SchoolUserForm()

    if form.validate_on_submit():
        try:
            # 准备用户数据
            user_data = {
                'username': form.username.data,
                'email': form.email.data,
                'real_name': form.real_name.data,
                'phone': form.phone.data,
                'status': form.status.data,
                'password': form.password.data,
                'sub_area_id': form.sub_area_id.data,
                'roles': form.roles.data
            }

            # 使用服务创建用户
            user = SchoolUserService.create_school_user(user_data)

            # 记录审计日志
            log_activity(
                action='create',
                resource_type='user',
                resource_id=user.id,
                details={
                    'user': user.to_dict(),
                    'created_by': current_user.username,
                    'created_by_id': current_user.id
                }
            )

            flash(f'用户 {user.username} 添加成功', 'success')
            return redirect(url_for('school_admin.users'))

        except ValueError as e:
            flash(str(e), 'danger')

    return render_template('school_admin/user_form.html',
                          title='添加学校用户',
                          form=form,
                          now=datetime.now())

@school_admin_bp.route('/users/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@check_permission('user', 'view')  # 学校管理员只需要有查看用户的权限
def edit_user(id):
    """编辑学校用户"""
    # 检查用户是否有关联的学校
    if not current_user.area or current_user.area_level != 3:  # 3表示学校级别
        flash('您不是学校管理员，无法访问此功能', 'danger')
        return redirect(url_for('main.index'))

    # 获取要编辑的用户
    user = User.query.get_or_404(id)

    # 检查用户是否属于当前学校或其子区域
    school_id = current_user.area_id
    school_area = AdministrativeArea.query.get(school_id)
    sub_area_ids = [area.id for area in school_area.children]
    all_area_ids = [school_id] + sub_area_ids

    if user.area_id not in all_area_ids:
        flash('您没有权限编辑此用户', 'danger')
        return redirect(url_for('school_admin.users'))

    # 创建表单并预填充数据
    form = SchoolUserForm(obj=user)
    form.user_id = id

    # 设置子区域字段的默认值
    if request.method == 'GET':
        if user.area_id == school_id:
            form.sub_area_id.data = 0  # 无子区域，使用学校
        else:
            form.sub_area_id.data = user.area_id

        # 设置角色默认值
        form.roles.data = [role.id for role in user.roles]

    if form.validate_on_submit():
        # 保存旧数据用于记录历史
        old_data = user.to_dict()

        # 确定用户所属区域
        if form.sub_area_id.data and form.sub_area_id.data > 0:
            area_id = form.sub_area_id.data
            # 获取子区域对象以确定级别
            sub_area = AdministrativeArea.query.get(area_id)
            area_level = sub_area.level if sub_area else 4  # 默认为食堂级别
        else:
            # 使用当前学校
            area_id = current_user.area_id
            area_level = 3  # 学校级别

        # 更新用户信息
        user.username = form.username.data
        user.email = form.email.data
        user.real_name = form.real_name.data
        user.phone = form.phone.data
        user.status = form.status.data
        user.area_id = area_id
        user.area_level = area_level

        # 如果提供了新密码，则更新密码
        if form.password.data:
            user.set_password(form.password.data)

        # 使用事务确保角色更新的原子性
        try:
            # 开始事务
            db.session.begin_nested()

            # 先删除所有现有角色
            UserRole.query.filter_by(user_id=user.id).delete()

            # 添加新角色
            for role_id in form.roles.data:
                # 检查角色是否是系统管理员或超级管理员
                role = Role.query.get(role_id)
                if role and role.name in ['系统管理员', '超级管理员']:
                    continue  # 跳过这些角色

                user_role = UserRole(user_id=user.id, role_id=role_id)
                db.session.add(user_role)

                # 设置角色的默认模块可见性
                SchoolUserService.set_default_module_visibility(role)

            # 提交事务
            db.session.commit()
        except Exception as e:
            # 回滚事务
            db.session.rollback()
            flash(f'更新用户角色失败: {str(e)}', 'danger')
            current_app.logger.error(f'更新用户角色失败: {str(e)}')
            return render_template('school_admin/user_form.html',
                                  title=f'编辑用户 - {user.username}',
                                  form=form,
                                  user=user,
                                  now=datetime.now())

        # 记录审计日志
        log_activity(
            action='update',
            resource_type='user',
            resource_id=user.id,
            details={
                'old': old_data,
                'new': user.to_dict(),
                'updated_by': current_user.username,
                'updated_by_id': current_user.id
            }
        )

        flash(f'用户 {user.username} 更新成功', 'success')
        return redirect(url_for('school_admin.users'))

    return render_template('school_admin/user_form.html',
                          title=f'编辑用户 - {user.username}',
                          form=form,
                          user=user,
                          now=datetime.now())




