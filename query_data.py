from app import create_app, db
from app.models import AdministrativeArea, WeeklyMenu

app = create_app()

with app.app_context():
    print('区域列表:')
    areas = AdministrativeArea.query.all()
    for area in areas:
        print(f'ID: {area.id}, 名称: {area.name}')
    
    print('\n周菜单列表:')
    menus = WeeklyMenu.query.all()
    for menu in menus:
        print(f'ID: {menu.id}, 区域ID: {menu.area_id}, 开始日期: {menu.week_start}, 状态: {menu.status}')
