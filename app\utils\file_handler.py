"""
文件处理工具模块

提供文件上传、保存和处理相关的功能。
"""

import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app

def save_photo(file, prefix='photo'):
    """保存照片文件

    参数:
        file: 上传的文件对象
        prefix: 文件名前缀

    返回:
        保存后的文件路径
    """
    # 检查文件是否有效
    if not file or file.filename == '':
        raise ValueError('无效的文件')

    # 获取文件扩展名
    filename = secure_filename(file.filename)
    _, ext = os.path.splitext(filename)
    if ext.lower() not in ['.jpg', '.jpeg', '.png', '.gif']:
        raise ValueError('不支持的文件格式，仅支持jpg、jpeg、png和gif格式')

    # 生成唯一文件名
    unique_filename = f"{prefix}_{datetime.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}{ext}"

    # 确保上传目录存在
    upload_folder = os.path.join(current_app.static_folder, 'uploads')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)

    # 保存文件
    file_path = os.path.join(upload_folder, unique_filename)
    file.save(file_path)

    # 返回相对路径，用于数据库存储和前端显示
    return f"/static/uploads/{unique_filename}"

def delete_file(file_path):
    """删除文件

    参数:
        file_path: 文件路径

    返回:
        是否删除成功
    """
    try:
        # 获取文件的绝对路径
        if file_path.startswith('/static/'):
            file_path = file_path[7:]  # 移除 '/static/' 前缀
        
        abs_path = os.path.join(current_app.static_folder, file_path)
        
        # 检查文件是否存在
        if os.path.exists(abs_path):
            os.remove(abs_path)
            return True
        return False
    except Exception as e:
        current_app.logger.error(f"删除文件失败: {str(e)}")
        return False
