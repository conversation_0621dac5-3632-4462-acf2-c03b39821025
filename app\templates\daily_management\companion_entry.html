<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .container {
            max-width: 800px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .school-name {
            font-size: 24px;
            font-weight: bold;
            color: #4e73df;
        }
        .date-info {
            font-size: 18px;
            color: #666;
            margin-top: 10px;
        }
        .card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        .card-header {
            background-color: #4e73df;
            color: white;
            font-weight: bold;
        }
        .card-body {
            padding: 25px;
        }
        .btn-lg {
            padding: 15px 25px;
            font-size: 18px;
            border-radius: 10px;
        }
        .icon-large {
            font-size: 24px;
            margin-right: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .info-box {
            background-color: #e8f4ff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="school-name">{{ school.name }}</div>
            <div class="date-info">陪餐记录系统 - {{ today.strftime('%Y年%m月%d日') }}</div>
        </div>
        
        <div class="info-box">
            <h5><i class="fas fa-info-circle"></i> 欢迎使用陪餐记录系统</h5>
            <p>感谢您参与学校食堂陪餐活动。您的反馈对我们改进食堂服务质量非常重要。</p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <i class="fas fa-utensils"></i> 陪餐记录选项
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('daily_management.public_add_companion', school_id=school.id) }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus-circle icon-large"></i> 添加今日陪餐记录
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="#" class="btn btn-success btn-lg w-100" data-bs-toggle="modal" data-bs-target="#historyModal">
                            <i class="fas fa-history icon-large"></i> 查看历史陪餐记录
                        </a>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 请注意：陪餐记录提交后将无法修改，请确保信息准确无误。
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <i class="fas fa-star"></i> 陪餐评价说明
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5><i class="fas fa-utensils text-primary"></i> 口味评分</h5>
                        <p>评价食物的味道、口感和新鲜度</p>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-broom text-success"></i> 卫生评分</h5>
                        <p>评价食堂环境、餐具和食品卫生状况</p>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-concierge-bell text-warning"></i> 服务评分</h5>
                        <p>评价食堂工作人员的服务态度和效率</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>{{ school.name }} 食堂管理系统 &copy; {{ today.year }}</p>
        </div>
    </div>
    
    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="historyModalLabel">历史陪餐记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-center">历史陪餐记录查询功能正在开发中...</p>
                    <p class="text-center">请联系学校管理员查询历史记录</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
