/**
 * 递归调用调试脚本
 * 用于检测和防止jQuery事件绑定导致的无限递归
 */

(function() {
    'use strict';

    // 调用栈深度监控
    let callStackDepth = 0;
    const MAX_CALL_STACK_DEPTH = 100;

    // 事件绑定计数器
    const eventBindingCounts = new Map();

    /**
     * 监控函数调用栈
     */
    function monitorCallStack(functionName) {
        callStackDepth++;
        
        if (callStackDepth > MAX_CALL_STACK_DEPTH) {
            console.error(`检测到可能的无限递归: ${functionName}, 调用栈深度: ${callStackDepth}`);
            console.trace();
            callStackDepth = 0; // 重置计数器
            return false;
        }
        
        return true;
    }

    /**
     * 重置调用栈计数器
     */
    function resetCallStack() {
        callStackDepth = 0;
    }

    /**
     * 监控事件绑定
     */
    function monitorEventBinding(element, eventType, namespace) {
        const key = `${element.tagName || 'DOCUMENT'}_${element.id || 'NO_ID'}_${eventType}_${namespace || 'NO_NAMESPACE'}`;
        
        const count = eventBindingCounts.get(key) || 0;
        eventBindingCounts.set(key, count + 1);
        
        if (count > 5) {
            console.warn(`检测到重复事件绑定: ${key}, 绑定次数: ${count + 1}`);
            console.trace();
        }
    }

    /**
     * 安全的jQuery事件绑定
     */
    function safeJQueryBind(selector, eventType, namespace, handler) {
        try {
            const $element = $(selector);
            
            if ($element.length === 0) {
                console.warn(`元素未找到: ${selector}`);
                return;
            }
            
            // 监控事件绑定
            monitorEventBinding($element[0], eventType, namespace);
            
            // 移除旧的事件监听器
            if (namespace) {
                $element.off(`${eventType}.${namespace}`);
            } else {
                $element.off(eventType);
            }
            
            // 添加新的事件监听器
            const eventName = namespace ? `${eventType}.${namespace}` : eventType;
            $element.on(eventName, function(e) {
                if (!monitorCallStack(`${selector}_${eventName}`)) {
                    return false;
                }
                
                try {
                    const result = handler.call(this, e);
                    resetCallStack();
                    return result;
                } catch (error) {
                    console.error(`事件处理器错误: ${selector}_${eventName}`, error);
                    resetCallStack();
                    return false;
                }
            });
            
            console.log(`安全绑定事件: ${selector} -> ${eventName}`);
            
        } catch (error) {
            console.error(`事件绑定失败: ${selector}`, error);
        }
    }

    /**
     * 安全的事件委托绑定
     */
    function safeJQueryDelegate(container, selector, eventType, namespace, handler) {
        try {
            const $container = $(container);
            
            if ($container.length === 0) {
                console.warn(`容器元素未找到: ${container}`);
                return;
            }
            
            // 监控事件绑定
            monitorEventBinding($container[0], eventType, namespace);
            
            // 移除旧的事件监听器
            if (namespace) {
                $container.off(`${eventType}.${namespace}`, selector);
            } else {
                $container.off(eventType, selector);
            }
            
            // 添加新的事件监听器
            const eventName = namespace ? `${eventType}.${namespace}` : eventType;
            $container.on(eventName, selector, function(e) {
                if (!monitorCallStack(`${container}_${selector}_${eventName}`)) {
                    return false;
                }
                
                try {
                    const result = handler.call(this, e);
                    resetCallStack();
                    return result;
                } catch (error) {
                    console.error(`委托事件处理器错误: ${container}_${selector}_${eventName}`, error);
                    resetCallStack();
                    return false;
                }
            });
            
            console.log(`安全绑定委托事件: ${container} -> ${selector} -> ${eventName}`);
            
        } catch (error) {
            console.error(`委托事件绑定失败: ${container} -> ${selector}`, error);
        }
    }

    /**
     * 检查现有的事件绑定
     */
    function checkExistingBindings() {
        console.log('检查现有事件绑定...');
        
        // 检查常见的问题元素
        const problematicSelectors = [
            '#dropZone',
            '#fileInput',
            '#saveDocuments',
            '.upload-doc-btn',
            '.remove-file',
            '.custom-file',
            'input[type="file"]'
        ];
        
        problematicSelectors.forEach(selector => {
            const $elements = $(selector);
            $elements.each(function() {
                const events = $._data(this, 'events');
                if (events) {
                    Object.keys(events).forEach(eventType => {
                        const handlers = events[eventType];
                        if (handlers.length > 1) {
                            console.warn(`元素 ${selector} 的 ${eventType} 事件有 ${handlers.length} 个处理器`);
                        }
                    });
                }
            });
        });
    }

    /**
     * 清理重复的事件绑定
     */
    function cleanupDuplicateBindings() {
        console.log('清理重复的事件绑定...');
        
        // 清理文件上传相关的事件
        $('#dropZone').off('click');
        $('#fileInput').off('change');
        $('#saveDocuments').off('click');
        $(document).off('click', '.remove-file');
        $(document).off('click', '.upload-doc-btn');
        
        console.log('清理完成');
    }

    /**
     * 获取调试信息
     */
    function getDebugInfo() {
        return {
            callStackDepth: callStackDepth,
            eventBindingCounts: Object.fromEntries(eventBindingCounts),
            timestamp: new Date().toISOString()
        };
    }

    // 导出到全局
    window.RecursionDebugger = {
        monitorCallStack: monitorCallStack,
        resetCallStack: resetCallStack,
        safeJQueryBind: safeJQueryBind,
        safeJQueryDelegate: safeJQueryDelegate,
        checkExistingBindings: checkExistingBindings,
        cleanupDuplicateBindings: cleanupDuplicateBindings,
        getDebugInfo: getDebugInfo
    };

    // 页面加载完成后自动检查
    $(document).ready(function() {
        setTimeout(function() {
            checkExistingBindings();
        }, 1000);
    });

    console.log('递归调试器已加载');

})();
