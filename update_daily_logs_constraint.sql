-- 修改 daily_logs 表的唯一约束
-- 声明变量
DECLARE @constraint_name NVARCHAR(128);
DECLARE @sql NVARCHAR(500);

-- 首先删除现有的唯一约束
IF EXISTS (
    SELECT * FROM sys.indexes
    WHERE name = 'UQ__daily_lo__2CCE3C86E0630470' AND object_id = OBJECT_ID('daily_logs')
)
BEGIN
    ALTER TABLE daily_logs DROP CONSTRAINT UQ__daily_lo__2CCE3C86E0630470;
    PRINT '已删除 daily_logs 表上的旧唯一约束';
END
ELSE
BEGIN
    -- 使用正确的方法查找唯一约束
    SELECT @constraint_name = kc.name
    FROM sys.key_constraints kc
    JOIN sys.index_columns ic ON kc.parent_object_id = ic.object_id AND kc.unique_index_id = ic.index_id
    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
    WHERE kc.parent_object_id = OBJECT_ID('daily_logs')
    AND kc.type = 'UQ'
    AND c.name = 'log_date';

    IF @constraint_name IS NOT NULL
    BEGIN
        SET @sql = N'ALTER TABLE daily_logs DROP CONSTRAINT ' + QUOTENAME(@constraint_name);
        EXEC sp_executesql @sql;
        PRINT '已删除 daily_logs 表上的旧唯一约束: ' + @constraint_name;
    END
    ELSE
    BEGIN
        PRINT '未找到 daily_logs 表上的旧唯一约束';
    END
END

-- 添加新的唯一约束，基于日期和区域的组合
ALTER TABLE daily_logs ADD CONSTRAINT UQ_daily_logs_date_area UNIQUE (log_date, area_id);
PRINT '已添加新的唯一约束 UQ_daily_logs_date_area';
