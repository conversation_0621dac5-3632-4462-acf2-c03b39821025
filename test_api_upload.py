#!/usr/bin/env python3
"""
测试公开上传API
"""

from app import create_app, db
import io
from PIL import Image

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def test_upload_api():
    """测试上传API"""
    app = create_app()

    with app.test_client() as client:
        print("=== 测试公开上传API ===")

        # 测试URL路径
        url_path = '/daily-management/api/v2/inspections/public/upload-photos/22/37/morning'
        print(f"测试路径: {url_path}")

        # 创建测试图片
        test_image = create_test_image()

        # 准备文件数据
        data = {
            'photos': (test_image, 'test.jpg'),
            'description': '测试上传',
            'inspection_item': 'morning_inspection'
        }

        try:
            # 发送POST请求
            response = client.post(url_path, data=data, content_type='multipart/form-data')

            print(f"状态码: {response.status_code}")

            if response.status_code == 200:
                print("✓ API调用成功")
                try:
                    json_response = response.get_json()
                    print(f"响应内容: {json_response}")
                except:
                    print("响应不是JSON格式")
                    print(f"响应内容: {response.get_data(as_text=True)[:200]}")
            else:
                print(f"✗ API调用失败: {response.status_code}")
                print(f"响应内容: {response.get_data(as_text=True)[:500]}")

        except Exception as e:
            print(f"✗ 错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_upload_api()
