"""
检查记录二维码相关路由

提供检查记录二维码生成和处理功能。
"""

from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify
from flask_login import login_required, current_user
from app import db
from app.routes.daily_management import daily_management_bp
from app.models_daily_management import DailyLog, InspectionRecord
from app.models import AdministrativeArea
from app.utils.qrcode_helper import generate_qrcode_base64
from sqlalchemy import text
from datetime import datetime, date, timedelta
import os

# 生成固定检查记录二维码（新版本）
@daily_management_bp.route('/inspections/fixed-qrcode')
@login_required
def generate_fixed_inspection_qrcode():
    """生成固定的检查记录二维码（不依赖特定日期）"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法生成二维码', 'danger')
        return redirect(url_for('main.index'))

    try:
        # 记录调试信息
        current_app.logger.info(f"生成固定二维码 - 用户: {current_user.username}, 学校: {user_area.name} (ID: {user_area.id})")

        # 生成固定的上传照片URL（员工用 - 匿名访问）
        upload_url = url_for('daily_management.public_inspection_select_date',
                            school_id=user_area.id,
                            action='upload',
                            _external=True)
        current_app.logger.info(f"员工上传URL: {upload_url}")
        upload_qrcode_base64 = generate_qrcode_base64(upload_url)

        # 生成固定的评分页面URL（管理员用 - 匿名访问）
        rate_url = url_for('daily_management.public_inspection_select_date',
                           school_id=user_area.id,
                           action='rate',
                           _external=True)
        current_app.logger.info(f"管理员评分URL: {rate_url}")
        rate_qrcode_base64 = generate_qrcode_base64(rate_url)

        return render_template('daily_management/fixed_inspection_qrcode.html',
                              title=f'{user_area.name} - 检查记录二维码',
                              school=user_area,
                              upload_qrcode_base64=upload_qrcode_base64,
                              upload_url=upload_url,
                              rate_qrcode_base64=rate_qrcode_base64,
                              rate_url=rate_url)
    except Exception as e:
        current_app.logger.error(f"生成固定检查记录二维码失败: {str(e)}")
        flash(f'生成二维码失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 生成检查记录二维码（兼容旧版本）
@daily_management_bp.route('/inspections/qrcode/<int:log_id>/<inspection_type>')
@login_required
def generate_inspection_qrcode(log_id, inspection_type):
    """生成检查记录二维码（兼容旧版本，重定向到固定二维码）"""
    # 重定向到新的固定二维码页面
    return redirect(url_for('daily_management.generate_fixed_inspection_qrcode'))

# 公开生成检查记录二维码（匿名访问）
@daily_management_bp.route('/public/inspections/qrcode/<int:school_id>/<int:log_id>/<inspection_type>')
def public_generate_inspection_qrcode(school_id, log_id, inspection_type):
    """公开生成检查记录二维码（匿名访问）"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 获取日志
    log = DailyLog.query.filter_by(id=log_id, area_id=school_id).first_or_404()

    # 验证检查类型
    if inspection_type not in ['morning', 'noon', 'evening']:
        return render_template('daily_management/public_error.html',
                              title='参数错误',
                              error='检查类型无效')

    try:
        # 生成上传照片的URL（员工用）
        upload_url = url_for('daily_management.public_upload_inspection_photo',
                            school_id=school_id,
                            log_id=log_id,
                            inspection_type=inspection_type,
                            _external=True)
        upload_qrcode_base64 = generate_qrcode_base64(upload_url)

        # 生成评分页面的URL（管理员用）
        rate_url = url_for('daily_management.public_rate_inspection_photos',
                           school_id=school_id,
                           log_id=log_id,
                           inspection_type=inspection_type,
                           _external=True)
        rate_qrcode_base64 = generate_qrcode_base64(rate_url)

        # 获取检查类型名称
        inspection_type_name = {
            'morning': '晨检',
            'noon': '午检',
            'evening': '晚检'
        }.get(inspection_type, '检查')

        return render_template('daily_management/inspection_qrcode.html',
                              title=f'{school.name} - {inspection_type_name}二维码',
                              log=log,
                              school=school,
                              inspection_type=inspection_type,
                              inspection_type_name=inspection_type_name,
                              upload_qrcode_base64=upload_qrcode_base64,
                              upload_url=upload_url,
                              rate_qrcode_base64=rate_qrcode_base64,
                              rate_url=rate_url)
    except Exception as e:
        current_app.logger.error(f"生成公开检查记录二维码失败: {str(e)}")
        return render_template('daily_management/public_error.html',
                              title='生成失败',
                              error=f'生成二维码失败: {str(e)}')

# 公开的日期选择页面
@daily_management_bp.route('/public/inspections/select-date/<int:school_id>/<action>')
def public_inspection_select_date(school_id, action):
    """公开的检查记录日期选择页面"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 验证操作类型
    if action not in ['upload', 'rate']:
        return render_template('daily_management/public_error.html',
                              title='参数错误',
                              error='操作类型无效')

    # 获取操作名称
    action_name = {
        'upload': '上传照片',
        'rate': '照片评分'
    }.get(action)

    return render_template('daily_management/public_inspection_select_date.html',
                          title=f'{school.name} - 选择日期',
                          school=school,
                          action=action,
                          action_name=action_name)

# 公开的检查类型选择页面
@daily_management_bp.route('/public/inspections/select-type/<int:school_id>/<action>/<date_str>')
def public_inspection_select_type(school_id, action, date_str):
    """公开的检查类型选择页面"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 验证操作类型
    if action not in ['upload', 'rate']:
        return render_template('daily_management/public_error.html',
                              title='参数错误',
                              error='操作类型无效')

    # 验证日期格式
    try:
        selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return render_template('daily_management/public_error.html',
                              title='参数错误',
                              error='日期格式无效')

    # 获取或创建该日期的日志
    log = DailyLog.query.filter_by(log_date=selected_date, area_id=school_id).first()
    if not log:
        # 自动创建日志
        try:
            log = DailyLog(
                log_date=selected_date,
                area_id=school_id,
                weather='晴',
                temperature='25℃',
                status='normal'
            )
            db.session.add(log)
            db.session.commit()
            current_app.logger.info(f"自动创建日志 - 学校: {school.name}, 日期: {selected_date}")
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建日志失败: {str(e)}")
            return render_template('daily_management/public_error.html',
                                  title='系统错误',
                                  error='无法创建日志记录')

    # 获取操作名称
    action_name = {
        'upload': '上传照片',
        'rate': '照片评分'
    }.get(action)

    return render_template('daily_management/public_inspection_select_type.html',
                          title=f'{school.name} - 选择检查类型',
                          school=school,
                          log=log,
                          action=action,
                          action_name=action_name,
                          selected_date=selected_date)

# 公开访问的照片上传页面
@daily_management_bp.route('/public/inspections/upload/<int:school_id>/<int:log_id>/<inspection_type>')
def public_upload_inspection_photo(school_id, log_id, inspection_type):
    """公开访问的检查记录照片上传页面"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 获取日志
    log = DailyLog.query.filter_by(id=log_id, area_id=school_id).first_or_404()

    # 验证检查类型
    if inspection_type not in ['morning', 'noon', 'evening']:
        flash('检查类型无效', 'danger')
        return redirect(url_for('main.index'))

    # 获取检查项目列表
    inspection_items = ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒']

    # 获取检查类型名称
    inspection_type_name = {
        'morning': '晨检',
        'noon': '午检',
        'evening': '晚检'
    }.get(inspection_type, '检查')

    return render_template('daily_management/public_upload_inspection_photo.html',
                          title=f'{school.name} - 上传{inspection_type_name}照片',
                          school=school,
                          log=log,
                          inspection_type=inspection_type,
                          inspection_type_name=inspection_type_name,
                          inspection_items=inspection_items)

# 扫码上传入口页面（重定向到固定二维码）
@daily_management_bp.route('/scan-upload')
@login_required
def scan_upload_entry():
    """扫码上传入口页面 - 重定向到固定二维码页面"""
    # 重定向到新的固定二维码页面
    return redirect(url_for('daily_management.generate_fixed_inspection_qrcode'))

# 公开访问的照片评分页面
@daily_management_bp.route('/public/inspections/rate/<int:school_id>/<int:log_id>/<inspection_type>')
def public_rate_inspection_photos(school_id, log_id, inspection_type):
    """公开访问的检查记录照片评分页面"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 获取日志
    log = DailyLog.query.filter_by(id=log_id, area_id=school_id).first_or_404()

    # 验证检查类型
    if inspection_type not in ['morning', 'noon', 'evening']:
        flash('检查类型无效', 'danger')
        return redirect(url_for('main.index'))

    # 获取检查记录
    inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type=inspection_type
    ).all()

    # 调试信息
    current_app.logger.info(f"找到 {len(inspections)} 条检查记录，日志ID: {log_id}, 检查类型: {inspection_type}")
    for inspection in inspections:
        current_app.logger.info(f"检查记录: ID={inspection.id}, 项目={inspection.inspection_item}")

    # 获取照片
    photos_by_item = {}
    for inspection in inspections:
        # 使用原始SQL查询获取照片
        # 根据检查时间类型查询照片
        # 新版本使用 morning/noon/evening 作为 reference_type
        # 旧版本使用 inspection 作为 reference_type
        sql = text("""
        SELECT id, file_path, description, rating, upload_time, comment
        FROM photos
        WHERE (reference_type = :inspection_type OR reference_type = 'inspection')
        AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {
            'reference_id': inspection.id,
            'inspection_type': inspection_type
        })

        photos = []
        photo_count = 0
        for row in result:
            photo_count += 1
            # 修复照片路径
            original_path = row[1]
            file_path = original_path

            # 确保路径以/static开头
            if file_path and not file_path.startswith('/static'):
                if file_path.startswith('/'):
                    file_path = '/static' + file_path
                else:
                    file_path = '/static/' + file_path

            # 检查文件是否存在，如果不存在尝试修复路径
            import os
            actual_file_path = os.path.join(current_app.static_folder, file_path.lstrip('/static/'))

            if not os.path.exists(actual_file_path):
                # 尝试不同的路径修复策略
                filename = os.path.basename(original_path)

                # 策略1: 检查是否在morning文件夹中
                morning_path = f"/static/uploads/daily_management/morning/{filename}"
                morning_actual = os.path.join(current_app.static_folder, morning_path.lstrip('/static/'))

                # 策略2: 检查是否在inspection文件夹中
                inspection_path = f"/static/uploads/daily_management/inspection/{filename}"
                inspection_actual = os.path.join(current_app.static_folder, inspection_path.lstrip('/static/'))

                # 策略3: 检查是否在旧的inspection文件夹中
                old_inspection_path = f"/static/uploads/inspection/{filename}"
                old_inspection_actual = os.path.join(current_app.static_folder, old_inspection_path.lstrip('/static/'))

                if os.path.exists(morning_actual):
                    file_path = morning_path
                    actual_file_path = morning_actual
                    current_app.logger.info(f"修复照片路径: {original_path} -> {file_path}")
                elif os.path.exists(inspection_actual):
                    file_path = inspection_path
                    actual_file_path = inspection_actual
                    current_app.logger.info(f"修复照片路径: {original_path} -> {file_path}")
                elif os.path.exists(old_inspection_actual):
                    file_path = old_inspection_path
                    actual_file_path = old_inspection_actual
                    current_app.logger.info(f"修复照片路径: {original_path} -> {file_path}")
                else:
                    current_app.logger.warning(f"照片文件不存在: {original_path}")
                    continue

            # 只添加存在的照片
            if os.path.exists(actual_file_path):
                photos.append({
                    'id': row[0],
                    'file_path': file_path,
                    'description': row[2],
                    'rating': row[3],
                    'upload_time': row[4],
                    'comment': row[5] if len(row) > 5 else None
                })

        current_app.logger.info(f"检查记录 {inspection.id} ({inspection.inspection_item}) 查询到 {photo_count} 张照片，有效照片 {len(photos)} 张")

        if photos:
            photos_by_item[inspection.inspection_item] = photos
            current_app.logger.info(f"添加到photos_by_item: {inspection.inspection_item} -> {len(photos)} 张照片")
        else:
            current_app.logger.info(f"检查记录 {inspection.inspection_item} 没有有效照片，跳过")

    # 获取检查类型名称
    inspection_type_name = {
        'morning': '晨检',
        'noon': '午检',
        'evening': '晚检'
    }.get(inspection_type, '检查')

    # 最终调试信息
    current_app.logger.info(f"最终photos_by_item包含 {len(photos_by_item)} 个项目: {list(photos_by_item.keys())}")

    return render_template('daily_management/public_rate_inspection_photos.html',
                          title=f'{school.name} - 评价{inspection_type_name}照片',
                          school=school,
                          log=log,
                          inspection_type=inspection_type,
                          inspection_type_name=inspection_type_name,
                          photos_by_item=photos_by_item)
