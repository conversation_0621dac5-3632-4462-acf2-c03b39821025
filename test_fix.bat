@echo off
echo ========================================
echo 文件上传和CSP修复测试
echo ========================================
echo.

echo 1. 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 2. 检查Flask应用...
echo 请确保Flask应用正在运行 (python run.py)
echo 按任意键继续测试...
pause > nul

echo.
echo 3. 运行测试脚本...
python test_file_upload_fix.py

echo.
echo 4. 手动测试建议:
echo    - 打开浏览器访问 http://localhost:5000
echo    - 查看浏览器控制台是否有CSP错误
echo    - 测试文件上传功能是否正常
echo    - 检查文件选择对话框是否能正常弹出

echo.
echo 测试完成！
pause
