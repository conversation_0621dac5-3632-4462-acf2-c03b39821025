{% macro feature_card(icon, title, description, badges=None) %}
<div class="feature-highlight animate-fadeInUp">
    <div class="feature-icon">
        <i class="{{ icon }}"></i>
    </div>
    <h5>{{ title }}</h5>
    <p class="text-muted">{{ description }}</p>
    {% if badges %}
    <div class="mt-3">
        {% for badge in badges %}
        <span class="badge bg-{{ badge.color }}">{{ badge.text }}</span>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endmacro %}

{% macro stat_card(number, label) %}
<div class="stat-item animate-fadeInUp">
    <div class="stat-number">{{ number }}</div>
    <div class="stat-label">{{ label }}</div>
</div>
{% endmacro %}

{% macro feature_item(icon, title, description) %}
<div class="feature-item animate-fadeInUp">
    <i class="{{ icon }}"></i>
    <h6>{{ title }}</h6>
    <small>{{ description }}</small>
</div>
{% endmacro %}

{% macro cta_section(title, description, primary_btn, secondary_btn) %}
<div class="container text-center mb-5">
    <h3 class="mb-4">{{ title }}</h3>
    <p class="lead text-muted mb-4">{{ description }}</p>
    <div class="d-flex justify-content-center gap-3">
        {{ primary_btn|safe }}
        {{ secondary_btn|safe }}
    </div>
</div>
{% endmacro %} 