#!/usr/bin/env python3
"""
测试二维码访问功能
"""

from app import create_app, db
from app.models import User, AdministrativeArea
from app.models_daily_management import DailyLog
from flask import url_for
import sys

def test_qrcode_access():
    """测试二维码访问功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 二维码访问功能诊断 ===\n")
        
        # 1. 检查日志记录
        print("1. 检查日志记录 ID 37:")
        log = DailyLog.query.get(37)
        if log:
            print(f"   ✓ 日志存在")
            print(f"   - 日期: {log.log_date}")
            print(f"   - 学校ID: {log.area_id}")
            
            # 检查学校
            school = AdministrativeArea.query.get(log.area_id)
            if school:
                print(f"   - 学校名称: {school.name}")
            else:
                print(f"   ✗ 学校不存在 (ID: {log.area_id})")
                return
        else:
            print("   ✗ 日志不存在")
            return
        
        # 2. 检查用户权限
        print("\n2. 检查用户权限:")
        users = User.query.filter_by(area_id=log.area_id).all()
        if users:
            print(f"   ✓ 找到 {len(users)} 个用户有权限访问此学校")
            for user in users[:3]:  # 只显示前3个
                print(f"   - 用户: {user.username} (ID: {user.id})")
        else:
            print("   ✗ 没有用户有权限访问此学校")
            
        # 检查所有用户
        all_users = User.query.all()
        print(f"   总用户数: {len(all_users)}")
        
        # 3. 检查路由注册
        print("\n3. 检查路由注册:")
        qrcode_routes = []
        for rule in app.url_map.iter_rules():
            if 'qrcode' in rule.rule and 'inspection' in rule.rule:
                qrcode_routes.append(rule)
                print(f"   ✓ 路由: {rule.rule} -> {rule.endpoint}")
        
        if not qrcode_routes:
            print("   ✗ 未找到相关路由")
            return
        
        # 4. 测试URL生成
        print("\n4. 测试URL生成:")
        try:
            # 设置SERVER_NAME以便生成外部URL
            app.config['SERVER_NAME'] = '192.168.1.7:5000'
            
            with app.test_request_context():
                upload_url = url_for('daily_management.public_upload_inspection_photo',
                                   school_id=log.area_id,
                                   log_id=log.id,
                                   inspection_type='morning',
                                   _external=True)
                print(f"   ✓ 上传URL: {upload_url}")
                
                rate_url = url_for('daily_management.public_rate_inspection_photos',
                                 school_id=log.area_id,
                                 log_id=log.id,
                                 inspection_type='morning',
                                 _external=True)
                print(f"   ✓ 评分URL: {rate_url}")
                
        except Exception as e:
            print(f"   ✗ URL生成失败: {e}")
        
        # 5. 检查二维码生成功能
        print("\n5. 检查二维码生成功能:")
        try:
            from app.utils.qrcode_helper import generate_qrcode_base64
            test_data = "http://test.example.com"
            qr_base64 = generate_qrcode_base64(test_data)
            if qr_base64:
                print(f"   ✓ 二维码生成成功，长度: {len(qr_base64)}")
            else:
                print("   ✗ 二维码生成失败")
        except Exception as e:
            print(f"   ✗ 二维码生成异常: {e}")
        
        # 6. 检查模板文件
        print("\n6. 检查模板文件:")
        import os
        template_path = os.path.join(app.root_path, 'templates', 'daily_management', 'inspection_qrcode.html')
        if os.path.exists(template_path):
            print(f"   ✓ 模板文件存在: {template_path}")
        else:
            print(f"   ✗ 模板文件不存在: {template_path}")
        
        # 7. 建议解决方案
        print("\n=== 建议解决方案 ===")
        print("1. 确保用户已登录")
        print("2. 确保用户有访问对应学校的权限")
        print("3. 检查用户的 area_id 是否与日志的 area_id 匹配")
        print("4. 如果是权限问题，需要管理员分配正确的学校权限")
        
        # 8. 显示访问URL
        print(f"\n=== 访问信息 ===")
        print(f"目标URL: http://192.168.1.7:5000/daily-management/inspections/qrcode/37/morning")
        print(f"需要登录: 是")
        print(f"需要权限: 用户必须属于学校ID {log.area_id} ({school.name})")

if __name__ == '__main__':
    test_qrcode_access()
