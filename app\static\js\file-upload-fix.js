/**
 * 文件上传功能修复脚本
 * 解决文件选择对话框不弹出的问题
 */

(function() {
    'use strict';

    /**
     * 修复文件上传功能
     */
    function fixFileUpload() {
        // 查找所有文件输入元素
        const fileInputs = document.querySelectorAll('input[type="file"]');
        
        fileInputs.forEach(function(fileInput) {
            // 跳过已经处理过的元素
            if (fileInput.dataset.uploadFixed) {
                return;
            }
            
            // 标记为已处理
            fileInput.dataset.uploadFixed = 'true';
            
            // 获取相关元素
            const customFileContainer = fileInput.closest('.custom-file');
            const customFileLabel = customFileContainer ? customFileContainer.querySelector('.custom-file-label') : null;
            
            if (customFileContainer && customFileLabel) {
                // 修复Bootstrap自定义文件上传
                fixBootstrapFileUpload(fileInput, customFileContainer, customFileLabel);
            } else {
                // 修复普通文件上传
                fixStandardFileUpload(fileInput);
            }
        });
    }

    /**
     * 修复Bootstrap自定义文件上传
     */
    function fixBootstrapFileUpload(fileInput, container, label) {
        // 设置样式确保可点击
        fileInput.style.position = 'absolute';
        fileInput.style.opacity = '0';
        fileInput.style.width = '100%';
        fileInput.style.height = '100%';
        fileInput.style.cursor = 'pointer';
        fileInput.style.zIndex = '1';
        
        // 设置容器样式
        container.style.position = 'relative';
        container.style.cursor = 'pointer';
        
        // 设置标签样式
        label.style.cursor = 'pointer';
        
        // 移除之前的事件监听器
        const newContainer = container.cloneNode(true);
        container.parentNode.replaceChild(newContainer, container);
        
        // 重新获取元素引用
        const newFileInput = newContainer.querySelector('input[type="file"]');
        const newLabel = newContainer.querySelector('.custom-file-label');
        
        // 点击容器时触发文件选择
        newContainer.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            newFileInput.click();
        });
        
        // 文件选择变化时更新标签
        newFileInput.addEventListener('change', function() {
            let fileName = '';
            if (this.files && this.files.length > 0) {
                fileName = this.files[0].name;
            } else if (this.value) {
                fileName = this.value.split('\\').pop();
            }
            
            if (fileName) {
                newLabel.textContent = fileName;
                newLabel.classList.add('file-selected');
            } else {
                newLabel.textContent = '选择文件';
                newLabel.classList.remove('file-selected');
            }
        });
    }

    /**
     * 修复标准文件上传
     */
    function fixStandardFileUpload(fileInput) {
        // 确保文件输入可见且可点击
        fileInput.style.cursor = 'pointer';
        
        // 如果有父级按钮或标签，确保点击时触发文件选择
        const parentButton = fileInput.closest('button, .btn, label');
        if (parentButton && parentButton.tagName !== 'LABEL') {
            parentButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                fileInput.click();
            });
        }
    }

    /**
     * 添加通用样式
     */
    function addStyles() {
        // 检查是否已添加样式
        if (document.getElementById('file-upload-fix-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'file-upload-fix-styles';
        style.textContent = `
            .custom-file {
                position: relative !important;
                cursor: pointer !important;
            }
            
            .custom-file-input {
                position: absolute !important;
                opacity: 0 !important;
                width: 100% !important;
                height: 100% !important;
                cursor: pointer !important;
                z-index: 1 !important;
            }
            
            .custom-file-label {
                cursor: pointer !important;
                border: 1px solid #ced4da;
                border-radius: 0.25rem;
                padding: 0.375rem 0.75rem;
                background-color: #fff;
                transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            }
            
            .custom-file-label:hover {
                border-color: #80bdff;
            }
            
            .custom-file-label.file-selected {
                color: #495057;
                background-color: #e9ecef;
            }
            
            /* 确保文件输入在所有情况下都可点击 */
            input[type="file"] {
                cursor: pointer !important;
            }
            
            /* 修复可能的z-index问题 */
            .custom-file-input:focus {
                box-shadow: none;
                border-color: transparent;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 初始化修复
     */
    function init() {
        // 添加样式
        addStyles();
        
        // 修复现有的文件上传
        fixFileUpload();
        
        // 监听DOM变化，修复动态添加的文件上传
        const observer = new MutationObserver(function(mutations) {
            let shouldFix = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.matches('input[type="file"]') || 
                                node.querySelector('input[type="file"]')) {
                                shouldFix = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldFix) {
                setTimeout(fixFileUpload, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 导出到全局，以便其他脚本调用
    window.FileUploadFix = {
        fix: fixFileUpload,
        init: init
    };

})();
