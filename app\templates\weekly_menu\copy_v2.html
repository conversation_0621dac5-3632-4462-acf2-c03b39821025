{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">复制周菜单</h1>
        <a href="{{ url_for('weekly_menu_v2.index') }}" class="d-none d-sm-inline-block btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> 返回列表
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">选择要复制的菜单和目标周次</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('weekly_menu_v2.copy') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="area_id" value="{{ area_id }}">
                        
                        <div class="form-group">
                            <label for="source_menu_id"><strong>选择源菜单</strong></label>
                            <select name="source_menu_id" id="source_menu_id" class="form-control" required>
                                <option value="">-- 请选择源菜单 --</option>
                                {% for menu in historical_menus %}
                                    <option value="{{ menu.id }}">
                                        {{ menu.area.name }} - 
                                        {{ menu.week_start|format_datetime('%Y-%m-%d') }} 至 
                                        {{ menu.week_end|format_datetime('%Y-%m-%d') }}
                                        ({{ menu.status }})
                                    </option>
                                {% endfor %}
                            </select>
                            <small class="form-text text-muted">选择要作为模板的历史菜单</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="target_week_start"><strong>选择目标周次</strong></label>
                            <select name="target_week_start" id="target_week_start" class="form-control" required>
                                <option value="">-- 请选择目标周次 --</option>
                                {% for week in target_weeks %}
                                    <option value="{{ week.start_date }}">
                                        {{ week.display_text }}
                                    </option>
                                {% endfor %}
                            </select>
                            <small class="form-text text-muted">选择要复制到的目标周次</small>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            注意：如果目标周次已存在菜单，将会被覆盖！
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-copy"></i> 复制菜单
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
