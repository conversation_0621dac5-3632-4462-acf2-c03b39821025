"""
仪表盘API路由

提供仪表盘API接口，用于前端调用。
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app.services.dashboard_service import DashboardService
from app.models_daily_management import DiningCompanion, Photo, DailyLog
from app import db
from sqlalchemy import desc
from datetime import datetime

# 创建蓝图
dashboard_api_bp = Blueprint('dashboard_api', __name__)

@dashboard_api_bp.route('/api/v2/dashboard/summary', methods=['GET'])
@login_required
def api_v2_dashboard_summary():
    """获取仪表盘摘要"""
    date_str = request.args.get('date')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_dashboard_summary(date_str, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/weekly', methods=['GET'])
@login_required
def api_v2_weekly_summary():
    """获取周摘要"""
    week_start = request.args.get('week_start')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_weekly_summary(week_start, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/monthly', methods=['GET'])
@login_required
def api_v2_monthly_summary():
    """获取月摘要"""
    year = request.args.get('year', type=int)
    month = request.args.get('month', type=int)
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_monthly_summary(year, month, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dining-companions/recent', methods=['GET'])
@login_required
def api_v2_recent_dining_companions():
    """获取最近的陪餐记录"""
    limit = request.args.get('limit', 5, type=int)
    area_id = request.args.get('area_id', type=int)

    # 构建查询
    query = DiningCompanion.query

    # 如果指定了区域，则通过日志筛选区域
    if area_id:
        # 获取指定区域的所有日志ID
        log_ids = db.session.query(DailyLog.id).filter(DailyLog.area_id == area_id).all()
        log_ids = [log_id[0] for log_id in log_ids]

        # 筛选这些日志下的陪餐记录
        if log_ids:
            query = query.filter(DiningCompanion.daily_log_id.in_(log_ids))
        else:
            # 如果没有找到日志，返回空列表
            return jsonify([])

    # 按时间倒序排序并限制数量
    companions = query.order_by(desc(DiningCompanion.dining_time)).limit(limit).all()

    # 构建响应数据
    result = []
    for companion in companions:
        # 获取关联的日志
        log = DailyLog.query.get(companion.daily_log_id) if companion.daily_log_id else None

        # 检查是否有照片
        has_photo = Photo.query.filter_by(
            reference_id=companion.id,
            reference_type='companion'
        ).count() > 0

        # 构建记录数据
        record = {
            'id': companion.id,
            'name': companion.companion_name,
            'role': companion.companion_role,
            'time': companion.dining_time.strftime('%H:%M') if companion.dining_time else '',
            'date': log.log_date.strftime('%Y-%m-%d') if log and log.log_date else '',
            'meal_type': companion.meal_type,
            'comments': companion.comments,
            'has_photo': has_photo,
            'area_name': log.area.name if log and hasattr(log, 'area') and log.area else '未知区域'
        }
        result.append(record)

    return jsonify(result)
