#!/usr/bin/env python3
"""
测试入库单文档存储和访问的脚本
"""

import os
import requests
import sys
from bs4 import BeautifulSoup

def test_document_storage_and_access():
    """测试文档存储路径和访问链接"""
    print("🔍 测试入库单文档存储和访问...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76  # 测试用的入库单ID
    
    try:
        # 1. 测试入库单详情页面
        details_url = f"{base_url}/stock-in/{stock_in_id}/details"
        print(f"📤 1. 访问入库单详情页面: {details_url}")
        
        response = requests.get(details_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 入库单详情页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找文档表格
            doc_table = soup.find('table')
            if doc_table:
                print("✅ 找到文档表格")
                
                # 查找文档链接
                doc_links = doc_table.find_all('a', href=True)
                
                if doc_links:
                    print(f"📋 找到 {len(doc_links)} 个文档链接")
                    
                    for i, link in enumerate(doc_links):
                        href = link.get('href', '')
                        text = link.get_text().strip()
                        
                        print(f"\n  文档链接 {i+1}:")
                        print(f"    链接文本: {text}")
                        print(f"    链接地址: {href}")
                        
                        # 检查链接格式
                        if href.startswith('/static/'):
                            print(f"    ✅ 链接格式正确 (以 /static/ 开头)")
                            
                            # 测试链接是否可访问
                            if text in ['查看', '下载']:
                                full_url = base_url + href
                                print(f"    🔗 测试访问: {full_url}")
                                
                                try:
                                    doc_response = requests.head(full_url, timeout=10)
                                    if doc_response.status_code == 200:
                                        print(f"    ✅ 文档可正常访问")
                                        
                                        # 获取文件信息
                                        content_type = doc_response.headers.get('Content-Type', '')
                                        content_length = doc_response.headers.get('Content-Length', '')
                                        
                                        print(f"    📄 文件类型: {content_type}")
                                        if content_length:
                                            print(f"    📏 文件大小: {content_length} bytes")
                                    else:
                                        print(f"    ❌ 文档访问失败，状态码: {doc_response.status_code}")
                                        
                                        # 检查文件是否存在于服务器
                                        file_path = href.replace('/static/', '')
                                        server_path = os.path.join('app', 'static', file_path)
                                        
                                        if os.path.exists(server_path):
                                            print(f"    📁 服务器文件存在: {server_path}")
                                        else:
                                            print(f"    ❌ 服务器文件不存在: {server_path}")
                                            
                                            # 尝试查找可能的文件位置
                                            print(f"    🔍 查找可能的文件位置...")
                                            
                                            # 检查不同的可能路径
                                            possible_paths = [
                                                os.path.join('static', file_path),
                                                os.path.join('app', 'static', file_path),
                                                os.path.join('uploads', file_path.replace('uploads/', '')),
                                                file_path
                                            ]
                                            
                                            for possible_path in possible_paths:
                                                if os.path.exists(possible_path):
                                                    print(f"    ✅ 找到文件: {possible_path}")
                                                    break
                                            else:
                                                print(f"    ❌ 未找到文件")
                                        
                                except requests.RequestException as e:
                                    print(f"    ❌ 请求异常: {e}")
                        else:
                            print(f"    ⚠️ 链接格式可能有问题 (不以 /static/ 开头)")
                else:
                    print("ℹ️ 没有找到文档链接")
            else:
                print("❌ 未找到文档表格")
                return False
        else:
            print(f"❌ 入库单详情页面访问失败，状态码: {response.status_code}")
            return False
        
        return True
        
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_upload_directory_structure():
    """检查上传目录结构"""
    print("\n🗂️ 检查上传目录结构...")
    
    # 检查可能的上传目录
    possible_upload_dirs = [
        'app/static/uploads/stock_in_docs',
        'static/uploads/stock_in_docs',
        'uploads/stock_in_docs'
    ]
    
    for upload_dir in possible_upload_dirs:
        print(f"\n📁 检查目录: {upload_dir}")
        
        if os.path.exists(upload_dir):
            print(f"  ✅ 目录存在")
            
            # 列出子目录
            try:
                subdirs = [d for d in os.listdir(upload_dir) if os.path.isdir(os.path.join(upload_dir, d))]
                
                if subdirs:
                    print(f"  📂 子目录: {subdirs}")
                    
                    # 检查每个子目录中的文件
                    for subdir in subdirs[:3]:  # 只检查前3个
                        subdir_path = os.path.join(upload_dir, subdir)
                        files = [f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))]
                        
                        if files:
                            print(f"    📄 {subdir} 中的文件: {files[:3]}{'...' if len(files) > 3 else ''}")
                        else:
                            print(f"    📄 {subdir} 中无文件")
                else:
                    print(f"  📂 无子目录")
                    
            except PermissionError:
                print(f"  ❌ 无权限访问目录")
            except Exception as e:
                print(f"  ❌ 访问目录时出错: {e}")
        else:
            print(f"  ❌ 目录不存在")

def check_flask_static_config():
    """检查Flask静态文件配置"""
    print("\n⚙️ 检查Flask静态文件配置...")
    
    # 测试静态文件服务
    base_url = "http://127.0.0.1:5000"
    
    # 测试已知的静态文件
    test_static_files = [
        '/static/css/bootstrap.min.css',
        '/static/js/jquery.min.js',
        '/static/vendor/fontawesome/css/all.min.css'
    ]
    
    for static_file in test_static_files:
        print(f"🔗 测试静态文件: {static_file}")
        
        try:
            response = requests.head(base_url + static_file, timeout=10)
            if response.status_code == 200:
                print(f"  ✅ 静态文件可访问")
            else:
                print(f"  ❌ 静态文件访问失败，状态码: {response.status_code}")
        except requests.RequestException as e:
            print(f"  ❌ 请求异常: {e}")

def provide_fix_suggestions():
    """提供修复建议"""
    print("\n💡 修复建议:")
    print("="*60)
    
    print("\n1. 🔍 检查文件存储路径:")
    print("   - 确认文件实际保存在 app/static/uploads/stock_in_docs/ 目录下")
    print("   - 检查目录权限是否正确")
    print("   - 确认文件名没有特殊字符")
    
    print("\n2. 🔧 检查数据库存储路径:")
    print("   - 数据库中的 file_path 应该是相对于 static 目录的路径")
    print("   - 格式应该是: uploads/stock_in_docs/stock_in_XX/filename.ext")
    print("   - 不应该包含 /static/ 前缀")
    
    print("\n3. 🌐 检查模板链接生成:")
    print("   - 使用 {{ url_for('static', filename=doc.file_path) }} 是正确的")
    print("   - 这会生成 /static/uploads/stock_in_docs/... 的URL")
    
    print("\n4. 🐛 调试步骤:")
    print("   - 在上传文档后，检查文件是否真的保存到了正确位置")
    print("   - 在数据库中查询 file_path 字段的值")
    print("   - 手动构造URL测试文件是否可访问")
    
    print("\n5. 🔄 可能的解决方案:")
    print("   - 重新上传一个文档，观察保存路径")
    print("   - 检查Flask应用的static_folder配置")
    print("   - 确认Web服务器的静态文件服务配置")

def main():
    """主函数"""
    print("🚀 开始测试入库单文档存储和访问\n")
    
    # 检查服务器连接
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code in [200, 302]:
            print("✅ 服务器连接正常\n")
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}\n")
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py\n")
        return False
    
    # 运行测试
    success = test_document_storage_and_access()
    
    # 检查目录结构
    check_upload_directory_structure()
    
    # 检查静态文件配置
    check_flask_static_config()
    
    # 输出测试结果
    print(f"\n{'='*60}")
    print("📊 测试结果")
    print('='*60)
    
    if success:
        print("🎉 文档存储和访问测试基本通过！")
        print("\n✅ 确认:")
        print("  - 入库单详情页面正常访问")
        print("  - 文档链接格式正确")
        print("  - 静态文件服务正常")
    else:
        print("❌ 文档存储和访问测试失败！")
        print("\n⚠️ 可能的问题:")
        print("  - 文件路径配置错误")
        print("  - 文件实际不存在")
        print("  - 静态文件服务配置问题")
        print("  - 权限问题")
    
    # 提供修复建议
    provide_fix_suggestions()
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
