-- 修改 daily_logs 表的 log_date 字段类型
-- 使用 ALTER TABLE 直接修改列类型，而不是重建表

-- 首先删除现有的唯一约束
IF EXISTS (
    SELECT * FROM sys.indexes
    WHERE name = 'UQ_daily_logs_date_area' AND object_id = OBJECT_ID('daily_logs')
)
BEGIN
    ALTER TABLE daily_logs DROP CONSTRAINT UQ_daily_logs_date_area;
    PRINT '已删除 daily_logs 表上的唯一约束 UQ_daily_logs_date_area';
END

-- 直接修改列类型
ALTER TABLE daily_logs ALTER COLUMN log_date DATETIME2(1) NOT NULL;
PRINT '已修改 daily_logs 表的 log_date 字段类型为 DATETIME2(1)';

-- 重新添加唯一约束
ALTER TABLE daily_logs ADD CONSTRAINT UQ_daily_logs_date_area UNIQUE (log_date, area_id);
PRINT '已重新添加唯一约束 UQ_daily_logs_date_area';

-- 更新现有数据，确保日期格式正确
UPDATE daily_logs
SET log_date = CONVERT(DATETIME2(1), log_date, 23);
PRINT '已更新现有数据的日期格式';

-- 创建或更新索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_daily_logs_log_date' AND object_id = OBJECT_ID('daily_logs'))
BEGIN
    CREATE INDEX idx_daily_logs_log_date ON daily_logs(log_date);
    PRINT '已创建索引 idx_daily_logs_log_date';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_daily_logs_area_id' AND object_id = OBJECT_ID('daily_logs'))
BEGIN
    CREATE INDEX idx_daily_logs_area_id ON daily_logs(area_id);
    PRINT '已创建索引 idx_daily_logs_area_id';
END

PRINT '已成功完成 daily_logs 表的修改';
