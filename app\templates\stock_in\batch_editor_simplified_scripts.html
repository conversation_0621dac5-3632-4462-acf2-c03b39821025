<script>
  $(document).ready(function() {
    // 全选/取消全选
    $("#selectAll").change(function() {
      $(".batch-checkbox").prop('checked', $(this).prop('checked'));
    });

    // 批量设置供应商
    $("#applyBulkSupplier").click(function() {
      var supplierId = $("#bulkSupplier").val();
      if (!supplierId) {
        alert("请先选择供应商");
        return;
      }

      $(".batch-checkbox:checked").each(function() {
        var itemId = $(this).val();
        $("select[name='supplier_id_" + itemId + "']").val(supplierId);
      });
    });

    // 批量设置存储位置
    $("#applyBulkStorageLocation").click(function() {
      var locationId = $("#bulkStorageLocation").val();
      if (!locationId) {
        alert("请先选择存储位置");
        return;
      }

      $(".batch-checkbox:checked").each(function() {
        var itemId = $(this).val();
        $("select[name='storage_location_id_" + itemId + "']").val(locationId);
      });
    });

    // 批量设置日期
    $("#applyBulkDates").click(function() {
      var productionDate = $("#bulkProductionDate").val();
      if (!productionDate) {
        alert("请先选择生产日期");
        return;
      }

      // 计算过期日期（默认30天后）
      var prodDate = new Date(productionDate);
      var expiryDate = new Date(prodDate);
      expiryDate.setDate(expiryDate.getDate() + 30);
      var expiryDateStr = expiryDate.toISOString().split('T')[0];

      $(".batch-checkbox:checked").each(function() {
        var itemId = $(this).val();
        $("input[name='production_date_" + itemId + "']").val(productionDate);
        $("input[name='expiry_date_" + itemId + "']").val(expiryDateStr);
      });
    });

    // 下拉菜单操作
    $("#selectAllBatches").click(function(e) {
      e.preventDefault();
      $(".batch-checkbox").prop('checked', true);
      $("#selectAll").prop('checked', true);
    });

    $("#deselectAllBatches").click(function(e) {
      e.preventDefault();
      $(".batch-checkbox").prop('checked', false);
      $("#selectAll").prop('checked', false);
    });

    // 按食材分组
    $("#groupByIngredient").click(function(e) {
      e.preventDefault();
      groupTable("ingredient-id");
    });

    // 按供应商分组
    $("#groupBySupplier").click(function(e) {
      e.preventDefault();
      groupTable("supplier-id");
    });

    // 分组函数
    function groupTable(groupBy) {
      var rows = $(".batch-row").detach();
      var groups = {};

      rows.each(function() {
        var key = $(this).data(groupBy);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(this);
      });

      var tbody = $("#batchTable tbody");
      tbody.empty();

      $.each(groups, function(key, groupRows) {
        var groupHeader = $("<tr>").addClass("table-secondary");
        var headerText = "";

        if (groupBy === "ingredient-id") {
          headerText = "食材: " + $(groupRows[0]).find("td:eq(1) div:first").text().trim() + " (" + groupRows.length + "个批次)";
        } else if (groupBy === "supplier-id") {
          var supplierName = $(groupRows[0]).find("select[name^='supplier_id_'] option:selected").text();
          headerText = "供应商: " + (supplierName || "未设置") + " (" + groupRows.length + "个批次)";
        }

        groupHeader.append($("<td>").attr("colspan", 6).text(headerText));
        tbody.append(groupHeader);

        $.each(groupRows, function(i, row) {
          tbody.append(row);
        });
      });
    }

    // 自动计算总价
    function calculateTotalPrice(row) {
      var quantity = parseFloat($(row).find('input[name^="quantity_"]').val()) || 0;
      var unitPrice = parseFloat($(row).find('input[name^="unit_price_"]').val()) || 0;
      var totalPrice = quantity * unitPrice;

      // 更新总价显示
      var totalPriceElement = $(row).find('.total-price');
      if (totalPrice > 0) {
        totalPriceElement.html('<strong>总价: ' + totalPrice.toFixed(2) + ' 元</strong>');
      } else {
        totalPriceElement.html('');
      }
    }

    // 为所有数量和单价输入框添加事件监听
    $('input[name^="quantity_"], input[name^="unit_price_"]').on('input', function() {
      var row = $(this).closest('tr');
      calculateTotalPrice(row);
    });

    // 初始计算所有行的总价
    $('.batch-row').each(function() {
      calculateTotalPrice(this);
    });

    // 文件上传预览
    $(".document-input").change(function(e) {
      var files = e.target.files;
      var itemId = $(this).attr('id').replace('documents_', '');
      var documentList = $("#document_list_" + itemId);

      // 添加新文件预览
      for (var i = 0; i < files.length; i++) {
        var file = files[i];
        var isImage = file.type.match('image.*');
        var isPdf = file.type === 'application/pdf';

        if (isImage || isPdf) {
          var documentItem = $('<div class="document-item"></div>');
          var icon = isImage ? '<i class="fas fa-file-image"></i>' : '<i class="fas fa-file-pdf"></i>';

          documentItem.append(icon);
          documentItem.append('<div class="document-name">' + file.name + '</div>');
          documentItem.append('<div class="document-actions"><button type="button" class="btn btn-sm btn-danger remove-file"><i class="fas fa-times"></i></button></div>');

          documentList.append(documentItem);
        }
      }
    });

    // 删除已上传文件
    $(document).on('click', '.delete-document', function() {
      var documentId = $(this).data('document-id');
      var documentItem = $(this).closest('.document-item');

      if (confirm('确定要删除这个文件吗？')) {
        $.ajax({
          url: "{{ url_for('stock_in.delete_batch_document') }}",
          type: "POST",
          data: {
            document_id: documentId,
            csrf_token: "{{ csrf_token() }}"
          },
          success: function(response) {
            if (response.success) {
              documentItem.remove();
            } else {
              alert('删除文件失败: ' + response.message);
            }
          },
          error: function() {
            alert('删除文件请求失败');
          }
        });
      }
    });

    // 移除未上传的文件
    $(document).on('click', '.remove-file', function() {
      $(this).closest('.document-item').remove();
    });

    // 批量上传文件
    $("#applyBulkDocument").click(function() {
      var fileInput = $("#bulkDocumentUpload")[0];
      if (fileInput.files.length === 0) {
        alert("请先选择文件");
        return;
      }

      // 获取选中的批次
      var selectedItems = $(".batch-checkbox:checked");
      if (selectedItems.length === 0) {
        alert("请至少选择一个批次");
        return;
      }

      // 创建一个FormData对象
      var formData = new FormData();
      for (var i = 0; i < fileInput.files.length; i++) {
        formData.append('documents[]', fileInput.files[i]);
      }

      // 添加选中的批次ID
      selectedItems.each(function() {
        formData.append('item_ids[]', $(this).val());
      });

      formData.append('csrf_token', "{{ csrf_token() }}");

      // 发送AJAX请求
      $.ajax({
        url: "{{ url_for('stock_in.bulk_upload_documents', stock_in_id=stock_in.id) }}",
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
          if (response.success) {
            alert('文件上传成功');
            // 刷新页面以显示新上传的文件
            location.reload();
          } else {
            alert('文件上传失败: ' + response.message);
          }
        },
        error: function() {
          alert('文件上传请求失败');
        }
      });
    });

    // 更新行可见性和状态的函数
    function updateRowsVisibility() {
      console.log("更新行可见性和状态");

      // 更新选中的数量显示
      var selectedCount = $('.batch-checkbox:checked').length;
      var totalCount = $('.batch-checkbox').length;

      console.log("选中数量:", selectedCount, "总数量:", totalCount);

      // 更新计数器显示
      $('#selectedCount').text(selectedCount);
      $('#totalCount').text(totalCount);

      // 遍历所有行
      $('.batch-row').each(function() {
        var row = $(this);
        var checkbox = row.find('.batch-checkbox');
        var isChecked = checkbox.prop('checked');

        // 如果勾选了，高亮显示行，表示这是要入库的食材
        if (isChecked) {
          row.removeClass('table-secondary').addClass('table-success'); // 使用绿色高亮表示要入库的食材

          // 确保所有输入字段都启用
          row.find('input:not(.batch-checkbox), select').prop('disabled', false);

          // 检查数量和单价
          var itemId = checkbox.val();
          var quantity = parseFloat(row.find('input[name="quantity_' + itemId + '"]').val()) || 0;
          var unitPrice = parseFloat(row.find('input[name="unit_price_' + itemId + '"]').val()) || 0;

          // 如果勾选了但金额为0，显示警告
          if (quantity <= 0 || unitPrice <= 0) {
            row.removeClass('table-success').addClass('table-warning');
          } else {
            row.removeClass('table-warning');
          }
        } else {
          // 未勾选的行，表示不需要入库的食材
          row.removeClass('table-success table-warning').addClass('table-secondary'); // 使用灰色表示不入库

          // 禁用所有输入字段（除了复选框）
          row.find('input:not(.batch-checkbox), select').prop('disabled', true);
        }
      });
    }

    // 复选框变化时更新状态
    $('.batch-checkbox').on('change', function() {
      console.log("复选框状态变化");
      var checkbox = $(this);
      var row = checkbox.closest('tr');
      var isChecked = checkbox.prop('checked');

      console.log("复选框状态:", isChecked ? "已选中" : "未选中");

      if (isChecked) {
        // 启用该行的所有输入字段（除了复选框）
        row.find('input:not(.batch-checkbox), select').prop('disabled', false);
        row.removeClass('table-secondary').addClass('table-success');
      } else {
        // 禁用该行的所有输入字段（除了复选框）
        row.find('input:not(.batch-checkbox), select').prop('disabled', true);
        row.removeClass('table-success table-warning').addClass('table-secondary');
      }

      // 更新所有行的状态
      updateRowsVisibility();
    });

    // 数量和单价变化时更新状态
    $('input[name^="quantity_"], input[name^="unit_price_"]').on('input', function() {
      updateRowsVisibility();
    });

    // 初始化时更新行状态
    setTimeout(function() {
      console.log("初始化更新行状态");
      updateRowsVisibility();
    }, 100);

    // 表单提交前验证和AJAX提交
    $('#batchEditForm').submit(function(e) {
      e.preventDefault(); // 阻止表单默认提交

      var selectedItems = $('.batch-checkbox:checked').length;

      if (selectedItems === 0) {
        alert('请至少勾选一个批次进行入库');
        return false;
      }

      var hasEmptyFields = false;
      var hasZeroAmount = false;
      var emptyFieldsInfo = [];
      var zeroAmountInfo = [];

      $('.batch-checkbox:checked').each(function() {
        var itemId = $(this).val();
        var row = $(this).closest('tr');
        var ingredientName = row.find('td:eq(1) div:first').text().trim();

        var supplier = row.find('select[name="supplier_id_' + itemId + '"]').val();
        var location = row.find('select[name="storage_location_id_' + itemId + '"]').val();
        var quantity = parseFloat(row.find('input[name="quantity_' + itemId + '"]').val()) || 0;
        var unitPrice = parseFloat(row.find('input[name="unit_price_' + itemId + '"]').val()) || 0;
        var productionDate = row.find('input[name="production_date_' + itemId + '"]').val();
        var expiryDate = row.find('input[name="expiry_date_' + itemId + '"]').val();

        var missingFields = [];

        if (!supplier) missingFields.push('供应商');
        if (!location) missingFields.push('存储位置');
        if (!productionDate) missingFields.push('生产日期');
        if (!expiryDate) missingFields.push('过期日期');

        if (missingFields.length > 0) {
          hasEmptyFields = true;
          emptyFieldsInfo.push('食材 "' + ingredientName + '" 缺少以下信息: ' + missingFields.join(', '));
        }

        // 检查数量和单价是否为0
        if (quantity <= 0 || unitPrice <= 0) {
          hasZeroAmount = true;
          zeroAmountInfo.push('食材 "' + ingredientName + '" 的' + (quantity <= 0 ? '数量' : '单价') + '为0');
        }
      });

      if (hasEmptyFields) {
        alert('请为勾选的批次填写完整的信息:\n\n' + emptyFieldsInfo.join('\n'));
        return false;
      }

      if (hasZeroAmount) {
        alert('勾选的批次中有数量或单价为0，请检查:\n\n' + zeroAmountInfo.join('\n'));
        return false;
      }

      // 如果勾选了直接审核，确认用户是否真的要直接审核
      if ($('#direct_approve').is(':checked')) {
        if (!confirm('您选择了保存后直接审核入库，这将使勾选的食材立即入库并更新库存。确定要继续吗？')) {
          return false;
        }
      }

      // 显示保存中提示
      var saveBtn = $('#saveBtn');
      var originalText = saveBtn.html();
      saveBtn.html('<i class="fas fa-spinner fa-spin"></i> 保存中...').prop('disabled', true);

      // 在提交前，临时启用所有选中行的字段，确保数据能够正确提交
      var disabledFields = [];
      $('.batch-checkbox:checked').each(function() {
        var itemId = $(this).val();
        var row = $(this).closest('tr');

        // 获取单价值用于调试
        var unitPrice = row.find('input[name="unit_price_' + itemId + '"]').val();
        console.log('提交前检查 - 项目ID:', itemId, '单价:', unitPrice);

        var disabledInputs = row.find('input:disabled, select:disabled');
        disabledInputs.each(function() {
          var fieldName = $(this).attr('name');
          var fieldValue = $(this).val();
          console.log('启用禁用字段:', fieldName, '值:', fieldValue);
          disabledFields.push($(this));
          $(this).prop('disabled', false);
        });
      });

      // 使用AJAX提交表单
      $.ajax({
        url: $(this).attr('action'),
        type: 'POST',
        data: new FormData(this),
        processData: false,
        contentType: false,
        success: function(response) {
          // 恢复字段的禁用状态
          disabledFields.forEach(function(field) {
            field.prop('disabled', true);
          });

          // 检查响应是否包含重定向URL
          if (response.redirect) {
            // 如果有重定向URL，直接跳转
            window.location.href = response.redirect;
            return;
          }

          // 显示成功消息
          var message = response.message || '所有勾选的批次信息已更新。';
          var alertHtml = '<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                         '<strong>保存成功!</strong> ' + message +
                         '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                         '<span aria-hidden="true">&times;</span></button></div>';

          // 在页面顶部显示成功消息
          $('#alertContainer').html(alertHtml);

          // 滚动到页面顶部
          $('html, body').animate({ scrollTop: 0 }, 'slow');

          // 恢复保存按钮状态
          saveBtn.html(originalText).prop('disabled', false);

          // 如果没有重定向，延迟2秒后自动跳转到详情页面
          setTimeout(function() {
            var detailsUrl = '{{ url_for("stock_in.view_details", id=stock_in.id) }}';
            window.location.href = detailsUrl;
          }, 2000);
        },
        error: function(xhr, status, error) {
          // 恢复字段的禁用状态
          disabledFields.forEach(function(field) {
            field.prop('disabled', true);
          });

          // 显示错误消息
          var errorMessage = '保存失败，请重试。';
          if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
          }

          var alertHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                         '<strong>错误!</strong> ' + errorMessage +
                         '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                         '<span aria-hidden="true">&times;</span></button></div>';

          // 在页面顶部显示错误消息
          $('#alertContainer').html(alertHtml);

          // 滚动到页面顶部
          $('html, body').animate({ scrollTop: 0 }, 'slow');

          // 恢复保存按钮状态
          saveBtn.html(originalText).prop('disabled', false);
        }
      });

      return false; // 阻止表单默认提交
    });

    // 自定义文件输入框显示选中的文件名
    $('.custom-file-input').on('change', function() {
      var fileName = $(this).val().split('\\').pop();
      $(this).next('.custom-file-label').html(fileName);
    });

    // 预览功能
    $('#previewBtn').click(function() {
      console.log("预览按钮被点击");

      // 获取选中的项目
      var selectedItems = [];
      $('.batch-checkbox:checked').each(function() {
        var itemId = $(this).val();
        var row = $(this).closest('tr');

        var ingredientName = row.find('td:eq(1) div:first').text().trim();
        var batchNumber = row.find('td:eq(1) .info-row:eq(0)').text().trim().replace('批次号: ', '');
        var unit = row.find('td:eq(1) .info-row:eq(1)').text().trim().replace('单位: ', '');
        var supplierName = row.find('select[name^="supplier_id_"] option:selected').text();
        var locationName = row.find('select[name^="storage_location_id_"] option:selected').text();
        var productionDate = row.find('input[name^="production_date_"]').val();
        var expiryDate = row.find('input[name^="expiry_date_"]').val();
        var quantity = row.find('input[name^="quantity_"]').val();
        var unitPrice = row.find('input[name^="unit_price_"]').val();
        var totalPrice = (parseFloat(quantity) * parseFloat(unitPrice)).toFixed(2);

        selectedItems.push({
          id: itemId,
          ingredientName: ingredientName,
          batchNumber: batchNumber,
          unit: unit,
          supplierName: supplierName,
          locationName: locationName,
          productionDate: productionDate,
          expiryDate: expiryDate,
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: totalPrice
        });
      });

      console.log("选中的项目数量:", selectedItems.length);

      if (selectedItems.length === 0) {
        alert('请至少选择一个批次进行预览');
        return;
      }

      // 移除之前的对话框（如果存在）
      $('#previewDialog').remove();

      // 创建预览对话框
      var previewDialog = $('<div id="previewDialog" title="A4横向打印预览"></div>');

      // 创建预览内容
      var previewContent = `
        <div class="preview-container" style="width: 100%; padding: 20px; background-color: white; overflow: auto; max-height: 500px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2>入库单</h2>
            <p>单号: {{ stock_in.stock_in_number }}</p>
          </div>

          <div style="margin-bottom: 20px;">
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
              <tr>
                <th style="border: 1px solid #000; padding: 8px; width: 15%; background-color: #f2f2f2;">仓库</th>
                <td style="border: 1px solid #000; padding: 8px; width: 35%;">{{ stock_in.warehouse.name }}</td>
                <th style="border: 1px solid #000; padding: 8px; width: 15%; background-color: #f2f2f2;">入库日期</th>
                <td style="border: 1px solid #000; padding: 8px; width: 35%;">{{ stock_in.stock_in_date|format_datetime('%Y-%m-%d') }}</td>
              </tr>
              <tr>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">入库类型</th>
                <td style="border: 1px solid #000; padding: 8px;">{{ stock_in.stock_in_type }}</td>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">状态</th>
                <td style="border: 1px solid #000; padding: 8px;">{{ stock_in.status }}</td>
              </tr>
              <tr>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">备注</th>
                <td colspan="3" style="border: 1px solid #000; padding: 8px;">{{ stock_in.notes or '无' }}</td>
              </tr>
            </table>
          </div>

          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">序号</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">食材</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">批次号</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">供应商</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">存储位置</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">数量</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">单位</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">单价(元)</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">总价(元)</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">生产日期</th>
                <th style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">过期日期</th>
              </tr>
            </thead>
            <tbody>
      `;

      var totalAmount = 0;

      selectedItems.forEach(function(item, index) {
        previewContent += `
          <tr>
            <td style="border: 1px solid #000; padding: 8px;">${index + 1}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.ingredientName}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.batchNumber}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.supplierName}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.locationName}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.quantity}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.unit}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.unitPrice}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.totalPrice}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.productionDate}</td>
            <td style="border: 1px solid #000; padding: 8px;">${item.expiryDate}</td>
          </tr>
        `;

        totalAmount += parseFloat(item.totalPrice);
      });

      previewContent += `
            </tbody>
            <tfoot>
              <tr>
                <th colspan="8" style="border: 1px solid #000; padding: 8px; text-align: right; background-color: #f2f2f2;">总计金额:</th>
                <th colspan="3" style="border: 1px solid #000; padding: 8px; background-color: #f2f2f2;">${totalAmount.toFixed(2)} 元</th>
              </tr>
            </tfoot>
          </table>

          <div style="text-align: center; margin-top: 20px; font-size: 12px;">
            <p>制单人: {{ current_user.username }} &nbsp;&nbsp;&nbsp;&nbsp; 打印时间: ${new Date().toLocaleString()}</p>
            <p>注: 本单据为入库明细清单，请妥善保管</p>
          </div>
        </div>
      `;

      // 添加预览内容到对话框
      previewDialog.html(previewContent);

      // 添加对话框到页面
      $('body').append(previewDialog);

      try {
        // 显示对话框
        previewDialog.dialog({
          modal: true,
          width: 1000,
          height: 600,
          buttons: {
            "打印": function() {
              $('#printBtn').click();
              $(this).dialog("close");
            },
            "关闭": function() {
              $(this).dialog("close");
            }
          },
          close: function() {
            $(this).dialog("destroy").remove();
          }
        });
        console.log("对话框已创建并显示");
      } catch (error) {
        console.error("创建对话框时出错:", error);

        // 如果对话框创建失败，使用替代方案：直接打开新窗口预览
        var printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>入库单预览</title>');
        printWindow.document.write('<style>body { font-family: Arial, sans-serif; }</style>');
        printWindow.document.write('</head><body>');
        printWindow.document.write(previewContent);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        console.log("已使用替代方案打开预览窗口");
      }
    });

    // 打印功能
    $('#printBtn').click(function() {
      // 获取选中的项目
      var selectedItems = $('.batch-checkbox:checked').length;

      if (selectedItems === 0) {
        alert('请至少选择一个批次进行打印');
        return;
      }

      // 使用系统内置的打印功能
      var printUrl = '{{ url_for("stock_in.print_stock_in", id=stock_in.id) }}';
      window.open(printUrl, '_blank');
    });
  });

  // 打开文档上传模态框
  function openDocumentUploadModal(itemId, ingredientName, batchNumber) {
    $('#targetItemId').val(itemId);
    $('#currentIngredientName').text(ingredientName);
    $('#currentBatchNumber').text(batchNumber);
    $('#batch_numbers').val(batchNumber); // 自动填入当前批次号

    // 重置表单
    $('#uploadDocumentForm')[0].reset();
    $('#targetItemId').val(itemId); // 重新设置，因为reset会清空
    $('#batch_numbers').val(batchNumber); // 重新设置批次号
    $('.custom-file-label').text('选择文件');

    $('#documentUploadModal').modal('show');
  }

  // 上传文档
  $('#uploadDocumentBtn').click(function() {
    const form = $('#uploadDocumentForm')[0];
    const formData = new FormData(form);

    // 验证必填字段
    if (!$('#document_type').val()) {
      alert('请选择文档类型');
      return;
    }

    if (!$('#document_file')[0].files.length) {
      alert('请选择要上传的文件');
      return;
    }

    // 显示加载状态
    const btn = $(this);
    const originalText = btn.html();
    btn.html('<i class="fas fa-spinner fa-spin"></i> 上传中...').prop('disabled', true);

    $.ajax({
      url: `/stock-in/{{ stock_in.id }}/upload-document`,
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success: function(response) {
        if (response.success) {
          alert('文档上传成功！');
          $('#documentUploadModal').modal('hide');

          // 刷新页面以显示新上传的文档
          location.reload();
        } else {
          alert('上传失败: ' + response.message);
        }
      },
      error: function() {
        alert('服务器错误，请稍后重试');
      },
      complete: function() {
        // 恢复按钮状态
        btn.html(originalText).prop('disabled', false);
      }
    });
  });

  // 删除文档
  $(document).on('click', '.delete-document', function() {
    if (confirm('确定要删除该文档吗？')) {
      const documentId = $(this).data('document-id');

      $.ajax({
        url: `/stock-in/delete-document/${documentId}`,
        type: 'POST',
        success: function(response) {
          if (response.success) {
            alert('文档删除成功');
            location.reload();
          } else {
            alert('删除失败: ' + response.message);
          }
        },
        error: function() {
          alert('服务器错误，请稍后重试');
        }
      });
    }
  });

  // 自定义文件输入框显示文件名
  $('.custom-file-input').on('change', function() {
    let fileName = $(this).val().split('\\').pop();
    $(this).next('.custom-file-label').html(fileName);
  });

  // 显示/隐藏批次号列表
  function showBatchNumbers() {
    $('#batchNumbersList').toggle();
  }

  // 添加批次号到文本框
  function addBatchNumber(batchNumber) {
    const textarea = $('#batch_numbers');
    const currentValue = textarea.val().trim();

    // 检查是否已经存在该批次号
    const lines = currentValue.split('\n').map(line => line.trim()).filter(line => line);
    if (lines.includes(batchNumber)) {
      alert('该批次号已经在列表中');
      return;
    }

    // 添加批次号
    if (currentValue) {
      textarea.val(currentValue + '\n' + batchNumber);
    } else {
      textarea.val(batchNumber);
    }

    // 高亮显示文本框
    textarea.focus();
  }

  });
</script>
