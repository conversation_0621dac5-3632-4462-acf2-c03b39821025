"""
简单的重启脚本，不依赖于psutil
"""

import os
import sys
import subprocess
import time
import signal

def restart_flask():
    """重启Flask服务器"""
    print("正在重启Flask服务器...")
    
    # 在Windows上，使用taskkill命令终止Flask进程
    try:
        subprocess.run(["taskkill", "/F", "/IM", "python.exe"], 
                      stdout=subprocess.PIPE, 
                      stderr=subprocess.PIPE)
        print("已终止所有Python进程")
    except Exception as e:
        print(f"终止Python进程时出错: {str(e)}")
    
    # 等待进程完全终止
    time.sleep(2)
    
    # 启动Flask服务器
    try:
        subprocess.Popen(["flask", "run"], shell=True)
        print("Flask服务器已重新启动")
    except Exception as e:
        print(f"启动Flask服务器失败: {str(e)}")

if __name__ == "__main__":
    restart_flask()
