{% extends 'base.html' %}

{% block title %}从周菜单创建采购订单 - {{ super() }}{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/sweetalert2/css/bootstrap-4.css') }}">
<script src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2.min.js') }}"></script>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* 用户引导样式 */
.context-guidance {
    border-left: 4px solid #17a2b8;
    background-color: #f8f9fa;
}
.workflow-context {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}
.previous-step, .current-step, .next-step {
    flex: 1;
    padding: 0 10px;
}
.current-step {
    border-left: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
}
.guidance-tips li {
    margin-bottom: 5px;
}
.step-guide-card {
    margin-bottom: 20px;
}
.alert-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}
.process-step {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    position: relative;
}
.process-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -20px;
    width: 20px;
    height: 2px;
    background-color: #dee2e6;
}
.process-step:first-child::before {
    display: none;
}
.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #17a2b8;
    color: white;
    border-radius: 50%;
    margin-right: 10px;
}
.highlight-box {
    border-left: 4px solid #28a745;
    padding-left: 15px;
    margin-bottom: 15px;
}

/* 原有样式 */
.meal-items .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}
.recipe-info {
    cursor: pointer;
    margin-left: 3px;
}
.ingredients-preview .card {
    padding: 15px;
    margin-bottom: 15px;
}
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.05);
}
.selected-row {
    background-color: rgba(0,123,255,.1) !important;
}
.meal-count {
    font-size: 0.85em;
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
.quantity-input {
    font-size: 1.2em !important;
    font-weight: bold !important;
    color: #dc3545 !important;
    text-align: center !important;
    background-color: #fff3f3 !important;
    border: 2px solid #dc3545 !important;
}
.quantity-input:focus {
    background-color: #fff !important;
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220,53,69,.25) !important;
}
.supplier-select {
    font-size: 1.1em !important;
}
.ingredient-name {
    font-weight: bold;
    color: #495057;
}
.unit-label {
    font-weight: 500;
    color: #6c757d;
}
.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
}
.card-header h6 {
    color: #495057;
    font-weight: bold;
    font-size: 1.1em;
}
.table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 采购计划流程引导 -->
    <div class="context-guidance card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> 采购计划创建 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle alert-icon"></i> <strong>提示：</strong> 采购计划是食堂管理的重要环节，合理的采购计划可以确保食材供应充足，同时避免浪费。
            </div>

            <div class="workflow-context mt-3">
                <div class="previous-step">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-calendar-alt"></i> 周菜单计划</p>
                    <small>已完成周菜单的制定</small>
                    <div class="mt-2">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回菜单计划
                        </a>
                    </div>
                </div>
                <div class="current-step bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="font-weight-bold"><i class="fas fa-shopping-cart"></i> 创建采购计划</p>
                    <small>根据菜单生成采购清单</small>
                </div>
                <div class="next-step">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-clipboard-check"></i> 入库检查</p>
                    <small>对采购的食材进行检查</small>
                    <div class="mt-2">
                        <a href="{{ url_for('inspection.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往入库检查
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作流程指南 -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-tasks"></i> 采购计划创建流程</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">1</span>
                                <strong>选择日期</strong>
                                <p class="small text-muted mt-2 mb-0">选择需要采购食材的日期，可以选择多天一起采购</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">2</span>
                                <strong>生成食材清单</strong>
                                <p class="small text-muted mt-2 mb-0">系统会根据菜单自动计算所需食材的种类和数量</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">3</span>
                                <strong>确认采购信息</strong>
                                <p class="small text-muted mt-2 mb-0">调整采购数量，选择供应商，确认后生成采购订单</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>勾选需要采购的日期（可以选择多天一起采购，减少采购频次）</li>
                    <li>点击"生成食材清单"按钮，系统会自动计算所需食材</li>
                    <li>在弹出的窗口中，可以调整采购数量和选择供应商</li>
                    <li>确认无误后，点击"确认采购"按钮生成采购订单</li>
                    <li>生成的采购订单可以打印或发送给供应商</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ area.name }}采购订单创建</h2>
            <p class="text-muted">{{ week_start }} 至 {{ week_end }}</p>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 周菜单选择卡片 -->
  <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
      <h5 class="mb-0">周菜单安排</h5>
      <p class="text-muted mb-0">请选择需要采购的日期</p>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" id="selectAllDays">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary" id="deselectAllDays">
                    <i class="fas fa-square"></i> 取消全选
                </button>
            </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th style="width: 5%" class="text-center">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="selectAll">
                                    <label class="custom-control-label" for="selectAll"></label>
                                </div>
                            </th>
              <th style="width: 10%">日期</th>
                            <th style="width: 28%">
                                早餐
                                <small class="text-muted">(菜品数: <span class="meal-count" data-meal="早餐">0</span>)</small>
                            </th>
                            <th style="width: 28%">
                                午餐
                                <small class="text-muted">(菜品数: <span class="meal-count" data-meal="午餐">0</span>)</small>
                            </th>
                            <th style="width: 28%">
                                晚餐
                                <small class="text-muted">(菜品数: <span class="meal-count" data-meal="晚餐">0</span>)</small>
                            </th>
            </tr>
          </thead>
          <tbody>
            {% for date_str, day_data in week_data.items() %}
                        <tr class="menu-row" data-date="{{ date_str }}">
              <td class="text-center">
                <div class="custom-control custom-checkbox">
                  <input type="checkbox" class="custom-control-input day-checkbox"
                         id="day_{{ date_str }}"
                         data-date="{{ date_str }}"
                         data-weekday="{{ day_data.weekday }}"
                         data-day-of-week="{{ day_data.day_of_week }}">
                  <label class="custom-control-label" for="day_{{ date_str }}"></label>
                </div>
              </td>
              <td>
                                <strong>{{ day_data.weekday }}</strong><br>
                                <small class="text-muted">{{ date_str }}</small>
              </td>
              {% for meal_type in ['早餐', '午餐', '晚餐'] %}
              <td>
                                <div class="meal-items">
                {% if day_data.meals[meal_type] %}
                  {% for recipe in day_data.meals[meal_type] %}
                                        <span class="badge badge-info">
                                            {{ recipe.name }}
                                            <span class="recipe-info"
                                                  data-toggle="tooltip"
                                                  title="查看食材详情"
                                                  data-recipe-id="{{ recipe.id }}">
                                                <i class="fas fa-info-circle"></i>
                                            </span>
                                        </span>
                  {% endfor %}
                {% else %}
                  <span class="text-muted">未安排</span>
                {% endif %}
                                </div>
              </td>
              {% endfor %}
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
        </div>
        <div class="card-footer text-right">
            <button type="button" id="previewIngredientsBtn" class="btn btn-primary" disabled>
                <i class="fas fa-list"></i> 生成食材清单
            </button>
          </div>
        </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">加载中...</span>
    </div>
  </div>
</div>

<!-- 食材预览模态框 -->
<div class="modal fade" id="ingredientsPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title"><i class="fas fa-list-alt"></i> 食材采购清单</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>

            <!-- 操作指引 -->
            <div class="alert alert-light border-info m-3">
                <div class="d-flex">
                    <div class="mr-3">
                        <i class="fas fa-info-circle text-info fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="alert-heading">食材采购清单操作指引</h6>
                        <p>在此页面您可以：</p>
                        <ol>
                            <li><strong>调整采购数量</strong> - 根据实际需求和库存情况调整每种食材的采购数量</li>
                            <li><strong>选择计量单位</strong> - 选择适合的计量单位（公斤、箱、包等）</li>
                            <li><strong>选择供应商</strong> - 为每种食材选择合适的供应商或设置为自购</li>
                        </ol>
                        <div class="alert alert-warning py-2">
                            <small><i class="fas fa-exclamation-triangle"></i> <strong>注意：</strong> 所有食材必须设置大于0的采购数量，并选择供应商才能提交。</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-body">
                <!-- 这里将通过JavaScript动态填充内容 -->
            </div>

            <!-- 采购提示 -->
            <div class="alert alert-light border-success mx-3 mb-3">
                <h6 class="text-success"><i class="fas fa-lightbulb"></i> 采购建议</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="small mb-0">
                            <li>蔬菜类食材建议少量多次采购，保证新鲜度</li>
                            <li>肉类和冷冻食品可以适当增加采购量，减少采购频次</li>
                            <li>干货类食材可以批量采购，降低采购成本</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="small mb-0">
                            <li>采购前先检查库存，避免重复采购</li>
                            <li>考虑食材的保质期，避免过期浪费</li>
                            <li>根据季节性调整采购策略，选择当季食材</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-arrow-left"></i> 返回修改
                </button>
                <button type="button" class="btn btn-primary btn-lg" id="confirmPurchaseBtn">
                    <i class="fas fa-shopping-cart"></i> 确认采购
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 页面加载完成后执行
  $(document).ready(function() {
    // 存储选中的日期和食谱数据
    let selectedDays = new Set();

    // 更新食材统计
    function updateIngredientStats() {
        const hasSelection = selectedDays.size > 0;
        $('#previewIngredientsBtn').prop('disabled', !hasSelection);

        // 更新每餐菜品数
        updateMealCounts();
    }

    // 更新每餐菜品数
    function updateMealCounts() {
        const mealCounts = {
            '早餐': 0,
            '午餐': 0,
            '晚餐': 0
        };

        $('.menu-row').each(function() {
            const $row = $(this);
            if ($row.find('.day-checkbox').prop('checked')) {
                $row.find('.meal-items').each(function(index) {
                    const mealType = ['早餐', '午餐', '晚餐'][index];
                    const recipeCount = $(this).find('.badge').length;
                    mealCounts[mealType] += recipeCount;
                });
            }
        });

        // 更新显示
        for (const [mealType, count] of Object.entries(mealCounts)) {
            $(`.meal-count[data-meal="${mealType}"]`).text(count);
        }
    }

    // 处理日期选择
    $('.day-checkbox').change(function() {
        const $checkbox = $(this);
        const $row = $checkbox.closest('tr');
        const date = $checkbox.data('date');

        if ($checkbox.prop('checked')) {
            selectedDays.add(date);
            $row.addClass('selected-row');
        } else {
            selectedDays.delete(date);
            $row.removeClass('selected-row');
        }

        updateIngredientStats();
    });

    // 全选/取消全选按钮
    $('#selectAllDays').click(function() {
        $('.day-checkbox').prop('checked', true).change();
    });

    $('#deselectAllDays').click(function() {
        $('.day-checkbox').prop('checked', false).change();
    });

    // 生成食材清单
    $('#previewIngredientsBtn').click(function() {
        const $btn = $(this);
        const $loadingOverlay = $('.loading-overlay');

        // 收集选中日期的数据
        const selectedData = [];
      $('.day-checkbox:checked').each(function() {
            const $checkbox = $(this);
            const $row = $checkbox.closest('tr');
            const date = $checkbox.data('date');
        const meals = [];

            $row.find('.meal-items').each(function(index) {
                const mealType = ['早餐', '午餐', '晚餐'][index];
          const recipes = [];

                $(this).find('.badge').each(function() {
                    const $recipe = $(this);
                    const recipeId = $recipe.find('.recipe-info').data('recipe-id');
                    const recipeName = $recipe.contents().filter(function() {
                        return this.nodeType === 3;
                    }).text().trim();

              recipes.push({
                        id: recipeId,
                        name: recipeName
                    });
              });

                if (recipes.length > 0) {
          meals.push({
            type: mealType,
            recipes: recipes
          });
                }
        });

            selectedData.push({
          date: date,
                weekday: $checkbox.data('weekday'),
                day_of_week: $checkbox.data('day-of-week'),
          meals: meals
        });
      });

        // 显示加载遮罩
        $loadingOverlay.css('display', 'flex');
        $btn.prop('disabled', true);

        // 发送请求获取食材清单
      $.ajax({
            url: "{{ url_for('purchase_order.get_ingredients') }}",
            method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
                'area_id': {{ area.id }},
                'selected_days': selectedData
        }),
        success: function(response) {
          if (response.success) {
                    renderIngredientsPreview(response.data.ingredients);
                    $('#ingredientsPreviewModal').modal('show');
          } else {
                    alert(response.message || '获取食材清单失败');
                }
            },
            error: function() {
                alert('服务器错误，请稍后重试');
            },
            complete: function() {
                $loadingOverlay.hide();
                $btn.prop('disabled', false);
        }
      });
    });

    // 渲染食材预览
    function renderIngredientsPreview(groupedIngredients) {
        const $modalBody = $('#ingredientsPreviewModal .modal-body');
        let html = '';

        // 遍历食材类别
        for (const [category, ingredients] of Object.entries(groupedIngredients)) {
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-utensils mr-2"></i>${category}</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 25%">食材名称</th>
                                        <th style="width: 20%">采购数量</th>
                                        <th style="width: 15%">单位</th>
                                        <th style="width: 35%">采购方式</th>
                                        <th style="width: 5%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>`;

            // 定义常用单位
            const commonUnits = ['公斤', 'KG', '盒', '箱', '包', '袋', '个', '件', '斤', '克'];

            // 遍历该类别下的食材
            for (const ingredient of ingredients) {
                const suppliers = ingredient.suppliers || [];
                const hasSuppliers = suppliers.length > 0;

                html += `
                    <tr>
                        <td>
                            <span class="ingredient-name">${ingredient.name}</span>
                        </td>
                        <td>
                            <input type="number" class="form-control quantity-input"
                                   data-ingredient-id="${ingredient.id}"
                                   value="1"
                                   min="0.1" step="0.1">
                        </td>
                        <td>
                            <select class="form-control unit-select">
                                ${commonUnits.map((unit, index) => `
                                    <option value="${unit}" ${index === 0 ? 'selected' : ''}>${unit}</option>
                                `).join('')}
                            </select>
                        </td>
                        <td>
                            <select class="form-control supplier-select"
                                    data-ingredient-id="${ingredient.id}">
                                ${hasSuppliers ? `
                                    ${suppliers.map((s, index) => `
                                        <option value="${s.product_id}"
                                                data-supplier-id="${s.id}"
                                                ${index === 0 ? 'selected' : ''}>
                                            ${s.name}
                                            ${s.specification ? ` (${s.specification})` : ''}
                                        </option>
                                    `).join('')}
                                    <option value="self">自购</option>
                                ` : `
                                    <option value="self" selected>自购</option>
                                `}
                            </select>
                        </td>
                        <td class="text-center">
                            <button type="button" class="btn btn-sm btn-danger delete-ingredient"
                                    data-ingredient-id="${ingredient.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>`;
            }

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>`;
        }

        $modalBody.html(html);

        // 为数量输入框添加提示
        $('.quantity-input').tooltip({
            title: '请输入采购数量',
            placement: 'top',
            trigger: 'hover'
        });
    }

    // 删除食材按钮点击事件
    $(document).on('click', '.delete-ingredient', function() {
        const $btn = $(this);
        const ingredientId = $btn.data('ingredient-id');
        const $row = $btn.closest('tr');

        // 确认删除
        if (confirm('确定要从采购清单中移除此食材吗？')) {
            $row.fadeOut(300, function() {
                $(this).remove();
            });
        }
    });

    // 确认采购按钮点击事件
    $('#confirmPurchaseBtn').click(function() {
        const $btn = $(this);
        const $loadingOverlay = $('.loading-overlay');

        // 检查是否有未选择供应商的食材
        let hasEmptySupplier = false;
        let hasEmptyQuantity = false;

        $('#ingredientsPreviewModal tbody tr:visible').each(function() {
            const $row = $(this);
            const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
            const supplierValue = $row.find('.supplier-select').val();

            if (quantity > 0 && !supplierValue) {
                hasEmptySupplier = true;
            }
            if (quantity <= 0) {
                hasEmptyQuantity = true;
                $row.find('.quantity-input').addClass('is-invalid');
            } else {
                $row.find('.quantity-input').removeClass('is-invalid');
            }
        });

        if (hasEmptySupplier) {
            alert('请为所有食材选择供应商或设置为自购');
          return;
        }

        if (hasEmptyQuantity) {
            alert('请确保所有食材的采购数量大于0');
          return;
        }

        // 收集采购数据
        const purchaseData = [];
        $('#ingredientsPreviewModal tbody tr:visible').each(function() {
            const $row = $(this);
            const ingredientId = $row.find('.quantity-input').data('ingredient-id');
            const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
            const unit = $row.find('.unit-select').val();
            const $supplierSelect = $row.find('.supplier-select');
            const productId = $supplierSelect.val();

            if (quantity > 0) {
                purchaseData.push({
                    id: ingredientId,
                    purchase_quantity: quantity,
                    unit: unit,
                    product_id: productId === 'self' ? null : productId
                });
            }
        });

        // 显示加载遮罩
        $loadingOverlay.css('display', 'flex');
        $btn.prop('disabled', true);

        // 发送创建订单请求
      $.ajax({
            url: "{{ url_for('purchase_order.create_order') }}",
            method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
          area_id: {{ area.id }},
                ingredients: purchaseData
        }),
        success: function(response) {
          if (response.success) {
                    window.location.href = response.redirect_url;
                } else {
                    // 显示详细的错误信息
                    Swal.fire({
                        title: '创建订单失败',
                        html: `
                            <div class="text-left">
                                <p class="mb-2">创建订单时遇到以下问题：</p>
                                <p class="text-danger">${response.message}</p>
                                <p class="mt-2">请检查：</p>
                                <ul>
                                    <li>是否选择了正确的供应商</li>
                                    <li>采购数量是否合理</li>
                                    <li>是否有必要的权限</li>
                                </ul>
                            </div>
                        `,
                        icon: 'error',
                        confirmButtonText: '确定'
                    });
                }
        },
        error: function(xhr) {
                // 显示详细的错误信息
                let errorMessage = '服务器错误，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire({
                    title: '创建订单失败',
                    html: `
                        <div class="text-left">
                            <p class="mb-2">创建订单时遇到以下问题：</p>
                            <p class="text-danger">${errorMessage}</p>
                            <p class="mt-2">建议操作：</p>
                            <ul>
                                <li>刷新页面重试</li>
                                <li>检查网络连接</li>
                                <li>联系系统管理员</li>
                            </ul>
                        </div>
                    `,
                    icon: 'error',
                    confirmButtonText: '确定'
                });
            },
            complete: function() {
                $loadingOverlay.hide();
                $btn.prop('disabled', false);
        }
      });
    });
  });
</script>
{% endblock %}
