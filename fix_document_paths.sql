-- 修复入库单文档路径中的反斜杠问题
-- 将所有反斜杠 \ 替换为正斜杠 /

-- 1. 查看需要修复的记录
SELECT 
    id,
    stock_in_id,
    document_type,
    file_path AS original_path,
    REPLACE(file_path, '\', '/') AS fixed_path
FROM stock_in_documents 
WHERE file_path LIKE '%\%';

-- 2. 执行修复（将反斜杠替换为正斜杠）
UPDATE stock_in_documents 
SET file_path = REPLACE(file_path, '\', '/')
WHERE file_path LIKE '%\%';

-- 3. 验证修复结果
SELECT 
    COUNT(*) AS total_documents,
    SUM(CASE WHEN file_path LIKE '%\%' THEN 1 ELSE 0 END) AS documents_with_backslash,
    SUM(CASE WHEN file_path LIKE '%/%' THEN 1 ELSE 0 END) AS documents_with_forward_slash
FROM stock_in_documents;

-- 4. 查看修复后的记录
SELECT 
    id,
    stock_in_id,
    document_type,
    file_path,
    created_at
FROM stock_in_documents 
ORDER BY created_at DESC;
