from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import WeeklyMenu, WeeklyMenuRecipe, Recipe, AdministrativeArea
from app.utils.decorators import check_permission
from datetime import datetime, date, timedelta
import json
from sqlalchemy.exc import SQLAlchemyError
from app.services.menu_sync_service import MenuSyncService

weekly_menu_bp = Blueprint('weekly_menu', __name__)

# 使用app/__init__.py中的inject_now函数，不需要在这里重复定义

@weekly_menu_bp.route('/weekly-menu')
@login_required
@check_permission('weekly_menu', 'view')
def index():
    """周菜单列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    week = request.args.get('week', '')  # 新增周筛选参数

    # 构建查询
    query = WeeklyMenu.query.filter(WeeklyMenu.area_id.in_(area_ids))

    # 应用筛选条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    if status:
        query = query.filter_by(status=status)
    if start_date:
        query = query.filter(WeeklyMenu.week_start >= datetime.strptime(start_date, '%Y-%m-%d').date())
    if end_date:
        query = query.filter(WeeklyMenu.week_start <= datetime.strptime(end_date, '%Y-%m-%d').date())
    if week:
        try:
            # 解析周参数，格式为 "2025-W20"（第20周）
            year, week_number = week.split('-W')
            year = int(year)
            week_number = int(week_number)

            # 计算该周的开始日期（周一）
            first_day = date(year, 1, 1)
            if first_day.weekday() > 0:  # 如果1月1日不是周一
                first_day = first_day - timedelta(days=first_day.weekday())
            target_date = first_day + timedelta(weeks=week_number-1)

            # 筛选该周的菜单
            query = query.filter(WeeklyMenu.week_start == target_date)
            current_app.logger.info(f'按周筛选: {week}, 对应日期: {target_date}')
        except Exception as e:
            current_app.logger.error(f'解析周参数失败: {week}, 错误: {str(e)}')

    # 排序并分页
    weekly_menus = query.order_by(WeeklyMenu.week_start.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取所有区域，用于筛选
    areas = accessible_areas

    # 为每个菜单添加周显示信息
    for menu in weekly_menus.items:
        # 计算周数
        week_number = menu.week_start.isocalendar()[1]
        # 格式化显示
        menu.week_display = f"{menu.week_start.year}年第{week_number}周({menu.week_start.strftime('%m-%d')}至{menu.week_end.strftime('%m-%d')})"

    return render_template('weekly_menu/index.html',
                          title='周菜单列表',
                          weekly_menus=weekly_menus,
                          areas=areas,
                          area_id=area_id,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          week=week)

@weekly_menu_bp.route('/weekly-menu/plan', methods=['GET', 'POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def plan():
    """周菜单计划页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果用户没有区域，且没有指定区域，提示选择区域
    if not area_id:
        flash('请选择一个学校进行菜单计划', 'warning')
        return render_template('weekly_menu/select_area.html',
                              areas=accessible_areas,
                              title='选择学校')

    # 检查用户是否有权限访问该区域
    # 系统管理员、学校管理员（一把手）或有权限访问该区域的用户可以访问
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('weekly_menu.index'))

    # 获取区域信息
    area = AdministrativeArea.query.get_or_404(area_id)

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')
    else:
        week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()

    # 计算周结束日期（7天，周一至周日）
    week_end = week_start + timedelta(days=6)

    # 检查是否已存在该周的菜单计划
    existing_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=week_start
    ).first()

    # 获取当前日期
    today = date.today()

    # 计算本周一和下周一
    this_week_monday = today - timedelta(days=today.weekday())
    next_week_monday = this_week_monday + timedelta(days=7)
    last_week_monday = this_week_monday - timedelta(days=7)

    # 检查当前选择的周是否是本周、上周或下周
    is_this_week = week_start == this_week_monday
    is_next_week = week_start == next_week_monday
    is_last_week = week_start == last_week_monday

    # 检查本周和上周菜单是否已发布
    this_week_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=this_week_monday
    ).first()

    last_week_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=last_week_monday
    ).first()

    this_week_published = this_week_menu and this_week_menu.status == '已发布'
    last_week_published = last_week_menu and last_week_menu.status == '已发布'

    # 确定是否可编辑
    is_editable = True

    # 如果是上周或更早的菜单，不可编辑
    if week_start <= last_week_monday:
        is_editable = False

    # 如果是本周菜单且已发布，不可编辑
    if is_this_week and this_week_published:
        is_editable = False

    # 如果当前菜单已发布，不可编辑
    if existing_menu and existing_menu.status == '已发布':
        is_editable = False

    # 生成周次导航数据
    available_weeks = []

    # 上周
    last_week_end = last_week_monday + timedelta(days=6)
    available_weeks.append({
        'start_date': last_week_monday.strftime('%Y-%m-%d'),
        'end_date': last_week_end.strftime('%Y-%m-%d'),
        'display_text': f'上周 ({last_week_monday.strftime("%m-%d")}至{last_week_end.strftime("%m-%d")})',
        'is_editable': False,  # 上周不可编辑
        'is_viewable': True,
        'status': last_week_menu.status if last_week_menu else '未创建'
    })

    # 本周
    this_week_end = this_week_monday + timedelta(days=6)
    available_weeks.append({
        'start_date': this_week_monday.strftime('%Y-%m-%d'),
        'end_date': this_week_end.strftime('%Y-%m-%d'),
        'display_text': f'本周 ({this_week_monday.strftime("%m-%d")}至{this_week_end.strftime("%m-%d")})',
        'is_editable': not this_week_published,  # 如果本周菜单未发布，则可编辑
        'is_viewable': True,
        'status': this_week_menu.status if this_week_menu else '未创建'
    })

    # 下周
    next_week_end = next_week_monday + timedelta(days=6)
    next_week_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=next_week_monday
    ).first()

    available_weeks.append({
        'start_date': next_week_monday.strftime('%Y-%m-%d'),
        'end_date': next_week_end.strftime('%Y-%m-%d'),
        'display_text': f'下周 ({next_week_monday.strftime("%m-%d")}至{next_week_end.strftime("%m-%d")})',
        'is_editable': True,  # 下周始终可编辑
        'is_viewable': True,
        'status': next_week_menu.status if next_week_menu else '未创建'
    })

    if existing_menu:
        current_app.logger.info(f'找到现有周菜单计划: id={existing_menu.id}, area_id={area_id}, week_start={week_start}')
    else:
        current_app.logger.info(f'未找到现有周菜单计划: area_id={area_id}, week_start={week_start}')

    # 如果是POST请求，处理表单提交
    if request.method == 'POST':
        try:
            # 获取表单数据
            menu_data = json.loads(request.form.get('menu_data', '{}'))

            # 开始事务
            db.session.begin_nested()

            try:
                # 使用原始SQL处理数据，避免ORM的datetime处理问题
                from sqlalchemy import text

                # 如果已存在菜单计划，则更新
                if existing_menu:
                    weekly_menu_id = existing_menu.id

                    # 删除现有的菜单食谱
                    delete_sql = text("""
                    DELETE FROM weekly_menu_recipes
                    WHERE weekly_menu_id = :weekly_menu_id
                    """)

                    db.session.execute(delete_sql, {'weekly_menu_id': weekly_menu_id})
                    current_app.logger.info(f'删除现有菜单食谱: weekly_menu_id={weekly_menu_id}')
                else:
                    # 创建新的周菜单计划
                    insert_sql = text("""
                    INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
                    OUTPUT inserted.id
                    VALUES (:area_id, :week_start, :week_end, :status, :created_by)
                    """)

                    result = db.session.execute(
                        insert_sql,
                        {
                            'area_id': area_id,
                            'week_start': week_start,
                            'week_end': week_end,
                            'status': '计划中',
                            'created_by': current_user.id
                        }
                    )

                    weekly_menu_id = result.scalar()
                    current_app.logger.info(f'创建新的周菜单计划: id={weekly_menu_id}')

                # 添加菜单食谱
                for day_str, meals in menu_data.items():
                    # 计算星期几（1-7表示周一到周日）
                    day_date = datetime.strptime(day_str, '%Y-%m-%d').date()
                    day_of_week = day_date.weekday() + 1  # 0-6 转为 1-7

                    for meal_type, recipes in meals.items():
                        for recipe_data in recipes:
                            recipe_id = recipe_data.get('id')
                            recipe_name = recipe_data.get('name')

                            # 处理recipe_id
                            final_recipe_id = None
                            if recipe_id and not str(recipe_id).startswith('custom_'):
                                final_recipe_id = recipe_id

                            # 使用原始SQL插入菜单食谱关联
                            insert_recipe_sql = text("""
                            INSERT INTO weekly_menu_recipes
                            (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name)
                            VALUES
                            (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name)
                            """)

                            db.session.execute(
                                insert_recipe_sql,
                                {
                                    'weekly_menu_id': weekly_menu_id,
                                    'day_of_week': day_of_week,
                                    'meal_type': meal_type,
                                    'recipe_id': final_recipe_id,
                                    'recipe_name': recipe_name
                                }
                            )

                # 提交事务
                db.session.commit()
                current_app.logger.info(f'成功保存周菜单计划: id={weekly_menu_id}')

                # 获取保存后的菜单对象，用于重定向
                weekly_menu = WeeklyMenu.query.get(weekly_menu_id)

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f'保存周菜单计划时出错: {str(e)}')
                raise e
            flash('周菜单计划已保存', 'success')
            return redirect(url_for('weekly_menu.view', id=weekly_menu.id))

        except Exception as e:
            db.session.rollback()
            flash(f'保存周菜单计划失败: {str(e)}', 'danger')
            current_app.logger.error(f'保存周菜单计划失败: {str(e)}')

    # 获取所有食谱，按分类组织
    recipes = Recipe.query.filter_by(status=1).all()
    recipes_by_category = {}
    for recipe in recipes:
        category = recipe.category
        if category not in recipes_by_category:
            recipes_by_category[category] = []
        recipes_by_category[category].append(recipe)

    # 生成周日期数据
    week_dates = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五']
    for i in range(5):  # 默认5天，周一到周五
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        week_dates[date_str] = {
            'date': date_str,
            'weekday': weekdays[i]
        }

    # 如果已存在菜单计划，获取现有数据
    menu_data = {}
    if existing_menu:
        menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=existing_menu.id).all()
        current_app.logger.info(f'找到 {len(menu_recipes)} 条菜谱记录')

        for menu_recipe in menu_recipes:
            # 计算日期字符串
            day_date = week_start + timedelta(days=menu_recipe.day_of_week - 1)
            date_str = day_date.strftime('%Y-%m-%d')

            # 初始化日期和餐次
            if date_str not in menu_data:
                menu_data[date_str] = {}
            if menu_recipe.meal_type not in menu_data[date_str]:
                menu_data[date_str][menu_recipe.meal_type] = []

            # 添加食谱
            recipe_data = {
                'id': menu_recipe.recipe_id or 'custom',
                'name': menu_recipe.recipe_name
            }
            menu_data[date_str][menu_recipe.meal_type].append(recipe_data)
            current_app.logger.info(f'加载菜谱: 日期={date_str}, 餐次={menu_recipe.meal_type}, 菜谱={recipe_data}')

        current_app.logger.info(f'加载的菜单数据: {menu_data}')
    else:
        current_app.logger.info('没有现有菜单数据，初始化为空')

    return render_template('weekly_menu/plan.html',
                          title=f'{area.name}周菜单计划',
                          area=area,
                          area_id=area_id,  # 添加区域ID
                          week_start=week_start_str,
                          week_end=week_end.strftime('%Y-%m-%d'),
                          week_dates=week_dates,
                          recipes_by_category=recipes_by_category,
                          menu_data=menu_data,
                          existing_menu=existing_menu,
                          is_editable=is_editable,
                          available_weeks=available_weeks)

@weekly_menu_bp.route('/weekly-menu/<int:id>')
@login_required
@check_permission('weekly_menu', 'view')
def view(id):
    """查看周菜单计划详情"""
    # 添加时间戳参数，避免缓存问题
    timestamp = request.args.get('t', '')
    current_app.logger.info(f'查看周菜单计划: id={id}, timestamp={timestamp}')

    # 使用 .options(db.joinedload()) 预加载关联数据，提高性能
    weekly_menu = WeeklyMenu.query.options(
        db.joinedload(WeeklyMenu.area),
        db.joinedload(WeeklyMenu.creator)
    ).get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限查看该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu.index'))

    # 获取菜单食谱 - 使用 .all() 确保立即执行查询
    menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=id).all()
    current_app.logger.info(f'获取到 {len(menu_recipes)} 条菜谱记录')

    # 按日期和餐次组织数据
    week_data = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

    # 初始化数据结构
    for i in range((weekly_menu.week_end - weekly_menu.week_start).days + 1):
        current_date = weekly_menu.week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        day_of_week = current_date.weekday()  # 0-6

        week_data[date_str] = {
            'date': date_str,
            'weekday': weekdays[day_of_week],
            'meals': {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }
        }

    # 填充菜单数据
    for recipe in menu_recipes:
        day_date = weekly_menu.week_start + timedelta(days=recipe.day_of_week - 1)
        date_str = day_date.strftime('%Y-%m-%d')

        if date_str in week_data:
            week_data[date_str]['meals'][recipe.meal_type].append({
                'id': recipe.id,
                'recipe_id': recipe.recipe_id,
                'name': recipe.recipe_name
            })

    return render_template('weekly_menu/view.html',
                          title=f'{weekly_menu.area.name}周菜单计划',
                          weekly_menu=weekly_menu,
                          week_data=week_data)

@weekly_menu_bp.route('/weekly-menu/<int:id>/publish', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'publish')
def publish(id):
    """发布周菜单计划"""
    weekly_menu = WeeklyMenu.query.get_or_404(id)

    # 检查用户是否有权限发布
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限发布该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu.index'))

    # 使用原始SQL更新状态，避免ORM的datetime处理问题
    from sqlalchemy import text

    update_sql = text("""
    UPDATE weekly_menus
    SET status = '已发布'
    WHERE id = :id
    """)

    db.session.execute(update_sql, {'id': id})
    db.session.commit()

    # 同步周菜单数据到工作日志
    try:
        sync_result = MenuSyncService.sync_weekly_menu_to_daily_logs(id)
        if sync_result['success']:
            results = sync_result['results']
            current_app.logger.info(f"同步周菜单到工作日志成功: 更新={results['updated_logs']}, 创建={results['created_logs']}, 跳过={results['skipped_logs']}")
            flash(f"周菜单计划已发布，并已同步到工作日志（更新{results['updated_logs']}条，创建{results['created_logs']}条）", 'success')
        else:
            current_app.logger.error(f"同步周菜单到工作日志失败: {sync_result['message']}")
            flash(f"周菜单计划已发布，但同步到工作日志失败: {sync_result['message']}", 'warning')
    except Exception as e:
        current_app.logger.error(f"同步周菜单到工作日志时出错: {str(e)}")
        flash(f"周菜单计划已发布，但同步到工作日志时出错: {str(e)}", 'warning')

    # 获取周开始日期和区域ID，用于重定向回周菜单计划页面
    week_start = weekly_menu.week_start.strftime('%Y-%m-%d')
    area_id = weekly_menu.area_id

    # 重定向回周菜单计划页面
    return redirect(url_for('weekly_menu.plan', area_id=area_id, week_start=week_start))

@weekly_menu_bp.route('/weekly-menu/<int:id>/unpublish', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'publish')
def unpublish(id):
    """解除发布周菜单计划"""
    weekly_menu = WeeklyMenu.query.get_or_404(id)

    # 检查用户是否有权限解除发布
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限解除发布该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu.index'))

    # 检查菜单是否已发布
    if weekly_menu.status != '已发布':
        flash('只有已发布的菜单才能解除发布', 'warning')
        return redirect(url_for('weekly_menu.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start.strftime('%Y-%m-%d')))

    # 使用原始SQL更新状态，避免ORM的datetime处理问题
    from sqlalchemy import text

    update_sql = text("""
    UPDATE weekly_menus
    SET status = '计划中'
    WHERE id = :id
    """)

    db.session.execute(update_sql, {'id': id})
    db.session.commit()

    flash('周菜单计划已解除发布', 'success')

    # 获取周开始日期和区域ID，用于重定向回周菜单计划页面
    week_start = weekly_menu.week_start.strftime('%Y-%m-%d')
    area_id = weekly_menu.area_id

    # 重定向回周菜单计划页面
    return redirect(url_for('weekly_menu.plan', area_id=area_id, week_start=week_start))

    # 使用原始SQL更新状态，避免ORM的datetime处理问题
    from sqlalchemy import text

    update_sql = text("""
    UPDATE weekly_menus
    SET status = '计划中'
    WHERE id = :id
    """)

    db.session.execute(update_sql, {'id': id})
    db.session.commit()

    # 获取周开始日期和区域ID，用于重定向回周菜单计划页面
    week_start = weekly_menu.week_start.strftime('%Y-%m-%d')
    area_id = weekly_menu.area_id

    flash('周菜单计划已解除发布，现在可以进行修改', 'success')
    return redirect(url_for('weekly_menu.plan', area_id=area_id, week_start=week_start))

@weekly_menu_bp.route('/api/weekly-menu/save', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def save_menu_item():
    """保存单个餐次的菜谱（API端点）"""
    try:
        data = request.get_json()
        if not data:
            current_app.logger.error('请求数据为空')
            return jsonify({'error': '请求数据为空'}), 400

        # 获取并验证必要参数
        area_id = data.get('area_id')
        week_start_str = data.get('week_start')
        date_str = data.get('date')
        meal_type = data.get('meal_type')
        recipes = data.get('recipes', [])

        if not all([area_id, week_start_str, date_str, meal_type]):
            current_app.logger.error(f'缺少必要参数: area_id={area_id}, week_start={week_start_str}, date={date_str}, meal_type={meal_type}')
            return jsonify({'error': '缺少必要参数'}), 400

        try:
            # 解析日期
            week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
            week_end = week_start + timedelta(days=4)  # 默认5天
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
            day_of_week = date.weekday() + 1  # 0-6 转为 1-7
        except ValueError as e:
            current_app.logger.error(f'日期格式错误: {str(e)}')
            return jsonify({'error': '日期格式错误'}), 400

        # 开始数据库事务
        try:
            # 使用原始SQL处理数据，避免ORM的datetime处理问题
            from sqlalchemy import text

            # 检查是否已存在该周的菜单计划
            check_sql = text("""
            SELECT id FROM weekly_menus
            WHERE area_id = :area_id AND week_start = :week_start
            """)

            result = db.session.execute(check_sql, {
                'area_id': area_id,
                'week_start': week_start
            })

            weekly_menu_id = result.scalar()

            # 如果不存在周菜单，创建一个新的
            if not weekly_menu_id:
                insert_sql = text("""
                INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
                OUTPUT inserted.id
                VALUES (:area_id, :week_start, :week_end, :status, :created_by)
                """)

                result = db.session.execute(
                    insert_sql,
                    {
                        'area_id': area_id,
                        'week_start': week_start,
                        'week_end': week_end,
                        'status': '计划中',
                        'created_by': current_user.id
                    }
                )

                weekly_menu_id = result.scalar()
                current_app.logger.info(f'创建新的周菜单计划: id={weekly_menu_id}, area_id={area_id}, week_start={week_start}')
            else:
                current_app.logger.info(f'更新现有周菜单计划: id={weekly_menu_id}, area_id={area_id}, week_start={week_start}')

            # 删除该日期和餐次的现有菜谱
            delete_sql = text("""
            DELETE FROM weekly_menu_recipes
            WHERE weekly_menu_id = :weekly_menu_id
            AND day_of_week = :day_of_week
            AND meal_type = :meal_type
            """)

            result = db.session.execute(
                delete_sql,
                {
                    'weekly_menu_id': weekly_menu_id,
                    'day_of_week': day_of_week,
                    'meal_type': meal_type
                }
            )

            deleted_count = result.rowcount
            current_app.logger.info(f'删除现有菜谱: weekly_menu_id={weekly_menu_id}, day_of_week={day_of_week}, meal_type={meal_type}, count={deleted_count}')

            # 添加新的菜谱
            for recipe_data in recipes:
                recipe_id = recipe_data.get('id')
                recipe_name = recipe_data.get('name')

                if not recipe_name:
                    current_app.logger.error(f'菜谱数据缺少name字段: {recipe_data}')
                    continue

                # 处理recipe_id
                final_recipe_id = None
                if recipe_id and not str(recipe_id).startswith('custom_'):
                    final_recipe_id = recipe_id

                # 使用原始SQL插入菜单食谱关联
                insert_recipe_sql = text("""
                INSERT INTO weekly_menu_recipes
                (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name)
                VALUES
                (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name)
                """)

                db.session.execute(
                    insert_recipe_sql,
                    {
                        'weekly_menu_id': weekly_menu_id,
                        'day_of_week': day_of_week,
                        'meal_type': meal_type,
                        'recipe_id': final_recipe_id,
                        'recipe_name': recipe_name
                    }
                )
                current_app.logger.info(f'添加菜谱: recipe_id={recipe_id}, recipe_name={recipe_name}')

            # 提交事务
            db.session.commit()
            current_app.logger.info('成功保存菜单数据')
            return jsonify({'success': True, 'message': '保存成功'})

        except SQLAlchemyError as e:
            db.session.rollback()
            error_msg = f'数据库错误: {str(e)}'
            current_app.logger.error(error_msg)
            return jsonify({'error': error_msg}), 500

    except Exception as e:
        current_app.logger.error(f'未知错误: {str(e)}')
        return jsonify({'error': '服务器内部错误'}), 500

@weekly_menu_bp.route('/api/weekly-menu/week/save', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')  # 使用 weekly_menu 权限，与其他函数保持一致
def save_week_menu():
    """保存周菜单数据 - 按时间保存到数据库，不管用户填入了多少
    如果未生成周菜单，则自动生成，如果已生成则按最新的更新周菜单
    """
    try:
        # 记录请求开始时间，用于性能分析
        start_time = datetime.now()

        # 获取并验证请求数据
        data = request.get_json()
        if not data:
            current_app.logger.error("请求数据为空")
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            })

        area_id = data.get('area_id')
        week_start = data.get('week_start')
        menu_data = data.get('menu_data', {})

        # 参数验证
        if not area_id:
            current_app.logger.error("缺少区域ID")
            return jsonify({'success': False, 'message': '缺少区域ID'})
        if not week_start:
            current_app.logger.error("缺少周开始日期")
            return jsonify({'success': False, 'message': '缺少周开始日期'})

        # 如果menu_data为空，初始化为空对象
        if not menu_data:
            current_app.logger.warning("菜单数据为空，将使用空对象")
            menu_data = {}

        current_app.logger.info(f"区域ID: {area_id}, 周开始日期: {week_start}")

        # 放宽区域访问权限检查，允许管理员和学校管理员访问
        can_access = current_user.is_admin() or current_user.has_role('学校管理员') or current_user.can_access_area_by_id(area_id)
        current_app.logger.info(f"用户是否有权限访问区域 {area_id}: {can_access}")
        if not can_access:
            current_app.logger.error(f"用户无权访问区域 {area_id}")
            return jsonify({
                'success': False,
                'message': f'您没有权限访问区域 {area_id}'
            })

        # 获取一周的日期范围
        try:
            week_start_date = datetime.strptime(week_start, '%Y-%m-%d').date()
            week_end_date = week_start_date + timedelta(days=6)  # 7天，周一至周日
            current_app.logger.info(f"周日期范围: {week_start_date} 至 {week_end_date}")
        except ValueError as e:
            current_app.logger.error(f"日期格式错误: {week_start}, 错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'日期格式错误: {week_start}'
            })

        # 使用事务处理
        try:
            # 使用原始SQL处理数据，避免ORM的datetime处理问题
            from sqlalchemy import text

            # 检查是否已存在该周的菜单计划
            check_sql = text("""
            SELECT id, status FROM weekly_menus
            WHERE area_id = :area_id AND week_start = :week_start
            """)

            result = db.session.execute(check_sql, {
                'area_id': area_id,
                'week_start': str(week_start_date)  # 将日期转换为字符串
            })

            row = result.fetchone()
            weekly_menu_id = row[0] if row else None
            menu_status = row[1] if row else None

            # 如果菜单已发布，不允许修改
            if menu_status == '已发布':
                return jsonify({
                    'success': False,
                    'message': '菜单已发布，无法修改。请先解除发布。'
                })

            # 如果不存在周菜单，创建一个新的
            if not weekly_menu_id:
                insert_sql = text("""
                INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
                OUTPUT inserted.id
                VALUES (:area_id, :week_start, :week_end, :status, :created_by)
                """)

                result = db.session.execute(
                    insert_sql,
                    {
                        'area_id': area_id,
                        'week_start': str(week_start_date),  # 将日期转换为字符串
                        'week_end': str(week_end_date),      # 将日期转换为字符串
                        'status': '计划中',
                        'created_by': current_user.id
                    }
                )

                weekly_menu_id = result.scalar()
                current_app.logger.info(f'创建新的周菜单计划: id={weekly_menu_id}, area_id={area_id}, week_start={week_start_date}')
                is_new = True
            else:
                current_app.logger.info(f'更新现有周菜单计划: id={weekly_menu_id}, area_id={area_id}, week_start={week_start_date}')
                is_new = False

            # 删除现有的菜单食谱
            delete_sql = text("""
            DELETE FROM weekly_menu_recipes
            WHERE weekly_menu_id = :weekly_menu_id
            """)

            result = db.session.execute(delete_sql, {'weekly_menu_id': weekly_menu_id})
            deleted_count = result.rowcount
            current_app.logger.info(f'删除现有菜单食谱: weekly_menu_id={weekly_menu_id}, count={deleted_count}')

            # 添加新的菜单食谱
            recipes_count = 0
            for date_str, meals in menu_data.items():
                try:
                    # 计算星期几（1-7表示周一到周日）
                    day_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    day_of_week = day_date.weekday() + 1  # 0-6 转为 1-7

                    for meal_type, recipes in meals.items():
                        for recipe_data in recipes:
                            recipe_id = recipe_data.get('id')
                            recipe_name = recipe_data.get('name')

                            if not recipe_name:
                                continue

                            # 处理recipe_id
                            final_recipe_id = None
                            if recipe_id and not str(recipe_id).startswith('custom_'):
                                try:
                                    final_recipe_id = int(recipe_id)
                                except (ValueError, TypeError):
                                    final_recipe_id = None

                            # 使用原始SQL插入菜单食谱关联
                            insert_recipe_sql = text("""
                            INSERT INTO weekly_menu_recipes
                            (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name)
                            VALUES
                            (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name)
                            """)

                            db.session.execute(
                                insert_recipe_sql,
                                {
                                    'weekly_menu_id': weekly_menu_id,
                                    'day_of_week': day_of_week,
                                    'meal_type': meal_type,
                                    'recipe_id': final_recipe_id,
                                    'recipe_name': recipe_name
                                }
                            )
                            recipes_count += 1
                except Exception as e:
                    current_app.logger.error(f"处理日期 {date_str} 的数据时出错: {str(e)}")
                    continue

            # 提交事务
            db.session.commit()

            # 计算周次
            week_number = week_start_date.isocalendar()[1]

            # 记录总耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            return jsonify({
                'success': True,
                'message': f'第{week_number}周菜单保存成功',
                'weekly_menu_id': weekly_menu_id,
                'is_new': is_new,
                'week_number': week_number,
                'stats': {
                    'deleted_recipes': deleted_count,
                    'added_recipes': recipes_count,
                    'duration': duration
                }
            })

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"保存周菜单失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            })

    except Exception as e:
        current_app.logger.error(f"未知错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}'
        })

@weekly_menu_bp.route('/weekly-menu/<int:id>/delete', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def delete_menu(id):
    """删除周菜单计划"""
    weekly_menu = WeeklyMenu.query.get_or_404(id)

    # 检查用户是否有权限删除
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限删除该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu.index'))

    try:
        # 开始事务
        db.session.begin_nested()

        try:
            # 使用原始SQL处理数据，避免ORM的datetime处理问题
            from sqlalchemy import text

            # 记录日志
            current_app.logger.info(f'删除周菜单计划: id={weekly_menu.id}, area_id={weekly_menu.area_id}, week_start={weekly_menu.week_start}')

            # 先删除菜单食谱
            delete_recipes_sql = text("""
            DELETE FROM weekly_menu_recipes
            WHERE weekly_menu_id = :weekly_menu_id
            """)

            result = db.session.execute(delete_recipes_sql, {'weekly_menu_id': id})
            deleted_recipes = result.rowcount
            current_app.logger.info(f'删除菜单食谱: count={deleted_recipes}')

            # 再删除菜单计划
            delete_menu_sql = text("""
            DELETE FROM weekly_menus
            WHERE id = :id
            """)

            db.session.execute(delete_menu_sql, {'id': id})

            # 提交事务
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise e

        flash('周菜单计划已成功删除', 'success')
        return redirect(url_for('weekly_menu.index'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除周菜单计划失败: {str(e)}')
        flash(f'删除周菜单计划失败: {str(e)}', 'danger')
        return redirect(url_for('weekly_menu.view', id=id))

@weekly_menu_bp.route('/weekly-menu/plan-improved', methods=['GET', 'POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def plan_improved():
    """改进的周菜单计划页面 - 使用更好的模态框实现"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果用户没有区域，且没有指定区域，提示选择区域
    if not area_id:
        flash('请选择一个学校进行菜单计划', 'warning')
        return render_template('weekly_menu/select_area.html',
                              areas=accessible_areas,
                              title='选择学校')

    # 检查用户是否有权限访问该区域
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('weekly_menu.index'))

    # 获取区域信息
    area = AdministrativeArea.query.get_or_404(area_id)

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')
    else:
        week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()

    # 计算周结束日期（默认为周五，5天）
    week_end = week_start + timedelta(days=4)

    # 检查是否已存在该周的菜单计划
    existing_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=week_start
    ).first()

    if existing_menu:
        current_app.logger.info(f'找到现有周菜单计划: id={existing_menu.id}, area_id={area_id}, week_start={week_start}')
    else:
        current_app.logger.info(f'未找到现有周菜单计划: area_id={area_id}, week_start={week_start}')

    # 如果是POST请求，处理表单提交
    if request.method == 'POST':
        try:
            # 获取表单数据
            menu_data = json.loads(request.form.get('menu_data', '{}'))

            # 开始事务
            db.session.begin_nested()

            try:
                # 使用原始SQL处理数据，避免ORM的datetime处理问题
                from sqlalchemy import text

                # 如果已存在菜单计划，则更新
                if existing_menu:
                    weekly_menu_id = existing_menu.id

                    # 删除现有的菜单食谱
                    delete_sql = text("""
                    DELETE FROM weekly_menu_recipes
                    WHERE weekly_menu_id = :weekly_menu_id
                    """)

                    db.session.execute(delete_sql, {'weekly_menu_id': weekly_menu_id})
                    current_app.logger.info(f'删除现有菜单食谱: weekly_menu_id={weekly_menu_id}')
                else:
                    # 创建新的周菜单计划
                    insert_sql = text("""
                    INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
                    OUTPUT inserted.id
                    VALUES (:area_id, :week_start, :week_end, :status, :created_by)
                    """)

                    result = db.session.execute(
                        insert_sql,
                        {
                            'area_id': area_id,
                            'week_start': str(week_start),  # 将日期转换为字符串
                            'week_end': str(week_end),      # 将日期转换为字符串
                            'status': '计划中',
                            'created_by': current_user.id
                        }
                    )

                    weekly_menu_id = result.scalar()
                    current_app.logger.info(f'创建新的周菜单计划: id={weekly_menu_id}')

                # 添加菜单食谱
                for day_str, meals in menu_data.items():
                    # 计算星期几（1-7表示周一到周日）
                    day_date = datetime.strptime(day_str, '%Y-%m-%d').date()
                    day_of_week = day_date.weekday() + 1  # 0-6 转为 1-7

                    for meal_type, recipes in meals.items():
                        for recipe_data in recipes:
                            recipe_id = recipe_data.get('id')
                            recipe_name = recipe_data.get('name')

                            # 处理recipe_id
                            final_recipe_id = None
                            if recipe_id and not str(recipe_id).startswith('custom_'):
                                final_recipe_id = recipe_id

                            # 使用原始SQL插入菜单食谱关联
                            insert_recipe_sql = text("""
                            INSERT INTO weekly_menu_recipes
                            (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name)
                            VALUES
                            (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name)
                            """)

                            db.session.execute(
                                insert_recipe_sql,
                                {
                                    'weekly_menu_id': weekly_menu_id,
                                    'day_of_week': day_of_week,
                                    'meal_type': meal_type,
                                    'recipe_id': final_recipe_id,
                                    'recipe_name': recipe_name
                                }
                            )

                # 提交事务
                db.session.commit()
                current_app.logger.info(f'成功保存周菜单计划: id={weekly_menu_id}')

                # 获取保存后的菜单对象，用于重定向
                weekly_menu = WeeklyMenu.query.get(weekly_menu_id)

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f'保存周菜单计划时出错: {str(e)}')
                raise e
            flash('周菜单计划已保存', 'success')
            return redirect(url_for('weekly_menu.view', id=weekly_menu.id))

        except Exception as e:
            db.session.rollback()
            flash(f'保存周菜单计划失败: {str(e)}', 'danger')
            current_app.logger.error(f'保存周菜单计划失败: {str(e)}')

    # 获取所有食谱，按分类组织
    recipes = Recipe.query.filter_by(status=1).all()
    recipes_by_category = {}
    for recipe in recipes:
        category = recipe.category
        if category not in recipes_by_category:
            recipes_by_category[category] = []
        recipes_by_category[category].append(recipe)

    # 生成周日期数据
    week_dates = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五']
    for i in range(5):  # 默认5天，周一到周五
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        week_dates[date_str] = {
            'date': date_str,
            'weekday': weekdays[i]
        }

    # 如果已存在菜单计划，获取现有数据
    menu_data = {}
    if existing_menu:
        menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=existing_menu.id).all()
        current_app.logger.info(f'找到 {len(menu_recipes)} 条菜谱记录')

        for menu_recipe in menu_recipes:
            # 计算日期字符串
            day_date = week_start + timedelta(days=menu_recipe.day_of_week - 1)
            date_str = day_date.strftime('%Y-%m-%d')

            # 初始化日期和餐次
            if date_str not in menu_data:
                menu_data[date_str] = {}
            if menu_recipe.meal_type not in menu_data[date_str]:
                menu_data[date_str][menu_recipe.meal_type] = []

            # 添加食谱
            recipe_data = {
                'id': menu_recipe.recipe_id or f'custom_{menu_recipe.id}',
                'name': menu_recipe.recipe_name
            }
            menu_data[date_str][menu_recipe.meal_type].append(recipe_data)
            current_app.logger.info(f'加载菜谱: 日期={date_str}, 餐次={menu_recipe.meal_type}, 菜谱={recipe_data}')

        current_app.logger.info(f'加载的菜单数据: {menu_data}')
    else:
        current_app.logger.info('没有现有菜单数据，初始化为空')

    return render_template('weekly_menu/plan_improved.html',
                          title=f'{area.name}周菜单计划',
                          area=area,
                          area_id=area_id,
                          week_start=week_start_str,
                          week_end=week_end.strftime('%Y-%m-%d'),
                          week_dates=week_dates,
                          recipes_by_category=recipes_by_category,
                          menu_data=menu_data,
                          existing_menu=existing_menu)

@weekly_menu_bp.route('/weekly-menu/plan-time-aware', methods=['GET', 'POST'])
@login_required
@check_permission('menu_plan', 'view')
def plan_time_aware():
    """时间感知的周菜单计划页面 - 考虑日历和时间因素"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果用户没有区域，且没有指定区域，提示选择区域
    if not area_id:
        flash('请选择一个学校进行菜单计划', 'warning')
        return render_template('weekly_menu/select_area.html',
                              areas=accessible_areas,
                              title='选择学校')

    # 检查用户是否有权限访问该区域
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('weekly_menu.index'))

    # 获取区域信息
    area = AdministrativeArea.query.get_or_404(area_id)

    # 获取当前日期
    today = date.today()

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')
    else:
        week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()

    # 计算周结束日期（周日，7天）
    week_end = week_start + timedelta(days=6)

    # 检查是否已存在该周的菜单计划
    existing_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=week_start
    ).first()

    # 确定当前周是否可编辑
    # 规则：
    # 1. 只能编辑本周和下周的菜单
    # 2. 如果本周菜单已发布，则只能编辑下周菜单
    # 3. 上周及更早的菜单只能查看，不能编辑

    # 计算本周一和下周一
    this_week_monday = today - timedelta(days=today.weekday())
    next_week_monday = this_week_monday + timedelta(days=7)

    # 检查当前选择的周是否是本周或下周
    is_this_week = week_start == this_week_monday
    is_next_week = week_start == next_week_monday

    # 检查本周菜单是否已发布
    this_week_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=this_week_monday
    ).first()

    this_week_published = this_week_menu and this_week_menu.status == '已发布'

    # 确定是否可编辑
    is_editable = False
    if is_this_week and not this_week_published:
        is_editable = True
    elif is_next_week:
        # 如果本周菜单已发布，则可以编辑下周菜单
        is_editable = True

    # 如果当前菜单已发布，则不可编辑
    if existing_menu and existing_menu.status == '已发布':
        is_editable = False

    # 生成可用周列表（前2周、本周、下周）
    available_weeks = []

    # 前2周
    for i in range(2, 0, -1):
        past_week_monday = this_week_monday - timedelta(days=7*i)
        past_week_end = past_week_monday + timedelta(days=6)  # 周日，7天

        past_week_menu = WeeklyMenu.query.filter_by(
            area_id=area_id,
            week_start=past_week_monday
        ).first()

        past_week_status = past_week_menu.status if past_week_menu else '未创建'

        available_weeks.append({
            'start_date': past_week_monday.strftime('%Y-%m-%d'),
            'end_date': past_week_end.strftime('%Y-%m-%d'),
            'display_text': f'第{past_week_monday.isocalendar()[1]}周 ({past_week_monday.strftime("%m-%d")}至{past_week_end.strftime("%m-%d")})',
            'is_editable': False,  # 过去的周不可编辑
            'is_viewable': True,   # 但可以查看
            'status': past_week_status
        })

    # 本周
    this_week_end = this_week_monday + timedelta(days=6)  # 周日，7天
    available_weeks.append({
        'start_date': this_week_monday.strftime('%Y-%m-%d'),
        'end_date': this_week_end.strftime('%Y-%m-%d'),
        'display_text': f'本周 ({this_week_monday.strftime("%m-%d")}至{this_week_end.strftime("%m-%d")})',
        'is_editable': not this_week_published,  # 如果本周菜单未发布，则可编辑
        'is_viewable': True,
        'status': this_week_menu.status if this_week_menu else '未创建'
    })

    # 下周
    next_week_end = next_week_monday + timedelta(days=6)  # 周日，7天
    next_week_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        week_start=next_week_monday
    ).first()

    available_weeks.append({
        'start_date': next_week_monday.strftime('%Y-%m-%d'),
        'end_date': next_week_end.strftime('%Y-%m-%d'),
        'display_text': f'下周 ({next_week_monday.strftime("%m-%d")}至{next_week_end.strftime("%m-%d")})',
        'is_editable': True,  # 下周始终可编辑
        'is_viewable': True,
        'status': next_week_menu.status if next_week_menu else '未创建'
    })

    # 如果是POST请求，处理表单提交
    if request.method == 'POST':
        # 检查是否有编辑权限
        if not is_editable:
            flash('当前周菜单不可编辑', 'danger')
            return redirect(url_for('weekly_menu.plan_time_aware', area_id=area_id, week_start=week_start_str))

        try:
            # 获取表单数据
            menu_data = json.loads(request.form.get('menu_data', '{}'))

            # 开始事务
            db.session.begin_nested()

            try:
                # 使用原始SQL处理数据，避免ORM的datetime处理问题
                from sqlalchemy import text

                # 如果已存在菜单计划，则更新
                if existing_menu:
                    weekly_menu_id = existing_menu.id

                    # 删除现有的菜单食谱
                    delete_sql = text("""
                    DELETE FROM weekly_menu_recipes
                    WHERE weekly_menu_id = :weekly_menu_id
                    """)

                    db.session.execute(delete_sql, {'weekly_menu_id': weekly_menu_id})
                    current_app.logger.info(f'删除现有菜单食谱: weekly_menu_id={weekly_menu_id}')
                else:
                    # 创建新的周菜单计划
                    insert_sql = text("""
                    INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
                    OUTPUT inserted.id
                    VALUES (:area_id, :week_start, :week_end, :status, :created_by)
                    """)

                    result = db.session.execute(
                        insert_sql,
                        {
                            'area_id': area_id,
                            'week_start': str(week_start),  # 将日期转换为字符串
                            'week_end': str(week_end),      # 将日期转换为字符串
                            'status': '计划中',
                            'created_by': current_user.id
                        }
                    )

                    weekly_menu_id = result.scalar()
                    current_app.logger.info(f'创建新的周菜单计划: id={weekly_menu_id}')

                # 添加菜单食谱
                for day_str, meals in menu_data.items():
                    # 计算星期几（1-7表示周一到周日）
                    day_date = datetime.strptime(day_str, '%Y-%m-%d').date()
                    day_of_week = day_date.weekday() + 1  # 0-6 转为 1-7

                    for meal_type, recipes in meals.items():
                        for recipe_data in recipes:
                            recipe_id = recipe_data.get('id')
                            recipe_name = recipe_data.get('name')

                            # 处理recipe_id
                            final_recipe_id = None
                            if recipe_id and not str(recipe_id).startswith('custom_'):
                                final_recipe_id = recipe_id

                            # 使用原始SQL插入菜单食谱关联
                            insert_recipe_sql = text("""
                            INSERT INTO weekly_menu_recipes
                            (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name)
                            VALUES
                            (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name)
                            """)

                            db.session.execute(
                                insert_recipe_sql,
                                {
                                    'weekly_menu_id': weekly_menu_id,
                                    'day_of_week': day_of_week,
                                    'meal_type': meal_type,
                                    'recipe_id': final_recipe_id,
                                    'recipe_name': recipe_name
                                }
                            )

                # 提交事务
                db.session.commit()
                current_app.logger.info(f'成功保存周菜单计划: id={weekly_menu_id}')

                # 获取保存后的菜单对象，用于重定向
                weekly_menu = WeeklyMenu.query.get(weekly_menu_id)

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f'保存周菜单计划时出错: {str(e)}')
                raise e
            flash('周菜单计划已保存', 'success')
            return redirect(url_for('weekly_menu.view', id=weekly_menu.id))

        except Exception as e:
            db.session.rollback()
            flash(f'保存周菜单计划失败: {str(e)}', 'danger')
            current_app.logger.error(f'保存周菜单计划失败: {str(e)}')

    # 获取所有食谱，按分类组织
    recipes = Recipe.query.filter_by(status=1).all()
    recipes_by_category = {}
    for recipe in recipes:
        category = recipe.category
        if category not in recipes_by_category:
            recipes_by_category[category] = []
        recipes_by_category[category].append(recipe)

    # 生成周日期数据
    week_dates = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    for i in range(7):  # 7天，周一到周日
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        week_dates[date_str] = {
            'date': date_str,
            'weekday': weekdays[i]
        }

    # 如果已存在菜单计划，获取现有数据
    menu_data = {}
    if existing_menu:
        menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=existing_menu.id).all()
        current_app.logger.info(f'找到 {len(menu_recipes)} 条菜谱记录')

        for menu_recipe in menu_recipes:
            # 计算日期字符串
            day_date = week_start + timedelta(days=menu_recipe.day_of_week - 1)
            date_str = day_date.strftime('%Y-%m-%d')

            # 初始化日期和餐次
            if date_str not in menu_data:
                menu_data[date_str] = {}
            if menu_recipe.meal_type not in menu_data[date_str]:
                menu_data[date_str][menu_recipe.meal_type] = []

            # 添加食谱
            recipe_data = {
                'id': menu_recipe.recipe_id or f'custom_{menu_recipe.id}',
                'name': menu_recipe.recipe_name
            }
            menu_data[date_str][menu_recipe.meal_type].append(recipe_data)
            current_app.logger.info(f'加载菜谱: 日期={date_str}, 餐次={menu_recipe.meal_type}, 菜谱={recipe_data}')

        current_app.logger.info(f'加载的菜单数据: {menu_data}')
    else:
        current_app.logger.info('没有现有菜单数据，初始化为空')

    return render_template('weekly_menu/plan_time_aware.html',
                          title=f'{area.name}周菜单计划',
                          area=area,
                          area_id=area_id,
                          week_start=week_start_str,
                          week_end=week_end.strftime('%Y-%m-%d'),
                          week_dates=week_dates,
                          recipes_by_category=recipes_by_category,
                          menu_data=menu_data,
                          existing_menu=existing_menu,
                          is_editable=is_editable,
                          available_weeks=available_weeks)

@weekly_menu_bp.route('/weekly-menu/<int:id>/print')
@login_required
@check_permission('weekly_menu', 'view')
def print_menu(id):
    """打印周菜单计划"""
    # 添加时间戳参数，避免缓存问题
    timestamp = request.args.get('t', '')
    current_app.logger.info(f'打印周菜单计划: id={id}, timestamp={timestamp}')

    # 使用 .options(db.joinedload()) 预加载关联数据，提高性能
    weekly_menu = WeeklyMenu.query.options(
        db.joinedload(WeeklyMenu.area),
        db.joinedload(WeeklyMenu.creator)
    ).get_or_404(id)

    # 检查用户是否有权限打印
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限打印该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu.index'))

    # 获取菜单食谱 - 使用 .all() 确保立即执行查询
    menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=id).all()
    current_app.logger.info(f'获取到 {len(menu_recipes)} 条菜谱记录')

    # 按日期和餐次组织数据
    week_data = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

    # 初始化数据结构
    for i in range((weekly_menu.week_end - weekly_menu.week_start).days + 1):
        current_date = weekly_menu.week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        day_of_week = current_date.weekday()  # 0-6

        week_data[date_str] = {
            'date': date_str,
            'weekday': weekdays[day_of_week],
            'meals': {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }
        }

    # 填充菜单数据
    for recipe in menu_recipes:
        day_date = weekly_menu.week_start + timedelta(days=recipe.day_of_week - 1)
        date_str = day_date.strftime('%Y-%m-%d')

        if date_str in week_data:
            week_data[date_str]['meals'][recipe.meal_type].append({
                'id': recipe.id,
                'recipe_id': recipe.recipe_id,
                'name': recipe.recipe_name
            })

    return render_template('weekly_menu/print.html',
                          weekly_menu=weekly_menu,
                          week_data=week_data)
