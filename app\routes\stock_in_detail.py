from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app.models import (
    StockIn, StockInItem, Supplier, Ingredient, Warehouse, StorageLocation,
    ConsumptionPlan, ConsumptionDetail, StockOut, StockOutItem
)
from app import db
from sqlalchemy import text
from datetime import datetime

stock_in_detail_bp = Blueprint('stock_in_detail', __name__)

@stock_in_detail_bp.route('/stock-in-detail/<int:item_id>')
@login_required
def view(item_id):
    """查看入库食材详情"""
    try:
        # 使用单个SQL查询获取入库食材的详细信息
        stock_in_item_query = text("""
            SELECT
                si_item.id, si_item.stock_in_id, si_item.ingredient_id, si_item.batch_number,
                si_item.quantity, si_item.unit, si_item.production_date, si_item.expiry_date,
                si_item.unit_price, si_item.storage_location_id, si_item.supplier_id,
                si_item.quality_status, si_item.notes,

                si.stock_in_number, si.warehouse_id, si.stock_in_date, si.stock_in_type,
                si.operator_id, si.status, si.notes as stock_in_notes,

                ing.name as ingredient_name, ing.category as ingredient_category,

                sup.name as supplier_name, sup.contact_person, sup.phone, sup.address,

                sl.name as storage_location_name, sl.location_code,

                w.name as warehouse_name,

                u.real_name as operator_name
            FROM
                stock_in_items si_item
            JOIN
                stock_ins si ON si_item.stock_in_id = si.id
            JOIN
                ingredients ing ON si_item.ingredient_id = ing.id
            LEFT JOIN
                suppliers sup ON si_item.supplier_id = sup.id
            LEFT JOIN
                storage_locations sl ON si_item.storage_location_id = sl.id
            JOIN
                warehouses w ON si.warehouse_id = w.id
            LEFT JOIN
                users u ON si.operator_id = u.id
            WHERE
                si_item.id = :item_id
        """)

        stock_in_item_result = db.session.execute(stock_in_item_query, {'item_id': item_id}).fetchone()

        if not stock_in_item_result:
            flash('未找到该入库食材', 'danger')
            return redirect(url_for('stock_in.index'))

        # 检查用户是否有权限查看
        warehouse_id = stock_in_item_result.warehouse_id
        warehouse = Warehouse.query.get(warehouse_id)
        if not warehouse or not current_user.can_access_area_by_id(warehouse.area_id):
            flash('您没有权限查看该入库食材', 'danger')
            return redirect(url_for('stock_in.index'))

        # 获取批次号
        batch_number = stock_in_item_result.batch_number

        # 获取检验检疫证明
        certificates_query = text("""
            SELECT
                doc.id, doc.document_type, doc.file_path, doc.notes,
                doc.created_at as issue_date
            FROM
                stock_in_documents doc
            JOIN
                stock_in_document_items doc_items ON doc.id = doc_items.document_id
            WHERE
                doc_items.item_id = :item_id
            ORDER BY
                doc.created_at DESC
        """)

        inspection_certificates = db.session.execute(certificates_query, {'item_id': item_id}).fetchall()

        # 查询关联的消耗计划
        consumption_plans_query = text("""
            SELECT
                cp.id, cp.consumption_date, cp.meal_type, cp.diners_count,
                a.name as area_name, a.id as area_id
            FROM
                stock_out_items soi
            JOIN
                stock_outs so ON soi.stock_out_id = so.id
            JOIN
                consumption_plans cp ON so.consumption_plan_id = cp.id
            JOIN
                menu_plans mp ON cp.menu_plan_id = mp.id
            JOIN
                administrative_areas a ON mp.area_id = a.id
            WHERE
                soi.batch_number = :batch_number
            ORDER BY
                cp.consumption_date DESC
        """)

        consumption_plans = db.session.execute(consumption_plans_query, {'batch_number': batch_number}).fetchall()

        # 记录日志
        current_app.logger.info(f"查看入库食材详情: ID={item_id}, 批次号={batch_number}")

        return render_template(
            'stock_in_detail/view.html',
            item=stock_in_item_result,
            batch_number=batch_number,
            inspection_certificates=inspection_certificates,
            consumption_plans=consumption_plans
        )

    except Exception as e:
        current_app.logger.error(f"查看入库食材详情失败: {str(e)}")
        flash(f'查看入库食材详情失败: {str(e)}', 'danger')
        return redirect(url_for('stock_in.index'))
