/**
 * JavaScript本地化文件
 * 用于存储所有的本地化字符串
 */

// 创建全局i18n对象
window.i18n = {
    // 通用
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    view: '查看',
    close: '关闭',
    yes: '是',
    no: '否',
    ok: '确定',
    
    // 图片上传组件
    dropZoneText: '拖拽图片到此处上传，或点击选择图片',
    uploadButtonText: '选择图片',
    maxFilesError: '最多只能上传{0}张图片',
    unsupportedFileType: '不支持的文件类型: {0}',
    fileTooLarge: '文件过大，最大允许{0}MB',
    uploadingCannotDelete: '正在上传，无法删除',
    waitForUpload: '请等待上传完成后再评分',
    uploadSuccess: '{0} 上传成功',
    parseResponseError: '解析响应失败: {0}',
    uploadFailed: '上传失败: {0}',
    networkError: '上传失败: 网络错误',
    loadExistingImagesFailed: '加载已有图片失败: {0}',
    confirmDelete: '确定要删除这张照片吗？',
    photoDeleted: '照片已删除',
    deleteFailed: '删除失败: {0}',
    unknownError: '未知错误',
    ratingUpdated: '评分已更新',
    updateRatingFailed: '更新评分失败: {0}',
    
    // 通知系统
    getNotificationsFailed: '获取通知失败: {0}',
    noNotifications: '暂无通知',
    urgent: '紧急',
    important: '重要',
    
    // 二维码生成
    qrCodeGenerateSuccess: '二维码生成成功',
    qrCodeGenerateFailed: '生成二维码失败',
    
    // 日期选择器
    dateFormat: 'yyyy-mm-dd',
    today: '今天',
    clear: '清除',
    
    // 日期范围选择器
    dateRangeFormat: 'YYYY-MM-DD',
    dateRangeSeparator: ' 至 ',
    applyLabel: '确定',
    cancelLabel: '取消',
    fromLabel: '从',
    toLabel: '到',
    customRangeLabel: '自定义',
    weekLabel: '周',
    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    firstDay: 1
};

// 提供一个格式化字符串的辅助函数
window.i18n.format = function(str, ...args) {
    return str.replace(/{(\d+)}/g, function(match, number) {
        return typeof args[number] !== 'undefined' ? args[number] : match;
    });
};
