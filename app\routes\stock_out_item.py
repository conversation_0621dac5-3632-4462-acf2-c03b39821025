from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from app.models import StockOutItem, StockOut, Inventory, Ingredient, StockInItem, StockIn, AdministrativeArea
from app import db
from sqlalchemy.orm import joinedload

stock_out_item_bp = Blueprint('stock_out_item', __name__)

@stock_out_item_bp.route('/stock-out-item/detail/<int:item_id>')
@login_required
def detail(item_id):
    """查看出库单明细详情"""
    item = StockOutItem.query.get_or_404(item_id)

    # Fetch related objects
    # Use joinedload for efficient fetching
    item = StockOutItem.query.options(
        joinedload(StockOutItem.stock_out).joinedload(StockOut.warehouse).joinedload(AdministrativeArea),
        joinedload(StockOutItem.inventory).joinedload(Inventory.storage_location),
        joinedload(StockOutItem.ingredient)
    ).get_or_404(item_id)

    stock_out = item.stock_out
    inventory = item.inventory
    ingredient = item.ingredient

    # Check user permission based on the area of the related stock out's warehouse
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限查看该出库单明细', 'danger')
        return redirect(url_for('stock_out.index')) # Redirect to stock out list or similar

    # Attempt to trace back to the original StockInItem and StockIn
    # Find the StockInItem that created this specific Inventory batch record
    original_stock_in_item = None
    original_stock_in = None
    if inventory:
         # Find StockInItems for the same ingredient and batch number
         stock_in_items_for_batch = StockInItem.query.filter_by(
             ingredient_id=inventory.ingredient_id,
             batch_number=inventory.batch_number
         ).all()

         # In a real system, you might need more logic to find the EXACT StockInItem
         # that created this specific *instance* of Inventory, maybe based on dates or a direct link.
         # For simplicity here, we'll just take the first StockInItem found with matching ingredient and batch number.
         if stock_in_items_for_batch:
             original_stock_in_item = stock_in_items_for_batch[0]
             if original_stock_in_item.stock_in:
                 original_stock_in = original_stock_in_item.stock_in


    return render_template(
        'stock_out_item/detail.html',
        item=item,
        stock_out=stock_out,
        inventory=inventory,
        ingredient=ingredient,
        original_stock_in=original_stock_in,
        original_stock_in_item=original_stock_in_item
    ) 