{% extends 'base.html' %}

{% block title %}打印检查记录{% endblock %}

{% block styles %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        body {
            padding: 0;
            margin: 0;
        }
        .container-fluid {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        .page-break {
            page-break-after: always;
        }
    }
    
    .print-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .print-header h1 {
        font-size: 24px;
        margin-bottom: 5px;
    }
    
    .print-header p {
        font-size: 14px;
        color: #666;
    }
    
    .print-section {
        margin-bottom: 30px;
    }
    
    .print-section h2 {
        font-size: 18px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
        margin-bottom: 15px;
    }
    
    .photo-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .photo-item {
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 5px;
    }
    
    .photo-item img {
        width: 100%;
        height: auto;
        max-height: 200px;
        object-fit: contain;
    }
    
    .photo-info {
        margin-top: 10px;
        font-size: 12px;
    }
    
    .photo-info p {
        margin: 3px 0;
    }
    
    .rating-stars {
        color: #f6c23e;
    }
    
    .signature-section {
        margin-top: 50px;
        display: flex;
        justify-content: space-between;
    }
    
    .signature-box {
        border-top: 1px solid #000;
        width: 200px;
        padding-top: 5px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">打印检查记录</h1>
        <div>
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print mr-1"></i> 打印
            </button>
            <a href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i> 返回
            </a>
        </div>
    </div>

    <div class="print-header">
        <h1>{{ school.name }} - 食堂检查记录</h1>
        <p>日期：{{ log.log_date }} &nbsp;&nbsp; 打印时间：{{ now|format_datetime }}</p>
    </div>

    {% if morning_photos %}
    <div class="print-section">
        <h2><i class="fas fa-sun mr-1"></i> 早晨检查记录</h2>
        <div class="photo-grid">
            {% for photo in morning_photos %}
            <div class="photo-item">
                <img src="{{ photo.file_path }}" alt="早晨检查照片">
                <div class="photo-info">
                    <p><strong>上传时间：</strong> {{ photo.upload_time|format_datetime }}</p>
                    <p><strong>评分：</strong> 
                        {% if photo.rating %}
                        <span class="rating-stars">
                            {% for i in range(photo.rating) %}★{% endfor %}
                            {% for i in range(5 - photo.rating) %}☆{% endfor %}
                        </span>
                        {% else %}
                        未评分
                        {% endif %}
                    </p>
                    {% if photo.description %}
                    <p><strong>备注：</strong> {{ photo.description }}</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if noon_photos %}
    <div class="print-section {% if morning_photos %}page-break{% endif %}">
        <h2><i class="fas fa-cloud-sun mr-1"></i> 中午检查记录</h2>
        <div class="photo-grid">
            {% for photo in noon_photos %}
            <div class="photo-item">
                <img src="{{ photo.file_path }}" alt="中午检查照片">
                <div class="photo-info">
                    <p><strong>上传时间：</strong> {{ photo.upload_time|format_datetime }}</p>
                    <p><strong>评分：</strong> 
                        {% if photo.rating %}
                        <span class="rating-stars">
                            {% for i in range(photo.rating) %}★{% endfor %}
                            {% for i in range(5 - photo.rating) %}☆{% endfor %}
                        </span>
                        {% else %}
                        未评分
                        {% endif %}
                    </p>
                    {% if photo.description %}
                    <p><strong>备注：</strong> {{ photo.description }}</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if evening_photos %}
    <div class="print-section {% if noon_photos or morning_photos %}page-break{% endif %}">
        <h2><i class="fas fa-moon mr-1"></i> 晚上检查记录</h2>
        <div class="photo-grid">
            {% for photo in evening_photos %}
            <div class="photo-item">
                <img src="{{ photo.file_path }}" alt="晚上检查照片">
                <div class="photo-info">
                    <p><strong>上传时间：</strong> {{ photo.upload_time|format_datetime }}</p>
                    <p><strong>评分：</strong> 
                        {% if photo.rating %}
                        <span class="rating-stars">
                            {% for i in range(photo.rating) %}★{% endfor %}
                            {% for i in range(5 - photo.rating) %}☆{% endfor %}
                        </span>
                        {% else %}
                        未评分
                        {% endif %}
                    </p>
                    {% if photo.description %}
                    <p><strong>备注：</strong> {{ photo.description }}</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="signature-section">
        <div class="signature-box">
            检查人签名
        </div>
        <div class="signature-box">
            负责人签名
        </div>
    </div>
</div>
{% endblock %}
