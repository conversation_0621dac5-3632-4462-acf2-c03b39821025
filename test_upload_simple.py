#!/usr/bin/env python3
"""
简化的照片上传功能测试
"""

import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_files_exist():
    """测试关键文件是否存在"""
    print("=== 测试关键文件 ===")
    
    files = {
        'app/templates/daily_management/public_upload_inspection_photo.html': '上传页面模板',
        'app/routes/daily_management/public_api.py': '公开API',
        'app/routes/daily_management/inspection_qrcode.py': '二维码生成'
    }
    
    all_exist = True
    for file_path, description in files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✓ {description}: {file_path} ({size} 字节)")
        else:
            print(f"✗ {description}: {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def test_template_content():
    """测试模板内容"""
    print("\n=== 测试模板内容 ===")
    
    template_file = 'app/templates/daily_management/public_upload_inspection_photo.html'
    
    if not os.path.exists(template_file):
        print("✗ 模板文件不存在")
        return False
    
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修复
    checks = [
        ('capture="environment"', False, '移除强制摄像头属性'),
        ('accept="image/*" multiple', True, '支持多文件选择'),
        ('点击选择照片或拍照', True, '更新提示文字'),
        ('支持相册选择、拍照、多张照片、拖拽上传', True, '完整功能说明'),
        ('file.size > 10 * 1024 * 1024', True, '10MB文件大小限制'),
        ('网络连接失败，请检查网络后重试', True, '改进错误提示')
    ]
    
    all_passed = True
    for check_text, should_exist, description in checks:
        exists = check_text in content
        if exists == should_exist:
            status = "✓" if should_exist else "✓ (已移除)"
            print(f"  {status} {description}")
        else:
            status = "✗" if should_exist else "✗ (仍存在)"
            print(f"  {status} {description}")
            all_passed = False
    
    return all_passed

def test_api_content():
    """测试API内容"""
    print("\n=== 测试API内容 ===")
    
    api_file = 'app/routes/daily_management/public_api.py'
    
    if not os.path.exists(api_file):
        print("✗ API文件不存在")
        return False
    
    with open(api_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修复
    checks = [
        ('photo_result = handle_photo_upload', True, '修复照片处理返回值'),
        ('img.convert(\'RGB\')', True, '图片格式转换'),
        ('format=\'JPEG\', quality=85', True, '统一JPEG格式'),
        ('db.session.rollback()', True, '添加事务回滚'),
        ('.jpg', True, '统一文件扩展名')
    ]
    
    all_passed = True
    for check_text, should_exist, description in checks:
        exists = check_text in content
        if exists == should_exist:
            print(f"  ✓ {description}")
        else:
            print(f"  ✗ {description}")
            all_passed = False
    
    return all_passed

def test_directories():
    """测试目录结构"""
    print("\n=== 测试目录结构 ===")
    
    directories = [
        'app/static/uploads',
        'app/static/uploads/daily_management',
        'app/static/uploads/daily_management/morning',
        'app/static/uploads/daily_management/noon',
        'app/static/uploads/daily_management/evening'
    ]
    
    all_exist = True
    for directory in directories:
        if os.path.exists(directory):
            print(f"✓ 目录存在: {directory}")
        else:
            print(f"✗ 目录不存在: {directory}")
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"  ✓ 已创建: {directory}")
            except Exception as e:
                print(f"  ✗ 创建失败: {str(e)}")
                all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("照片上传功能修复验证 (简化版)")
    print("=" * 50)
    
    tests = [
        test_files_exist,
        test_template_content,
        test_api_content,
        test_directories
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 照片上传功能修复验证通过！")
        print("\n主要修复内容：")
        print("1. ✅ 移除了 capture=\"environment\" 属性")
        print("2. ✅ 支持相册选择和拍照")
        print("3. ✅ 改进了文件验证逻辑")
        print("4. ✅ 统一了图片处理格式")
        print("5. ✅ 增强了错误处理")
        print("6. ✅ 创建了必要的上传目录")
        print("\n现在用户可以：")
        print("📱 使用手机拍照")
        print("🖼️ 从相册选择照片")
        print("🖱️ 拖拽上传文件")
        print("📁 一次选择多张照片")
        print("\n请在手机上测试扫码上传功能！")
    else:
        print("\n❌ 部分测试未通过，请检查修复")

if __name__ == "__main__":
    main()
