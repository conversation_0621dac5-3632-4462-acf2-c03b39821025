-- 为photos表添加comment字段
-- 用于存储照片的文字评价和改善建议

USE [StudentsCMSSP]
GO

-- 检查comment字段是否已存在
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'photos' AND COLUMN_NAME = 'comment'
)
BEGIN
    -- 添加comment字段
    ALTER TABLE photos 
    ADD comment NVARCHAR(1000) NULL;
    
    PRINT '成功添加comment字段到photos表';
END
ELSE
BEGIN
    PRINT 'comment字段已存在于photos表中';
END
GO

-- 验证字段添加结果
SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'photos' AND COLUMN_NAME = 'comment';
GO
