"""
食堂日常管理服务

提供食堂日常管理所需的数据服务，包括日志管理、检查记录管理、陪餐记录管理等。
"""

from datetime import datetime, date, timedelta, time
from sqlalchemy import func, and_, or_, extract, text
from flask_login import current_user
from flask import current_app
from app import db
from app.models_daily_management import (
    DailyLog, InspectionRecord, DiningCompanion,
    CanteenTrainingRecord, SpecialEvent, Issue, Photo
)
from app.models import AdministrativeArea, WeeklyMenu, WeeklyMenuRecipe, MenuPlan, MenuRecipe
from app.utils.log_activity import log_activity
from app.utils.datetime_helper import get_current_time, sql_datetime_format
from app.utils.error_handler import log_error


class DailyLogService:
    """日志服务类"""

    @staticmethod
    def get_menu_text_from_recipes(recipes):
        """
        将菜谱列表转换为文本格式

        Args:
            recipes: 菜谱列表

        Returns:
            str: 菜谱文本，如"红烧肉、清蒸鱼、炒青菜"
        """
        if not recipes:
            return None
        return "、".join([recipe.get('name', '未知菜品') for recipe in recipes])

    @staticmethod
    def get_menu_data_for_date(log_date, area_id):
        """
        获取指定日期的菜单数据

        Args:
            log_date: 日期
            area_id: 区域ID

        Returns:
            dict: 包含早餐、午餐、晚餐菜单数据的字典
        """
        try:
            # 初始化菜单数据
            menu_data = {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }

            # 尝试从日菜单获取数据
            for meal_type in menu_data.keys():
                menu_plan = MenuPlan.query.filter(
                    MenuPlan.plan_date == log_date,
                    MenuPlan.meal_type == meal_type,
                    MenuPlan.area_id == area_id,
                    MenuPlan.status.in_(['已发布', '已执行'])
                ).first()

                if menu_plan:
                    recipes = []
                    for menu_recipe in MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all():
                        recipe_name = menu_recipe.recipe.name if menu_recipe.recipe else '未知菜品'
                        recipes.append({'name': recipe_name})

                    menu_data[meal_type] = recipes

            # 对于没有日菜单的餐次，尝试从周菜单获取
            for meal_type in menu_data.keys():
                if not menu_data[meal_type]:
                    # 获取当前是周几（0-6，0表示周一）
                    weekday = log_date.weekday()

                    # 查找包含当前日期的周菜单
                    weekly_menu = WeeklyMenu.query.filter(
                        WeeklyMenu.week_start <= log_date,
                        WeeklyMenu.week_end >= log_date,
                        WeeklyMenu.area_id == area_id,
                        WeeklyMenu.status == '已发布'
                    ).first()

                    if weekly_menu:
                        # 查找对应日期和餐次的菜谱
                        weekly_recipes = WeeklyMenuRecipe.query.filter(
                            WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                            WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                            WeeklyMenuRecipe.meal_type == meal_type
                        ).all()

                        if weekly_recipes:
                            recipes = []
                            for weekly_recipe in weekly_recipes:
                                recipes.append({'name': weekly_recipe.recipe_name})

                            menu_data[meal_type] = recipes

            return menu_data

        except Exception as e:
            current_app.logger.error(f"获取菜单数据失败: {str(e)}")
            return {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }

    @staticmethod
    def get_daily_logs(start_date=None, end_date=None, area_id=None):
        """
        获取日志列表

        Args:
            start_date: 开始日期字符串，格式为YYYY-MM-DD
            end_date: 结束日期字符串，格式为YYYY-MM-DD
            area_id: 区域ID

        Returns:
            list: 日志列表
        """
        # 解析日期
        if start_date:
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                start = date.today() - timedelta(days=30)
        else:
            start = date.today() - timedelta(days=30)

        if end_date:
            try:
                end = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                end = date.today()
        else:
            end = date.today()

        # 获取当前用户可访问的区域
        if area_id:
            area_ids = [area_id]
        else:
            accessible_areas = current_user.get_accessible_areas()
            area_ids = [area.id for area in accessible_areas]

        # 查询日志
        query = DailyLog.query.filter(
            DailyLog.log_date >= start,
            DailyLog.log_date <= end
        )

        if area_ids:
            query = query.filter(DailyLog.area_id.in_(area_ids))

        return query.order_by(DailyLog.log_date.desc()).all()

    @staticmethod
    def get_daily_log_by_id(log_id):
        """
        根据ID获取日志

        Args:
            log_id: 日志ID

        Returns:
            DailyLog: 日志对象
        """
        return DailyLog.query.get(log_id)

    @staticmethod
    def get_daily_log_by_date(log_date, area_id=None):
        """
        根据日期获取日志

        Args:
            log_date: 日期
            area_id: 区域ID

        Returns:
            DailyLog: 日志对象
        """
        try:
            # 使用安全的日期时间处理函数
            from app.utils.datetime_helper import safe_strftime
            log_date_str = safe_strftime(log_date, '%Y-%m-%d')

            if not log_date_str:
                current_app.logger.error(f"无效的日期格式: {log_date}")
                return None

            # 使用原始 SQL 查询，避免日期类型问题
            if area_id:
                sql = text("""
                    SELECT id FROM daily_logs
                    WHERE CONVERT(VARCHAR(10), log_date, 120) = :log_date
                    AND area_id = :area_id
                """)
                params = {'log_date': log_date_str, 'area_id': area_id}
            else:
                sql = text("""
                    SELECT id FROM daily_logs
                    WHERE CONVERT(VARCHAR(10), log_date, 120) = :log_date
                """)
                params = {'log_date': log_date_str}

            result = db.session.execute(sql, params).fetchone()

            if result:
                # 使用 ID 查询，避免日期类型问题
                return DailyLog.query.get(result[0])
            return None

        except Exception as e:
            # 记录错误并返回 None
            current_app.logger.error(f"获取日志失败: {str(e)}")
            return None

    @staticmethod
    def create_daily_log(data):
        """
        创建日志

        Args:
            data: 日志数据

        Returns:
            DailyLog: 创建的日志对象，如果已存在则返回已存在的对象
        """
        try:
            # 检查是否已存在
            existing_log = DailyLogService.get_daily_log_by_date(
                data.get('log_date'),
                data.get('area_id')
            )

            if existing_log:
                return existing_log

            # 使用安全的日期时间处理函数
            from app.utils.datetime_helper import safe_strftime
            log_date_str = safe_strftime(data.get('log_date'), '%Y-%m-%d')

            if not log_date_str:
                current_app.logger.error(f"无效的日期格式: {data.get('log_date')}")
                return None

            # 构建 SQL 语句，不包含时间戳字段
            sql = text("""
                INSERT INTO daily_logs
                (log_date, weather, manager, student_count, teacher_count,
                 other_count, breakfast_menu, lunch_menu, dinner_menu,
                 food_waste, special_events, operation_summary, area_id, created_by)
                OUTPUT inserted.id
                VALUES
                (CONVERT(DATETIME2(1), :log_date, 120), :weather, :manager, :student_count, :teacher_count,
                 :other_count, :breakfast_menu, :lunch_menu, :dinner_menu,
                 :food_waste, :special_events, :operation_summary, :area_id, :created_by)
            """)

            # 准备参数
            params = {
                'log_date': log_date_str,
                'weather': data.get('weather'),
                'manager': data.get('manager'),
                'student_count': data.get('student_count', 0),
                'teacher_count': data.get('teacher_count', 0),
                'other_count': data.get('other_count', 0),
                'food_waste': data.get('food_waste'),
                'special_events': data.get('special_events'),
                'operation_summary': data.get('operation_summary'),
                'area_id': data.get('area_id'),
                'created_by': data.get('created_by')
            }

            # 如果没有提供菜单数据，尝试从菜单计划或周菜单获取
            if not any([data.get('breakfast_menu'), data.get('lunch_menu'), data.get('dinner_menu')]):
                try:
                    # 解析日期
                    if isinstance(data.get('log_date'), str):
                        log_date = datetime.strptime(data.get('log_date'), '%Y-%m-%d').date()
                    else:
                        log_date = data.get('log_date')

                    # 获取菜单数据
                    menu_data = DailyLogService.get_menu_data_for_date(log_date, data.get('area_id'))

                    # 填充菜单字段
                    params['breakfast_menu'] = DailyLogService.get_menu_text_from_recipes(menu_data.get('早餐', []))
                    params['lunch_menu'] = DailyLogService.get_menu_text_from_recipes(menu_data.get('午餐', []))
                    params['dinner_menu'] = DailyLogService.get_menu_text_from_recipes(menu_data.get('晚餐', []))

                    current_app.logger.info(f"自动填充菜单数据: 早餐={params['breakfast_menu']}, 午餐={params['lunch_menu']}, 晚餐={params['dinner_menu']}")
                except Exception as e:
                    current_app.logger.error(f"自动填充菜单数据失败: {str(e)}")
            else:
                # 使用提供的菜单数据
                params['breakfast_menu'] = data.get('breakfast_menu')
                params['lunch_menu'] = data.get('lunch_menu')
                params['dinner_menu'] = data.get('dinner_menu')

            # 执行 SQL
            result = db.session.execute(sql, params)
            record_id = result.fetchone()[0]
            db.session.commit()

            # 获取创建的记录
            log = DailyLog.query.get(record_id)

            # 记录活动
            if log:
                try:
                    log_activity('创建了工作日志', f'日期: {log.log_date}', 'daily_log', log.id)
                except Exception as e:
                    print(f"记录活动失败: {str(e)}")

            return log

        except Exception as e:
            db.session.rollback()
            # 如果是唯一约束冲突，尝试再次获取已存在的记录
            if 'UNIQUE KEY' in str(e) and 'daily_logs' in str(e):
                existing_log = DailyLogService.get_daily_log_by_date(
                    data.get('log_date'),
                    data.get('area_id')
                )
                if existing_log:
                    return existing_log
            # 其他错误则抛出
            raise

    @staticmethod
    def update_daily_log(log_id, data):
        """
        更新日志

        Args:
            log_id: 日志ID
            data: 更新的数据

        Returns:
            DailyLog: 更新后的日志对象
        """
        try:
            # 检查记录是否存在
            log = DailyLog.query.get(log_id)
            if not log:
                return None

            # 构建 SQL 语句，不包含时间戳字段
            sql = text("""
                UPDATE daily_logs
                SET weather = :weather,
                    manager = :manager,
                    student_count = :student_count,
                    teacher_count = :teacher_count,
                    other_count = :other_count,
                    breakfast_menu = :breakfast_menu,
                    lunch_menu = :lunch_menu,
                    dinner_menu = :dinner_menu,
                    food_waste = :food_waste,
                    special_events = :special_events,
                    operation_summary = :operation_summary
                WHERE id = :id
            """)

            # 准备参数
            params = {
                'id': log_id,
                'weather': data.get('weather', log.weather),
                'manager': data.get('manager', log.manager),
                'student_count': data.get('student_count', log.student_count),
                'teacher_count': data.get('teacher_count', log.teacher_count),
                'other_count': data.get('other_count', log.other_count),
                'food_waste': data.get('food_waste', log.food_waste),
                'special_events': data.get('special_events', log.special_events),
                'operation_summary': data.get('operation_summary', log.operation_summary)
            }

            # 处理菜单字段
            # 如果明确提供了菜单数据，使用提供的数据
            if 'breakfast_menu' in data:
                params['breakfast_menu'] = data.get('breakfast_menu')
            else:
                params['breakfast_menu'] = log.breakfast_menu

            if 'lunch_menu' in data:
                params['lunch_menu'] = data.get('lunch_menu')
            else:
                params['lunch_menu'] = log.lunch_menu

            if 'dinner_menu' in data:
                params['dinner_menu'] = data.get('dinner_menu')
            else:
                params['dinner_menu'] = log.dinner_menu

            # 如果请求了自动填充菜单数据
            if data.get('auto_fill_menu', False):
                try:
                    # 获取菜单数据
                    menu_data = DailyLogService.get_menu_data_for_date(log.log_date, log.area_id)

                    # 填充菜单字段
                    breakfast_menu = DailyLogService.get_menu_text_from_recipes(menu_data.get('早餐', []))
                    lunch_menu = DailyLogService.get_menu_text_from_recipes(menu_data.get('午餐', []))
                    dinner_menu = DailyLogService.get_menu_text_from_recipes(menu_data.get('晚餐', []))

                    # 只有在有数据时才更新
                    if breakfast_menu:
                        params['breakfast_menu'] = breakfast_menu
                    if lunch_menu:
                        params['lunch_menu'] = lunch_menu
                    if dinner_menu:
                        params['dinner_menu'] = dinner_menu

                    current_app.logger.info(f"自动填充菜单数据: 早餐={params['breakfast_menu']}, 午餐={params['lunch_menu']}, 晚餐={params['dinner_menu']}")
                except Exception as e:
                    current_app.logger.error(f"自动填充菜单数据失败: {str(e)}")

            # 执行 SQL
            db.session.execute(sql, params)
            db.session.commit()

            # 获取更新后的记录
            log = DailyLog.query.get(log_id)

            # 记录活动
            if log:
                try:
                    log_activity('更新了工作日志', f'日期: {log.log_date}', 'daily_log', log.id)
                except Exception as e:
                    print(f"记录活动失败: {str(e)}")

            return log

        except Exception as e:
            db.session.rollback()
            raise

    @staticmethod
    def delete_daily_log(log_id):
        """
        删除日志

        Args:
            log_id: 日志ID

        Returns:
            bool: 是否删除成功
        """
        log = DailyLog.query.get(log_id)

        if not log:
            return False

        # 记录活动
        log_date = log.log_date

        # 删除关联的检查记录
        inspections = InspectionRecord.query.filter_by(daily_log_id=log_id).all()
        for inspection in inspections:
            # 删除关联的照片
            photos = Photo.query.filter_by(reference_type='inspection', reference_id=inspection.id).all()
            for photo in photos:
                db.session.delete(photo)
            db.session.delete(inspection)

        # 删除关联的陪餐记录
        companions = DiningCompanion.query.filter_by(daily_log_id=log_id).all()
        for companion in companions:
            # 删除关联的照片
            photos = Photo.query.filter_by(reference_type='companion', reference_id=companion.id).all()
            for photo in photos:
                db.session.delete(photo)
            db.session.delete(companion)

        # 删除关联的培训记录
        trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log_id).all()
        for training in trainings:
            # 删除关联的照片
            photos = Photo.query.filter_by(reference_type='training', reference_id=training.id).all()
            for photo in photos:
                db.session.delete(photo)
            db.session.delete(training)

        # 删除关联的特殊事件
        events = SpecialEvent.query.filter_by(daily_log_id=log_id).all()
        for event in events:
            # 删除关联的照片
            photos = Photo.query.filter_by(reference_type='event', reference_id=event.id).all()
            for photo in photos:
                db.session.delete(photo)
            db.session.delete(event)

        # 删除日志
        db.session.delete(log)
        db.session.commit()

        # 记录活动
        try:
            log_activity('删除了工作日志', f'日期: {log_date}', 'daily_log', log_id)
        except Exception as e:
            print(f"记录活动失败: {str(e)}")

        return True

    @staticmethod
    def get_daily_statistics(start_date=None, end_date=None, area_id=None):
        """
        获取日志统计数据

        Args:
            start_date: 开始日期字符串，格式为YYYY-MM-DD
            end_date: 结束日期字符串，格式为YYYY-MM-DD
            area_id: 区域ID

        Returns:
            dict: 统计数据
        """
        try:
            # 处理开始日期
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d').replace(microsecond=0)
                except ValueError:
                    start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=30)
            else:
                start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=30)

            # 处理结束日期
            if end_date:
                try:
                    end = datetime.strptime(end_date, '%Y-%m-%d').replace(microsecond=0)
                    # 设置为当天的最后一刻
                    end = end.replace(hour=23, minute=59, second=59, microsecond=0)
                except ValueError:
                    end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=0)
            else:
                end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=0)

            # 获取当前用户可访问的区域
            if area_id:
                area_ids = [area_id]
            else:
                accessible_areas = current_user.get_accessible_areas()
                area_ids = [area.id for area in accessible_areas]

            # 使用原始 SQL 查询统计数据
            sql = text("""
                SELECT
                    CONVERT(VARCHAR(10), log_date, 120) as date,
                    SUM(student_count) as student_count,
                    SUM(teacher_count) as teacher_count,
                    SUM(other_count) as other_count
                FROM daily_logs
                WHERE log_date >= :start_date
                AND log_date <= :end_date
                AND area_id IN :area_ids
                GROUP BY CONVERT(VARCHAR(10), log_date, 120)
                ORDER BY date DESC
            """)

            result = db.session.execute(
                sql,
                {
                    'start_date': start,
                    'end_date': end,
                    'area_ids': tuple(area_ids) if area_ids else (0,)
                }
            )

            # 处理统计数据
            daily_counts = {}
            total_student_count = 0
            total_teacher_count = 0
            total_other_count = 0

            for row in result:
                date_str = row.date
                student_count = row.student_count or 0
                teacher_count = row.teacher_count or 0
                other_count = row.other_count or 0

                daily_counts[date_str] = {
                    'student': student_count,
                    'teacher': teacher_count,
                    'other': other_count,
                    'total': student_count + teacher_count + other_count
                }

                total_student_count += student_count
                total_teacher_count += teacher_count
                total_other_count += other_count

            # 构建统计数据
            statistics = {
                'start_date': start.strftime('%Y-%m-%d'),
                'end_date': end.strftime('%Y-%m-%d'),
                'area_id': area_id,
                'diners': {
                    'student_count': total_student_count,
                    'teacher_count': total_teacher_count,
                    'other_count': total_other_count,
                    'total_count': total_student_count + total_teacher_count + total_other_count
                },
                'daily_counts': daily_counts
            }

            return statistics

        except Exception as e:
            print(f"获取统计数据失败: {str(e)}")
            return None


class InspectionService:
    """检查记录服务类"""

    @staticmethod
    def get_inspections_by_daily_log(daily_log_id):
        """
        获取日志的检查记录

        Args:
            daily_log_id: 日志ID

        Returns:
            list: 检查记录列表
        """
        return InspectionRecord.query.filter_by(daily_log_id=daily_log_id).all()

    @staticmethod
    def get_inspection_by_id(inspection_id):
        """
        根据ID获取检查记录

        Args:
            inspection_id: 检查记录ID

        Returns:
            InspectionRecord: 检查记录对象
        """
        return InspectionRecord.query.get(inspection_id)

    @staticmethod
    def create_inspection(data):
        """
        创建检查记录

        Args:
            data: 检查记录数据

        Returns:
            InspectionRecord: 创建的检查记录对象
        """
        try:
            # 验证必要参数
            required_fields = ['daily_log_id', 'inspection_type', 'inspection_item']
            missing_fields = [field for field in required_fields if field not in data or not data[field]]
            if missing_fields:
                error_msg = f"缺少必要参数: {', '.join(missing_fields)}"
                current_app.logger.warning(error_msg)
                raise ValueError(error_msg)

            # 验证字段类型
            if data.get('inspection_type') not in ['morning', 'noon', 'evening']:
                error_msg = f"检查类型无效: {data.get('inspection_type')}"
                current_app.logger.warning(error_msg)
                raise ValueError(error_msg)

            # 处理日期时间参数，确保去除微秒部分
            inspection_time = data.get('inspection_time', get_current_time())
            if isinstance(inspection_time, datetime):
                inspection_time = inspection_time.replace(microsecond=0)
                inspection_time_str = sql_datetime_format(inspection_time)
            else:
                try:
                    # 尝试解析字符串日期时间
                    inspection_time = datetime.strptime(str(inspection_time), '%Y-%m-%d %H:%M:%S').replace(microsecond=0)
                    inspection_time_str = sql_datetime_format(inspection_time)
                except ValueError:
                    # 如果解析失败，使用当前时间
                    inspection_time = get_current_time()
                    inspection_time_str = sql_datetime_format(inspection_time)
                    current_app.logger.warning(f"无效的日期时间格式: {data.get('inspection_time')}, 使用当前时间")

            # 构建 SQL 语句，不包含时间戳字段
            sql = text("""
                INSERT INTO inspection_records
                (daily_log_id, inspection_type, inspection_item, inspector_id,
                 inspection_time, status, description, photo_path)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :inspection_type, :inspection_item, :inspector_id,
                 CONVERT(DATETIME2(1), :inspection_time, 120), :status, :description, :photo_path)
            """)

            # 准备参数
            params = {
                'daily_log_id': data.get('daily_log_id'),
                'inspection_type': data.get('inspection_type'),
                'inspection_item': data.get('inspection_item'),
                'inspector_id': data.get('inspector_id', current_user.id),
                'inspection_time': inspection_time_str,
                'status': data.get('status', 'normal'),
                'description': data.get('description', ''),
                'photo_path': data.get('photo_path', '')
            }

            # 执行 SQL
            result = db.session.execute(sql, params)
            record_id = result.fetchone()[0]
            db.session.commit()

            # 获取创建的记录
            inspection = InspectionRecord.query.get(record_id)

            # 记录活动
            if inspection:
                try:
                    log_activity('创建了检查记录',
                               f'类型: {inspection.inspection_type}, 项目: {inspection.inspection_item}',
                               'inspection',
                               inspection.id)
                except Exception as e:
                    log_error(e, f"记录活动失败: 检查记录ID={inspection.id}")

            return inspection

        except Exception as e:
            db.session.rollback()
            log_error(e, f"创建检查记录失败: {data}")
            raise

    @staticmethod
    def update_inspection(inspection_id, data):
        """
        更新检查记录

        Args:
            inspection_id: 检查记录ID
            data: 检查记录数据

        Returns:
            InspectionRecord: 更新后的检查记录对象
        """
        try:
            inspection = InspectionRecord.query.get(inspection_id)

            if not inspection:
                current_app.logger.warning(f"更新检查记录失败: 记录不存在 ID={inspection_id}")
                return None

            # 验证字段类型
            if 'inspection_type' in data and data['inspection_type'] not in ['morning', 'noon', 'evening']:
                error_msg = f"检查类型无效: {data.get('inspection_type')}"
                current_app.logger.warning(error_msg)
                raise ValueError(error_msg)

            # 处理日期时间参数
            if 'inspection_time' in data:
                inspection_time = data.get('inspection_time')
                if isinstance(inspection_time, datetime):
                    inspection_time = inspection_time.replace(microsecond=0)
                else:
                    try:
                        inspection_time = datetime.strptime(str(inspection_time), '%Y-%m-%d %H:%M:%S').replace(microsecond=0)
                    except ValueError:
                        inspection_time = inspection.inspection_time
                        current_app.logger.warning(f"无效的日期时间格式: {data.get('inspection_time')}, 保持原值")
            else:
                inspection_time = inspection.inspection_time

            # 使用原始SQL更新，避免ORM的日期时间处理问题
            sql = text("""
                UPDATE inspection_records
                SET inspection_type = :inspection_type,
                    inspection_item = :inspection_item,
                    inspector_id = :inspector_id,
                    inspection_time = CONVERT(DATETIME2(1), :inspection_time, 120),
                    status = :status,
                    description = :description,
                    photo_path = :photo_path
                WHERE id = :id
            """)

            # 准备参数
            params = {
                'id': inspection_id,
                'inspection_type': data.get('inspection_type', inspection.inspection_type),
                'inspection_item': data.get('inspection_item', inspection.inspection_item),
                'inspector_id': data.get('inspector_id', inspection.inspector_id),
                'inspection_time': sql_datetime_format(inspection_time),
                'status': data.get('status', inspection.status),
                'description': data.get('description', inspection.description),
                'photo_path': data.get('photo_path', inspection.photo_path)
            }

            # 执行SQL
            db.session.execute(sql, params)
            db.session.commit()

            # 获取更新后的记录
            inspection = InspectionRecord.query.get(inspection_id)

            # 记录活动
            try:
                log_activity('更新了检查记录',
                           f'类型: {inspection.inspection_type}, 项目: {inspection.inspection_item}',
                           'inspection',
                           inspection.id)
            except Exception as e:
                log_error(e, f"记录活动失败: 检查记录ID={inspection.id}")

            return inspection

        except Exception as e:
            db.session.rollback()
            log_error(e, f"更新检查记录失败: ID={inspection_id}, 数据={data}")
            raise

    @staticmethod
    def delete_inspection(inspection_id):
        """
        删除检查记录

        Args:
            inspection_id: 检查记录ID

        Returns:
            bool: 是否删除成功
        """
        try:
            inspection = InspectionRecord.query.get(inspection_id)

            if not inspection:
                current_app.logger.warning(f"删除检查记录失败: 记录不存在 ID={inspection_id}")
                return False

            # 保存记录信息用于日志记录
            inspection_type = inspection.inspection_type
            inspection_item = inspection.inspection_item

            # 使用事务确保数据一致性
            try:
                # 删除关联的照片
                photos = Photo.query.filter_by(reference_type='inspection', reference_id=inspection_id).all()
                for photo in photos:
                    db.session.delete(photo)

                # 删除检查记录
                db.session.delete(inspection)
                db.session.commit()

                # 记录活动
                try:
                    log_activity('删除了检查记录',
                               f'类型: {inspection_type}, 项目: {inspection_item}',
                               'inspection',
                               inspection_id)
                except Exception as e:
                    log_error(e, f"记录活动失败: 检查记录ID={inspection_id}")

                return True

            except Exception as e:
                db.session.rollback()
                log_error(e, f"删除检查记录事务失败: ID={inspection_id}")
                raise

        except Exception as e:
            log_error(e, f"删除检查记录失败: ID={inspection_id}")
            return False


class DiningCompanionService:
    """陪餐记录服务类"""

    @staticmethod
    def get_companions_by_daily_log(daily_log_id):
        """
        获取日志的陪餐记录

        Args:
            daily_log_id: 日志ID

        Returns:
            list: 陪餐记录列表
        """
        return DiningCompanion.query.filter_by(daily_log_id=daily_log_id).all()

    @staticmethod
    def get_companion_by_id(companion_id):
        """
        根据ID获取陪餐记录

        Args:
            companion_id: 陪餐记录ID

        Returns:
            DiningCompanion: 陪餐记录对象
        """
        return DiningCompanion.query.get(companion_id)

    @staticmethod
    def create_companion(data):
        """
        创建陪餐记录

        Args:
            data: 陪餐记录数据

        Returns:
            DiningCompanion: 创建的陪餐记录对象
        """
        try:
            # 处理日期时间参数
            dining_time = data.get('dining_time')
            if isinstance(dining_time, datetime):
                dining_time_str = dining_time.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(dining_time, str):
                dining_time_str = dining_time
            else:
                dining_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 构建 SQL 语句，不包含时间戳字段
            sql = text("""
                INSERT INTO dining_companions
                (daily_log_id, companion_name, companion_role, meal_type,
                 dining_time, taste_rating, hygiene_rating, service_rating,
                 comments, suggestions, photo_paths)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :companion_name, :companion_role, :meal_type,
                 CONVERT(DATETIME2(1), :dining_time, 120), :taste_rating, :hygiene_rating, :service_rating,
                 :comments, :suggestions, :photo_paths)
            """)

            # 准备参数
            params = {
                'daily_log_id': data.get('daily_log_id'),
                'companion_name': data.get('companion_name'),
                'companion_role': data.get('companion_role'),
                'meal_type': data.get('meal_type'),
                'dining_time': dining_time_str,
                'taste_rating': data.get('taste_rating'),
                'hygiene_rating': data.get('hygiene_rating'),
                'service_rating': data.get('service_rating'),
                'comments': data.get('comments'),
                'suggestions': data.get('suggestions'),
                'photo_paths': data.get('photo_paths')
            }

            # 执行 SQL
            result = db.session.execute(sql, params)
            record_id = result.fetchone()[0]
            db.session.commit()

            # 获取创建的记录
            companion = DiningCompanion.query.get(record_id)

            # 记录活动
            if companion:
                try:
                    log_activity('创建了陪餐记录',
                               f'陪餐人: {companion.companion_name}, 餐次: {companion.meal_type}',
                               'companion',
                               companion.id)
                except Exception as e:
                    print(f"记录活动失败: {str(e)}")

            return companion

        except Exception as e:
            db.session.rollback()
            raise

    @staticmethod
    def update_companion(companion_id, data):
        """
        更新陪餐记录

        Args:
            companion_id: 陪餐记录ID
            data: 陪餐记录数据

        Returns:
            DiningCompanion: 更新后的陪餐记录对象
        """
        companion = DiningCompanion.query.get(companion_id)

        if not companion:
            return None

        # 更新陪餐记录
        companion.companion_name = data.get('companion_name', companion.companion_name)
        companion.companion_role = data.get('companion_role', companion.companion_role)
        companion.meal_type = data.get('meal_type', companion.meal_type)
        companion.dining_time = data.get('dining_time', companion.dining_time)
        companion.taste_rating = data.get('taste_rating', companion.taste_rating)
        companion.hygiene_rating = data.get('hygiene_rating', companion.hygiene_rating)
        companion.service_rating = data.get('service_rating', companion.service_rating)
        companion.comments = data.get('comments', companion.comments)
        companion.suggestions = data.get('suggestions', companion.suggestions)

        db.session.commit()

        # 记录活动
        log_activity('更新了陪餐记录', f'陪餐人: {companion.companion_name}, 餐次: {companion.meal_type}', 'companion', companion.id)

        return companion

    @staticmethod
    def delete_companion(companion_id):
        """
        删除陪餐记录

        Args:
            companion_id: 陪餐记录ID

        Returns:
            bool: 是否删除成功
        """
        companion = DiningCompanion.query.get(companion_id)

        if not companion:
            return False

        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='companion', reference_id=companion_id).all()
        for photo in photos:
            db.session.delete(photo)

        # 记录活动
        companion_name = companion.companion_name
        meal_type = companion.meal_type

        # 删除陪餐记录
        db.session.delete(companion)
        db.session.commit()

        # 记录活动
        log_activity('删除了陪餐记录', f'陪餐人: {companion_name}, 餐次: {meal_type}', 'companion', companion_id)

        return True

    @staticmethod
    def get_rating_statistics(start_date=None, end_date=None, area_id=None):
        """
        获取陪餐评价统计数据

        Args:
            start_date: 开始日期字符串，格式为YYYY-MM-DD
            end_date: 结束日期字符串，格式为YYYY-MM-DD
            area_id: 区域ID

        Returns:
            dict: 统计数据
        """
        # 解析日期
        if start_date:
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                start = date.today() - timedelta(days=30)
        else:
            start = date.today() - timedelta(days=30)

        if end_date:
            try:
                end = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                end = date.today()
        else:
            end = date.today()

        # 获取当前用户可访问的区域
        if area_id:
            area_ids = [area_id]
        else:
            accessible_areas = current_user.get_accessible_areas()
            area_ids = [area.id for area in accessible_areas]

        # 查询日志
        logs_query = DailyLog.query.filter(
            DailyLog.log_date >= start,
            DailyLog.log_date <= end
        )

        if area_ids:
            logs_query = logs_query.filter(DailyLog.area_id.in_(area_ids))

        logs = logs_query.all()
        log_ids = [log.id for log in logs]

        # 查询陪餐记录
        companions = DiningCompanion.query.filter(DiningCompanion.daily_log_id.in_(log_ids)).all()

        # 统计数据
        total_count = len(companions)
        taste_ratings = [c.taste_rating for c in companions if c.taste_rating]
        hygiene_ratings = [c.hygiene_rating for c in companions if c.hygiene_rating]
        service_ratings = [c.service_rating for c in companions if c.service_rating]

        avg_taste = sum(taste_ratings) / len(taste_ratings) if taste_ratings else 0
        avg_hygiene = sum(hygiene_ratings) / len(hygiene_ratings) if hygiene_ratings else 0
        avg_service = sum(service_ratings) / len(service_ratings) if service_ratings else 0

        # 按餐次统计
        meal_types = {}
        for c in companions:
            if c.meal_type not in meal_types:
                meal_types[c.meal_type] = {
                    'count': 0,
                    'taste_ratings': [],
                    'hygiene_ratings': [],
                    'service_ratings': []
                }
            meal_types[c.meal_type]['count'] += 1
            if c.taste_rating:
                meal_types[c.meal_type]['taste_ratings'].append(c.taste_rating)
            if c.hygiene_rating:
                meal_types[c.meal_type]['hygiene_ratings'].append(c.hygiene_rating)
            if c.service_rating:
                meal_types[c.meal_type]['service_ratings'].append(c.service_rating)

        # 计算平均值
        for meal_type, data in meal_types.items():
            data['avg_taste'] = sum(data['taste_ratings']) / len(data['taste_ratings']) if data['taste_ratings'] else 0
            data['avg_hygiene'] = sum(data['hygiene_ratings']) / len(data['hygiene_ratings']) if data['hygiene_ratings'] else 0
            data['avg_service'] = sum(data['service_ratings']) / len(data['service_ratings']) if data['service_ratings'] else 0
            # 删除原始评分列表
            del data['taste_ratings']
            del data['hygiene_ratings']
            del data['service_ratings']

        # 构建统计数据
        statistics = {
            'start_date': start.strftime('%Y-%m-%d'),
            'end_date': end.strftime('%Y-%m-%d'),
            'area_id': area_id,
            'total_count': total_count,
            'avg_ratings': {
                'taste': avg_taste,
                'hygiene': avg_hygiene,
                'service': avg_service
            },
            'meal_types': meal_types
        }

        return statistics
