"""
基础服务类

提供通用的数据服务功能，包括学校关联的数据操作。
"""

from flask_login import current_user
from app import db
from sqlalchemy import text

class BaseService:
    """基础服务类，提供通用的数据操作方法"""
    
    @staticmethod
    def get_user_area():
        """获取当前用户所属学校"""
        return current_user.get_current_area()
    
    @staticmethod
    def filter_query_by_school(query, model_class, area_field='area_id'):
        """
        根据用户所属学校筛选查询结果
        
        Args:
            query: SQLAlchemy查询对象
            model_class: 数据模型类
            area_field: 模型中表示学校/区域的字段名
        
        Returns:
            筛选后的查询对象
        """
        # 检查用户是否有关联的学校
        user_area = current_user.get_current_area()
        if not user_area and not current_user.is_admin():
            # 如果用户没有关联学校且不是管理员，返回空查询
            return query.filter(False)
        
        # 如果用户是管理员，不进行筛选
        if current_user.is_admin():
            return query
        
        # 获取字段对象
        area_field_obj = getattr(model_class, area_field, None)
        if area_field_obj is None:
            # 如果模型没有区域字段，返回原查询
            return query
        
        # 根据用户所属学校筛选查询结果
        return query.filter(area_field_obj == user_area.id)
    
    @staticmethod
    def execute_school_filtered_sql(sql_template, params=None, area_field='area_id'):
        """
        执行带有学校筛选的SQL语句
        
        Args:
            sql_template: SQL模板字符串
            params: SQL参数字典
            area_field: 表示学校/区域的字段名
        
        Returns:
            执行结果
        """
        # 检查用户是否有关联的学校
        user_area = current_user.get_current_area()
        if not user_area and not current_user.is_admin():
            # 如果用户没有关联学校且不是管理员，返回空结果
            return []
        
        # 准备参数
        if params is None:
            params = {}
        
        # 如果用户不是管理员，添加学校筛选条件
        if not current_user.is_admin():
            # 检查SQL是否已经包含WHERE子句
            if 'WHERE' in sql_template.upper():
                # 在WHERE子句后添加AND条件
                sql_template = sql_template.replace('WHERE', f'WHERE {area_field} = :user_area_id AND')
                sql_template = sql_template.replace('where', f'where {area_field} = :user_area_id AND')
            else:
                # 添加WHERE子句
                sql_template = sql_template + f' WHERE {area_field} = :user_area_id'
            
            # 添加用户所属学校ID参数
            params['user_area_id'] = user_area.id
        
        # 执行SQL
        sql = text(sql_template)
        result = db.session.execute(sql, params)
        
        return result
    
    @staticmethod
    def create_with_school(model_class, data, area_field='area_id'):
        """
        创建关联到用户所属学校的记录
        
        Args:
            model_class: 数据模型类
            data: 记录数据字典
            area_field: 表示学校/区域的字段名
        
        Returns:
            创建的记录
        """
        # 检查用户是否有关联的学校
        user_area = current_user.get_current_area()
        if not user_area:
            raise ValueError('用户没有关联到任何学校，无法创建记录')
        
        # 添加学校关联
        data[area_field] = user_area.id
        
        # 创建记录
        record = model_class(**data)
        db.session.add(record)
        db.session.commit()
        
        return record
    
    @staticmethod
    def update_with_school_check(record, data, area_field='area_id'):
        """
        更新记录，并检查是否属于用户所属学校
        
        Args:
            record: 要更新的记录
            data: 更新数据字典
            area_field: 表示学校/区域的字段名
        
        Returns:
            更新后的记录
        """
        # 检查用户是否有关联的学校
        user_area = current_user.get_current_area()
        if not user_area and not current_user.is_admin():
            raise ValueError('用户没有关联到任何学校，无法更新记录')
        
        # 检查记录是否属于用户所属学校
        record_area_id = getattr(record, area_field, None)
        if record_area_id != user_area.id and not current_user.is_admin():
            raise ValueError('用户没有权限更新此记录')
        
        # 更新记录
        for key, value in data.items():
            setattr(record, key, value)
        
        db.session.commit()
        
        return record
    
    @staticmethod
    def delete_with_school_check(record, area_field='area_id'):
        """
        删除记录，并检查是否属于用户所属学校
        
        Args:
            record: 要删除的记录
            area_field: 表示学校/区域的字段名
        
        Returns:
            是否删除成功
        """
        # 检查用户是否有关联的学校
        user_area = current_user.get_current_area()
        if not user_area and not current_user.is_admin():
            raise ValueError('用户没有关联到任何学校，无法删除记录')
        
        # 检查记录是否属于用户所属学校
        record_area_id = getattr(record, area_field, None)
        if record_area_id != user_area.id and not current_user.is_admin():
            raise ValueError('用户没有权限删除此记录')
        
        # 删除记录
        db.session.delete(record)
        db.session.commit()
        
        return True
