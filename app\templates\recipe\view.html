{% extends 'base.html' %}

{% block title %}{{ recipe.name }} - 食谱详情{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .recipe-header {
        position: relative;
        padding-bottom: 20px;
        margin-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    .recipe-image {
        max-height: 300px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .recipe-info-item {
        margin-bottom: 10px;
    }
    .recipe-info-label {
        font-weight: bold;
        color: #555;
    }
    .ingredient-list {
        list-style-type: none;
        padding-left: 0;
    }
    .ingredient-item {
        padding: 8px 0;
        border-bottom: 1px dashed #eee;
    }
    .ingredient-item:last-child {
        border-bottom: none;
    }
    .process-step {
        position: relative;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
        background-color: #f9f9f9;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .process-step-number {
        position: absolute;
        top: -10px;
        left: -10px;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        font-weight: bold;
    }
    .process-image {
        max-height: 150px;
        border-radius: 5px;
        margin-top: 10px;
    }
    .nutrition-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 20px;
    }
    .nutrition-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #495057;
    }
    .process-ingredients {
        margin-top: 10px;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
    }
    .process-ingredient-item {
        padding: 5px 10px;
        margin-bottom: 5px;
        border-radius: 3px;
        background-color: #fff;
        border-left: 3px solid #28a745;
    }
    .favorite-btn {
        cursor: pointer;
        transition: all 0.3s;
        font-size: 1.5rem;
        color: #ccc;
    }
    .favorite-btn.active {
        color: #dc3545;
    }
    .favorite-btn:hover {
        transform: scale(1.1);
    }
    .recipe-actions {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    .recipe-rating {
        display: flex;
        align-items: center;
        margin-left: 20px;
    }
    .rating-stars {
        color: #ffc107;
        margin-right: 5px;
    }
    .rating-count {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食谱详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('recipe.edit', id=recipe.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        <a href="{{ url_for('recipe.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 食谱基本信息 -->
                    <div class="recipe-header">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                {% if recipe.main_image %}
                                <img src="{{ url_for('static', filename=recipe.main_image) }}" alt="{{ recipe.name }}" class="recipe-image img-fluid">
                                {% else %}
                                <div class="recipe-image-placeholder d-flex align-items-center justify-content-center bg-light" style="height: 250px;">
                                    <i class="fas fa-utensils fa-5x text-muted"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-8">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h2>{{ recipe.name }}</h2>
                                    <div class="favorite-btn" id="favoriteBtn" data-recipe-id="{{ recipe.id }}">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                </div>

                                <div class="recipe-actions mb-3">
                                    <div class="recipe-rating">
                                        <div class="rating-stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star-half-alt"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                        <div class="rating-count">4.5 (12人评价)</div>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary ml-3" id="rateRecipeBtn">
                                        <i class="fas fa-star mr-1"></i> 评价
                                    </button>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">分类：</span>
                                            {% if recipe.category_rel %}
                                                {{ recipe.category_rel.name }}
                                            {% else %}
                                                {{ recipe.category }}
                                            {% endif %}
                                        </div>
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">适用餐次：</span>
                                            {{ recipe.meal_type or '未指定' }}
                                        </div>
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">烹饪方法：</span>
                                            {{ recipe.cooking_method or '未指定' }}
                                        </div>
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">烹饪时间：</span>
                                            {% if recipe.cooking_time %}{{ recipe.cooking_time }} 分钟{% else %}未指定{% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">热量：</span>
                                            {% if recipe.calories %}{{ recipe.calories }} 卡路里{% else %}未指定{% endif %}
                                        </div>
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">标准份量：</span>
                                            {% if recipe.serving_size %}{{ recipe.serving_size }} 份{% else %}未指定{% endif %}
                                        </div>
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">状态：</span>
                                            {% if recipe.status == 1 %}
                                            <span class="badge badge-success">启用</span>
                                            {% else %}
                                            <span class="badge badge-danger">停用</span>
                                            {% endif %}
                                            {% if recipe.is_user_defined %}
                                            <span class="badge badge-warning ml-1">自定义</span>
                                            {% endif %}
                                        </div>
                                        <div class="recipe-info-item">
                                            <span class="recipe-info-label">创建时间：</span>
                                            {{  recipe.created_at|format_datetime('%Y-%m-%d')   }}
                                        </div>
                                    </div>
                                </div>
                                {% if recipe.description %}
                                <div class="recipe-info-item mt-3">
                                    <span class="recipe-info-label">描述：</span>
                                    <p class="mt-2">{{ recipe.description }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 食材配比 -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">食材配比</h4>
                        </div>
                        <div class="card-body">
                            {% if recipe_ingredients %}
                            <div class="row">
                                <div class="col-md-8">
                                    <ul class="ingredient-list">
                                        {% for ri in recipe_ingredients %}
                                        <li class="ingredient-item">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>{{ ri.ingredient.name }}</strong>
                                                </div>
                                                <div class="col-md-6">
                                                    {{ ri.quantity }} {{ ri.unit }}
                                                </div>
                                            </div>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    {% if recipe.nutrition_info %}
                                    <div class="nutrition-info">
                                        <div class="nutrition-title">营养信息</div>
                                        <div class="nutrition-content">
                                            {{  recipe.nutrition_info|nl2br  }}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% else %}
                            <p class="text-muted">暂无食材信息</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 制作工序 -->
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h4 class="mb-0">制作工序</h4>
                        </div>
                        <div class="card-body">
                            {% if recipe_processes %}
                            <div class="process-steps">
                                {% for process in recipe_processes %}
                                <div class="process-step">
                                    <div class="process-step-number">{{ loop.index }}</div>
                                    <h5>{{ process.process_name }}</h5>

                                    {% if process.description %}
                                    <p>{{ process.description }}</p>
                                    {% endif %}

                                    {% if process.estimated_time %}
                                    <div class="text-muted">
                                        <i class="far fa-clock"></i> 预计时间: {{ process.estimated_time }} 分钟
                                    </div>
                                    {% endif %}

                                    {% if process_ingredients.get(process.id) %}
                                    <div class="process-ingredients">
                                        <div class="mb-2"><strong>所需食材:</strong></div>
                                        {% for pi in process_ingredients.get(process.id) %}
                                        <div class="process-ingredient-item">
                                            {{ pi.ingredient.name }}: {{ pi.quantity }} {{ pi.unit }}
                                            {% if pi.processing_method %} ({{ pi.processing_method }}){% endif %}
                                            {% if pi.notes %} - {{ pi.notes }}{% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    {% if process.image %}
                                    <div class="text-center mt-3">
                                        <img src="{{ url_for('static', filename=process.image) }}" alt="{{ process.process_name }}" class="process-image">
                                    </div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <p class="text-muted">暂无工序信息</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- 评分模态框 -->
<div class="modal fade" id="rateRecipeModal" tabindex="-1" role="dialog" aria-labelledby="rateRecipeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rateRecipeModalLabel">评价食谱</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="rateRecipeForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="form-group">
                        <label>您对这个食谱的评分</label>
                        <div class="rating-input text-center">
                            <i class="far fa-star fa-2x rating-star" data-rating="1"></i>
                            <i class="far fa-star fa-2x rating-star" data-rating="2"></i>
                            <i class="far fa-star fa-2x rating-star" data-rating="3"></i>
                            <i class="far fa-star fa-2x rating-star" data-rating="4"></i>
                            <i class="far fa-star fa-2x rating-star" data-rating="5"></i>
                        </div>
                        <input type="hidden" id="ratingValue" name="rating" value="0">
                    </div>
                    <div class="form-group">
                        <label for="ratingComment">评价内容</label>
                        <textarea class="form-control" id="ratingComment" name="comment" rows="3" placeholder="分享您对这个食谱的看法..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submitRatingBtn">提交评价</button>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function() {
        // 获取CSRF令牌
        var csrfToken = $('meta[name=csrf-token]').attr('content');

        // 设置AJAX默认头部包含CSRF令牌
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrfToken);
                }
            }
        });

        // 检查食谱是否已收藏
        checkFavoriteStatus();

        // 收藏按钮点击事件
        $('#favoriteBtn').click(function() {
            var recipeId = $(this).data('recipe-id');
            toggleFavorite(recipeId);
        });

        // 评分按钮点击事件
        $('#rateRecipeBtn').click(function() {
            // 使用requireLogin函数检查登录状态
            requireLogin(function() {
                $('#rateRecipeModal').modal('show');
            }, '请先登录后再评价食谱');
        });

        // 评分星星点击事件
        $('.rating-star').hover(
            function() {
                var rating = $(this).data('rating');
                updateStars(rating, 'hover');
            },
            function() {
                var currentRating = $('#ratingValue').val();
                updateStars(currentRating, 'selected');
            }
        );

        $('.rating-star').click(function() {
            var rating = $(this).data('rating');
            $('#ratingValue').val(rating);
            updateStars(rating, 'selected');
        });

        // 提交评分
        $('#submitRatingBtn').click(function() {
            var rating = $('#ratingValue').val();
            var comment = $('#ratingComment').val();

            if (rating == 0) {
                toastr.warning('请选择评分');
                return;
            }

            // 使用requireLogin函数检查登录状态
            requireLogin(function() {
                // 这里可以添加AJAX请求提交评分
                $.ajax({
                    url: '/api/rate-recipe/' + $('#favoriteBtn').data('recipe-id'),
                    type: 'POST',
                    data: {
                        rating: rating,
                        comment: comment
                    },
                    success: function(response) {
                        toastr.success('评分提交成功！');
                        $('#rateRecipeModal').modal('hide');
                    },
                    error: function(xhr) {
                        // 使用handleAuthError函数处理认证错误
                        if (!handleAuthError(xhr, '{{ request.path }}')) {
                            toastr.error('评分提交失败，请重试');
                        }
                    }
                });
            }, '请先登录后再评价食谱');
        });

        // 检查食谱是否已收藏
        function checkFavoriteStatus() {
            // 只有在用户已登录的情况下才检查收藏状态
            if (getLoginStatus()) {
                var recipeId = $('#favoriteBtn').data('recipe-id');

                $.ajax({
                    url: '/api/check-favorite/' + recipeId,
                    type: 'GET',
                    success: function(response) {
                        if (response.is_favorited) {
                            $('#favoriteBtn').addClass('active');
                        }
                    },
                    error: function(xhr) {
                        // 使用handleAuthError函数处理认证错误
                        handleAuthError(xhr, '{{ request.path }}');
                    }
                });
            }
        }

        // 收藏/取消收藏
        function toggleFavorite(recipeId) {
            // 使用requireLogin函数检查登录状态
            requireLogin(function() {
                $.ajax({
                    url: '/api/toggle-favorite/' + recipeId,
                    type: 'POST',
                    success: function(response) {
                        if (response.action === 'favorited') {
                            $('#favoriteBtn').addClass('active');
                            toastr.success('食谱已收藏');
                        } else {
                            $('#favoriteBtn').removeClass('active');
                            toastr.info('已取消收藏');
                        }
                    },
                    error: function(xhr) {
                        // 使用handleAuthError函数处理认证错误
                        if (!handleAuthError(xhr, '{{ request.path }}')) {
                            toastr.error('操作失败，请重试');
                        }
                    }
                });
            }, '请先登录后再收藏食谱');
        }

        // 更新星星显示
        function updateStars(rating, type) {
            $('.rating-star').removeClass('fas').addClass('far');

            if (rating > 0) {
                for (var i = 1; i <= rating; i++) {
                    $('.rating-star[data-rating="' + i + '"]').removeClass('far').addClass('fas');
                }
            }

            if (type === 'hover') {
                $('.rating-star').css('color', '');
                for (var i = 1; i <= rating; i++) {
                    $('.rating-star[data-rating="' + i + '"]').css('color', '#ffc107');
                }
            } else {
                $('.rating-star').css('color', '');
            }
        }
    });
</script>
{% endblock %}
