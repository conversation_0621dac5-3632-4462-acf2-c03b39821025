"""
二维码生成工具模块
提供生成和处理二维码的功能
"""

from flask import current_app, url_for
import qrcode
from io import BytesIO
import os
import base64
from datetime import datetime

def generate_qrcode_buffer(data, size=10, border=4):
    """
    生成二维码并返回BytesIO对象

    Args:
        data: 要编码的数据
        size: 二维码方块大小
        border: 二维码边框宽度

    Returns:
        BytesIO: 包含二维码图像的BytesIO对象
    """
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=size,
            border=border,
        )
        qr.add_data(data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer)
        buffer.seek(0)

        return buffer
    except Exception as e:
        current_app.logger.error(f"Failed to generate QR code: {str(e)}")
        raise

def generate_qrcode_base64(data, size=10, border=4):
    """
    生成二维码并返回base64编码的字符串

    Args:
        data: 要编码的数据
        size: 二维码方块大小
        border: 二维码边框宽度

    Returns:
        str: base64编码的二维码图像
    """
    try:
        # 直接使用qrcode库生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=size,
            border=border,
        )
        qr.add_data(data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # 将图像转换为bytes
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        buffer.seek(0)

        # 转换为base64
        encoded = base64.b64encode(buffer.getvalue()).decode('utf-8')

        current_app.logger.info(f"成功生成二维码base64，数据长度: {len(encoded)}")

        return encoded
    except Exception as e:
        current_app.logger.error(f"生成二维码base64失败: {str(e)}")
        return ""

def generate_companion_qrcode(school_id, log_id=None, use_entry=False):
    """
    生成陪餐记录二维码，如果已存在则直接返回

    Args:
        school_id: 学校ID
        log_id: 日志ID (可选)
        use_entry: 是否使用入口页面 (默认False)

    Returns:
        str: 二维码图像的相对路径
    """
    try:
        # 创建保存目录
        qrcode_dir = os.path.join(current_app.root_path, 'static', 'qrcodes', 'companions')
        if not os.path.exists(qrcode_dir):
            os.makedirs(qrcode_dir)

        # 生成固定文件名（不包含时间戳）
        if use_entry:
            filename = f"companion_school_{school_id}_entry.png"
        elif log_id:
            # 对于特定日志的二维码，使用通用名称，不包含具体log_id
            # 这样可以确保每个学校只有一个固定的二维码
            filename = f"companion_school_{school_id}_direct.png"
        else:
            filename = f"companion_school_{school_id}_direct.png"

        file_path = os.path.join(qrcode_dir, filename)

        # 生成URL
        if use_entry:
            # 使用入口页面
            url = url_for('daily_management.companion_entry', school_id=school_id, _external=True)
        elif log_id:
            # 直接到添加页面，带日志ID
            url = url_for('daily_management.public_add_companion', school_id=school_id, log_id=log_id, _external=True)
        else:
            # 直接到添加页面，不带日志ID
            url = url_for('daily_management.public_add_companion', school_id=school_id, _external=True)

        # 检查文件是否已存在
        if os.path.exists(file_path):
            # 文件已存在，直接返回路径
            current_app.logger.info(f"使用现有二维码: {filename}")
            return os.path.join('static', 'qrcodes', 'companions', filename)

        # 文件不存在，生成新的二维码
        current_app.logger.info(f"生成新二维码: {filename}, URL: {url}")
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        img.save(file_path)

        # 返回相对路径
        return os.path.join('static', 'qrcodes', 'companions', filename)

    except Exception as e:
        current_app.logger.error(f"Failed to generate companion QR code: {str(e)}")
        raise
