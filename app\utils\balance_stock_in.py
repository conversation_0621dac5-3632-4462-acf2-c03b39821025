"""
平账入库工具模块 - 用于处理库存不足时自动生成平账入库
"""
from datetime import datetime, timedelta
from sqlalchemy import text
from app import db
from app.models import Inventory, StorageLocation, AuditLog
import json
import uuid

def create_balance_stock_in(ingredient_id, warehouse_id, required_quantity, current_quantity, unit, consumption_plan_id, operator_id):
    """
    创建平账入库记录

    Args:
        ingredient_id: 食材ID
        warehouse_id: 仓库ID
        required_quantity: 需要的数量
        current_quantity: 当前库存数量
        unit: 单位
        consumption_plan_id: 消耗计划ID
        operator_id: 操作员ID

    Returns:
        dict: 包含平账结果的字典
    """
    try:
        # 获取食材名称，用于日志记录
        from app.models import Ingredient
        ingredient = Ingredient.query.get(ingredient_id)
        ingredient_name = ingredient.name if ingredient else f"食材ID: {ingredient_id}"

        # 计算差额
        difference = required_quantity - current_quantity

        # 只有在差额为正数时才创建平账入库
        if difference <= 0:
            return {
                'success': True,
                'message': '库存充足，无需平账',
                'difference': 0,
                'ingredient_name': ingredient_name
            }

        # 生成批次号 - 使用更明确的格式，包含食材ID
        batch_number = f"BAL-{datetime.now().strftime('%Y%m%d')}-{ingredient_id}-{uuid.uuid4().hex[:6]}"

        # 生成入库单号 - 使用更明确的格式
        stock_in_number = f"RK-BAL-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 记录详细日志
        from flask import current_app
        current_app.logger.info(f"创建平账入库: 食材={ingredient_name}, 需要={required_quantity}, 当前={current_quantity}, 差额={difference}, 批次号={batch_number}")

        # 1. 创建入库单
        sql_stock_in = text("""
        INSERT INTO stock_ins (
            stock_in_number, warehouse_id, stock_in_date, stock_in_type,
            operator_id, status, notes, created_at, updated_at
        )
        OUTPUT inserted.id
        VALUES (
            :stock_in_number, :warehouse_id, GETDATE(), :stock_in_type,
            :operator_id, :status, :notes, GETDATE(), GETDATE()
        )
        """)

        result = db.session.execute(sql_stock_in, {
            'stock_in_number': stock_in_number,
            'warehouse_id': warehouse_id,
            'stock_in_type': '平账入库',
            'operator_id': operator_id,
            'status': '已完成',
            'notes': f'系统自动生成的平账入库，关联消耗计划ID: {consumption_plan_id}'
        })

        stock_in_id = result.fetchone()[0]

        # 2. 创建入库明细
        sql_stock_in_item = text("""
        INSERT INTO stock_in_items (
            stock_in_id, ingredient_id, storage_location_id, batch_number,
            quantity, unit, production_date, expiry_date, notes,
            quality_status, created_at, updated_at
        )
        OUTPUT inserted.id
        VALUES (
            :stock_in_id, :ingredient_id, :storage_location_id, :batch_number,
            :quantity, :unit, GETDATE(), DATEADD(MONTH, 3, GETDATE()), :notes,
            :quality_status, GETDATE(), GETDATE()
        )
        """)

        # 获取默认存储位置
        storage_location = StorageLocation.query.filter_by(warehouse_id=warehouse_id).first()
        storage_location_id = storage_location.id if storage_location else None

        result = db.session.execute(sql_stock_in_item, {
            'stock_in_id': stock_in_id,
            'ingredient_id': ingredient_id,
            'storage_location_id': storage_location_id,
            'batch_number': batch_number,
            'quantity': difference,
            'unit': unit,
            'notes': '平账入库自动生成',
            'quality_status': '良好'
        })

        stock_in_item_id = result.fetchone()[0]

        # 3. 更新库存
        # 先检查是否已经存在相同批次的库存
        check_inventory_sql = text("""
        SELECT id, quantity FROM inventories
        WHERE batch_number = :batch_number AND ingredient_id = :ingredient_id
        AND warehouse_id = :warehouse_id AND status = '正常'
        """)

        existing_inventory = db.session.execute(check_inventory_sql, {
            'batch_number': batch_number,
            'ingredient_id': ingredient_id,
            'warehouse_id': warehouse_id
        }).fetchone()

        if existing_inventory:
            # 更新现有库存
            update_inventory_sql = text("""
            UPDATE inventories
            SET quantity = quantity + :quantity,
                updated_at = GETDATE(),
                notes = CONCAT(notes, '; 平账入库更新，关联消耗计划ID: ', :consumption_plan_id)
            WHERE id = :id
            """)

            db.session.execute(update_inventory_sql, {
                'quantity': difference,
                'consumption_plan_id': consumption_plan_id,
                'id': existing_inventory.id
            })

            from flask import current_app
            current_app.logger.info(f"更新现有库存: ID={existing_inventory.id}, 原数量={existing_inventory.quantity}, 新增={difference}")
        else:
            # 创建新库存记录
            sql_inventory = text("""
            INSERT INTO inventories (
                warehouse_id, storage_location_id, ingredient_id, batch_number,
                quantity, unit, production_date, expiry_date, status, notes,
                created_at, updated_at
            )
            VALUES (
                :warehouse_id, :storage_location_id, :ingredient_id, :batch_number,
                :quantity, :unit, GETDATE(), DATEADD(MONTH, 3, GETDATE()), :status, :notes,
                GETDATE(), GETDATE()
            )
            """)

            db.session.execute(sql_inventory, {
                'warehouse_id': warehouse_id,
                'storage_location_id': storage_location_id,
                'ingredient_id': ingredient_id,
                'batch_number': batch_number,
                'quantity': difference,
                'unit': unit,
                'status': '正常',
                'notes': f'平账入库自动生成，关联消耗计划ID: {consumption_plan_id}'
            })

            from flask import current_app
            current_app.logger.info(f"创建新库存记录: 批次号={batch_number}, 数量={difference}")

        # 4. 记录审计日志
        audit_log = AuditLog(
            user_id=operator_id,
            action='balance_stock_in',
            resource_type='consumption_plan',
            resource_id=consumption_plan_id,
            details=json.dumps({
                'ingredient_id': ingredient_id,
                'required_quantity': required_quantity,
                'current_quantity': current_quantity,
                'difference': difference,
                'stock_in_id': stock_in_id,
                'stock_in_item_id': stock_in_item_id
            })
        )

        db.session.add(audit_log)
        db.session.commit()

        # 记录详细日志
        from flask import current_app
        current_app.logger.info(f"平账入库完成: 食材={ingredient_name}, 补充数量={difference}, 入库单ID={stock_in_id}")

        return {
            'success': True,
            'message': f'成功创建平账入库，补充库存 {difference} {unit}',
            'stock_in_id': stock_in_id,
            'stock_in_number': stock_in_number,
            'batch_number': batch_number,
            'difference': difference,
            'ingredient_name': ingredient_name
        }

    except Exception as e:
        db.session.rollback()
        # 记录错误日志
        from flask import current_app
        current_app.logger.error(f"创建平账入库失败: {str(e)}")

        return {
            'success': False,
            'message': f'创建平账入库失败: {str(e)}',
            'error': str(e),
            'ingredient_id': ingredient_id,
            'required_quantity': required_quantity,
            'current_quantity': current_quantity
        }
