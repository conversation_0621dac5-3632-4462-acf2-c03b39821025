#!/usr/bin/env python3
"""
简单的单价保存调试脚本
"""

def check_backend_logic():
    """检查后端逻辑"""
    print("🔍 检查后端单价保存逻辑...")
    
    try:
        with open('app/routes/stock_in.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键代码片段
        checks = [
            {
                'name': '获取单价字段',
                'pattern': "unit_price = request.form.get(f'unit_price_{item_id}')",
                'found': False
            },
            {
                'name': 'SQL更新语句包含单价',
                'pattern': 'unit_price = :unit_price',
                'found': False
            },
            {
                'name': '执行SQL时传递单价参数',
                'pattern': "'unit_price': unit_price",
                'found': False
            },
            {
                'name': '调试日志',
                'pattern': 'current_app.logger.info',
                'found': False
            }
        ]
        
        for check in checks:
            if check['pattern'] in content:
                check['found'] = True
                print(f"  ✅ {check['name']}: 找到")
            else:
                print(f"  ❌ {check['name']}: 未找到")
        
        # 检查是否所有关键逻辑都存在
        all_found = all(check['found'] for check in checks)
        
        if all_found:
            print("✅ 后端逻辑检查通过")
        else:
            print("⚠️ 后端逻辑可能有问题")
        
        return all_found
        
    except FileNotFoundError:
        print("❌ 未找到后端路由文件")
        return False
    except Exception as e:
        print(f"❌ 检查后端文件时出错: {e}")
        return False

def check_frontend_logic():
    """检查前端逻辑"""
    print("\n🔍 检查前端单价字段逻辑...")
    
    try:
        # 检查HTML模板
        with open('app/templates/stock_in/batch_editor_simplified.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        html_checks = [
            {
                'name': '单价输入框',
                'pattern': 'name="unit_price_{{ item.id }}"',
                'found': False
            },
            {
                'name': '复选框',
                'pattern': 'name="selected_items[]"',
                'found': False
            },
            {
                'name': '表单action',
                'pattern': 'save_batch_edit_simplified',
                'found': False
            }
        ]
        
        for check in html_checks:
            if check['pattern'] in html_content:
                check['found'] = True
                print(f"  ✅ {check['name']}: 找到")
            else:
                print(f"  ❌ {check['name']}: 未找到")
        
        # 检查JavaScript文件
        try:
            with open('app/templates/stock_in/batch_editor_simplified_scripts.html', 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            js_checks = [
                {
                    'name': '表单提交处理',
                    'pattern': 'batchEditForm',
                    'found': False
                },
                {
                    'name': '禁用字段处理',
                    'pattern': 'disabledFields',
                    'found': False
                },
                {
                    'name': 'AJAX提交',
                    'pattern': '$.ajax',
                    'found': False
                }
            ]
            
            for check in js_checks:
                if check['pattern'] in js_content:
                    check['found'] = True
                    print(f"  ✅ {check['name']}: 找到")
                else:
                    print(f"  ❌ {check['name']}: 未找到")
            
        except FileNotFoundError:
            print("  ⚠️ 未找到JavaScript文件")
        
        return True
        
    except FileNotFoundError:
        print("❌ 未找到HTML模板文件")
        return False
    except Exception as e:
        print(f"❌ 检查前端文件时出错: {e}")
        return False

def analyze_potential_issues():
    """分析潜在问题"""
    print("\n🔍 分析潜在问题...")
    
    issues = [
        {
            'issue': '复选框未勾选',
            'description': '用户没有勾选复选框，导致该行数据不会被提交',
            'solution': '确保勾选要编辑的行的复选框'
        },
        {
            'issue': '字段被禁用',
            'description': '未选中行的输入字段被禁用，禁用字段不会在表单提交时发送',
            'solution': '在表单提交前临时启用选中行的字段'
        },
        {
            'issue': '表单验证失败',
            'description': 'JavaScript验证阻止了表单提交',
            'solution': '检查所有必填字段是否有值'
        },
        {
            'issue': '数据类型转换问题',
            'description': '单价字段的数据类型转换可能有问题',
            'solution': '确保单价值是有效的数字格式'
        },
        {
            'issue': 'SQL执行失败',
            'description': 'SQL更新语句执行时出错',
            'solution': '检查数据库连接和SQL语句语法'
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"\n{i}. {issue['issue']}")
        print(f"   描述: {issue['description']}")
        print(f"   解决方案: {issue['solution']}")

def provide_debugging_steps():
    """提供调试步骤"""
    print("\n💡 调试步骤建议:")
    print("="*60)
    
    print("\n1. 🌐 浏览器调试:")
    print("   - 打开 http://127.0.0.1:5000/stock-in/76/batch-editor-simplified")
    print("   - 按F12打开开发者工具")
    print("   - 切换到Network标签页")
    
    print("\n2. ✅ 操作步骤:")
    print("   - 勾选一个批次项目的复选框")
    print("   - 修改单价值（如：19999.99）")
    print("   - 确保其他必填字段有值")
    print("   - 点击保存按钮")
    
    print("\n3. 🔍 检查Network请求:")
    print("   - 查看POST请求到 save_batch_edit_simplified")
    print("   - 检查请求的Form Data部分")
    print("   - 确认是否包含 unit_price_XX 字段")
    
    print("\n4. 📊 检查Console输出:")
    print("   - 查看是否有JavaScript错误")
    print("   - 查看调试日志输出")
    
    print("\n5. 🗄️ 检查服务器日志:")
    print("   - 在运行Flask的终端查看日志输出")
    print("   - 查看是否有单价字段的调试信息")
    
    print("\n6. 🔄 验证结果:")
    print("   - 重新访问编辑器页面")
    print("   - 检查单价值是否已更新")
    print("   - 或访问详情页面查看单价")

def main():
    """主函数"""
    print("🚀 开始简单的单价保存调试\n")
    
    # 检查后端逻辑
    backend_ok = check_backend_logic()
    
    # 检查前端逻辑
    frontend_ok = check_frontend_logic()
    
    # 分析潜在问题
    analyze_potential_issues()
    
    # 提供调试步骤
    provide_debugging_steps()
    
    # 输出总结
    print(f"\n{'='*60}")
    print("📊 检查结果总结")
    print('='*60)
    
    print(f"后端逻辑: {'✅ 正常' if backend_ok else '❌ 有问题'}")
    print(f"前端逻辑: {'✅ 正常' if frontend_ok else '❌ 有问题'}")
    
    if backend_ok and frontend_ok:
        print("\n🎯 代码逻辑看起来正常，问题可能在于:")
        print("1. 用户操作流程（复选框未勾选）")
        print("2. 表单验证阻止提交")
        print("3. 数据库连接或权限问题")
        print("4. 浏览器缓存问题")
    else:
        print("\n⚠️ 发现代码逻辑问题，需要修复")
    
    print("\n💡 建议:")
    print("1. 按照上述调试步骤进行手动测试")
    print("2. 重点检查Network请求中的表单数据")
    print("3. 查看服务器端的调试日志输出")
    print("4. 确认数据库中的数据是否真的没有更新")
    
    return backend_ok and frontend_ok

if __name__ == '__main__':
    success = main()
    print(f"\n{'🎉 检查完成' if success else '⚠️ 发现问题'}")
