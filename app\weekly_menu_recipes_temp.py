"""
周菜单菜品临时表模型
用于存储周菜单菜品的临时数据，确保recipe_id正确保存
"""

from datetime import datetime
from sqlalchemy.dialects.mssql import DATETIME2
from app import db


class WeeklyMenuRecipesTemp(db.Model):
    """周菜单菜品临时表"""
    __tablename__ = 'weekly_menu_recipes_temp'

    id = db.Column(db.Integer, primary_key=True)
    weekly_menu_id = db.Column(db.Integer, db.ForeignKey('weekly_menus.id', ondelete='CASCADE'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)
    meal_type = db.Column(db.String(20), nullable=False)
    recipe_id = db.Column(db.Integer, nullable=True)
    recipe_name = db.Column(db.String(100), nullable=False)
    is_custom = db.Column(db.<PERSON>, default=False)
    temp_data = db.Column(db.Text, nullable=True)  # 存储JSON格式的临时数据
    created_at = db.Column(DATETIME2(precision=1), default=datetime.now)
    updated_at = db.Column(DATETIME2(precision=1), default=datetime.now, onupdate=datetime.now)

    # 关联关系
    weekly_menu = db.relationship('WeeklyMenu', backref=db.backref('temp_recipes', lazy='dynamic', cascade='all, delete-orphan'))

    def __init__(self, weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name, is_custom=False, temp_data=None):
        self.weekly_menu_id = weekly_menu_id
        self.day_of_week = day_of_week
        self.meal_type = meal_type
        self.recipe_id = recipe_id
        self.recipe_name = recipe_name
        self.is_custom = is_custom
        self.temp_data = temp_data

    def __repr__(self):
        return f'<WeeklyMenuRecipesTemp {self.id}: {self.recipe_name}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'weekly_menu_id': self.weekly_menu_id,
            'day_of_week': self.day_of_week,
            'meal_type': self.meal_type,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe_name,
            'is_custom': self.is_custom,
            'temp_data': self.temp_data,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_by_weekly_menu(cls, weekly_menu_id):
        """获取指定周菜单的所有临时菜品"""
        return cls.query.filter_by(weekly_menu_id=weekly_menu_id).all()

    @classmethod
    def get_by_day_meal(cls, weekly_menu_id, day_of_week, meal_type):
        """获取指定日期和餐次的临时菜品"""
        return cls.query.filter_by(
            weekly_menu_id=weekly_menu_id,
            day_of_week=day_of_week,
            meal_type=meal_type
        ).all()

    @classmethod
    def delete_by_day_meal(cls, weekly_menu_id, day_of_week, meal_type):
        """删除指定日期和餐次的临时菜品"""
        cls.query.filter_by(
            weekly_menu_id=weekly_menu_id,
            day_of_week=day_of_week,
            meal_type=meal_type
        ).delete()
        db.session.commit()

    @classmethod
    def sync_to_weekly_menu_recipes(cls, weekly_menu_id):
        """同步临时菜品数据到周菜单菜品表"""
        from app.models import WeeklyMenuRecipe

        # 获取所有临时菜品
        temp_recipes = cls.get_by_weekly_menu(weekly_menu_id)

        # 删除原有的周菜单菜品
        WeeklyMenuRecipe.query.filter_by(weekly_menu_id=weekly_menu_id).delete()

        # 添加新的周菜单菜品
        for temp in temp_recipes:
            recipe = WeeklyMenuRecipe(
                weekly_menu_id=temp.weekly_menu_id,
                day_of_week=temp.day_of_week,
                meal_type=temp.meal_type,
                recipe_id=temp.recipe_id,
                recipe_name=temp.recipe_name
            )
            db.session.add(recipe)

        # 提交事务
        db.session.commit()

        return len(temp_recipes)
