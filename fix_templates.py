"""
修复模板中的strftime错误

自动修复所有模板文件中的 'str object has no attribute strftime' 错误。
将 xxx.strftime(...) 替换为 xxx|safe_datetime(...)
"""

import os
import re
import sys

# 模板目录
TEMPLATES_DIR = 'app/templates/daily_management'

# 正则表达式模式，匹配 xxx.strftime('...') 或 xxx.strftime("%...")
STRFTIME_PATTERN = r'([a-zA-Z0-9_\.]+)\.strftime\([\'"]([^\'"]+)[\'"]\)'

# 替换函数
def replace_strftime(match):
    """将 xxx.strftime('...') 替换为 xxx|safe_datetime('...')"""
    var_name = match.group(1)
    format_str = match.group(2)
    return f"{var_name}|safe_datetime('{format_str}')"

def fix_template_file(file_path):
    """修复单个模板文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查文件是否包含 strftime 调用
    if '.strftime(' not in content:
        print(f"跳过文件 {file_path}，未找到 strftime 调用")
        return 0
    
    # 替换 strftime 调用
    new_content = re.sub(STRFTIME_PATTERN, replace_strftime, content)
    
    # 如果内容有变化，写回文件
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        # 计算替换次数
        count = len(re.findall(STRFTIME_PATTERN, content))
        print(f"修复文件 {file_path}，替换了 {count} 处 strftime 调用")
        return count
    
    return 0

def fix_all_templates():
    """修复所有模板文件"""
    total_files = 0
    total_fixes = 0
    
    # 遍历模板目录
    for root, dirs, files in os.walk(TEMPLATES_DIR):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                fixes = fix_template_file(file_path)
                if fixes > 0:
                    total_files += 1
                    total_fixes += fixes
    
    print(f"\n总计修复了 {total_files} 个文件中的 {total_fixes} 处 strftime 调用")

if __name__ == '__main__':
    print("开始修复模板文件中的 strftime 错误...")
    fix_all_templates()
    print("修复完成！")
