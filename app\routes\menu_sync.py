"""
菜单同步路由

提供菜单数据同步功能，包括从周菜单或日菜单同步到工作日志。
"""

from flask import Blueprint, request, jsonify, current_app, flash, redirect, url_for, render_template
from flask_login import login_required, current_user
from app.models import WeeklyMenu, MenuPlan, AdministrativeArea
from app.utils.permissions import check_permission
from app.services.menu_sync_service import MenuSyncService
from datetime import datetime, timedelta

menu_sync_bp = Blueprint('menu_sync', __name__)


@menu_sync_bp.route('/menu-sync', methods=['GET'])
@login_required
@check_permission('weekly_menu', 'view')
def index():
    """菜单同步页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取最近的周菜单
    recent_weekly_menus = []
    for area in accessible_areas:
        weekly_menus = WeeklyMenu.query.filter_by(area_id=area.id).order_by(WeeklyMenu.week_start.desc()).limit(5).all()
        for menu in weekly_menus:
            recent_weekly_menus.append({
                'id': menu.id,
                'area_name': area.name,
                'week_start': menu.week_start.strftime('%Y-%m-%d'),
                'week_end': menu.week_end.strftime('%Y-%m-%d'),
                'status': menu.status
            })

    # 获取最近的日菜单
    recent_menu_plans = []
    for area in accessible_areas:
        menu_plans = MenuPlan.query.filter_by(area_id=area.id).order_by(MenuPlan.plan_date.desc()).limit(10).all()
        for plan in menu_plans:
            recent_menu_plans.append({
                'id': plan.id,
                'area_name': area.name,
                'plan_date': plan.plan_date.strftime('%Y-%m-%d'),
                'meal_type': plan.meal_type,
                'status': plan.status
            })

    return render_template('menu_sync/index.html',
                          areas=accessible_areas,
                          recent_weekly_menus=recent_weekly_menus,
                          recent_menu_plans=recent_menu_plans)


@menu_sync_bp.route('/menu-sync/weekly/<int:weekly_menu_id>', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def sync_weekly_menu(weekly_menu_id):
    """同步周菜单数据到工作日志"""
    weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)

    # 检查用户是否有权限
    if not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限同步该周菜单', 'danger')
        return redirect(url_for('menu_sync.index'))

    # 同步数据
    try:
        sync_result = MenuSyncService.sync_weekly_menu_to_daily_logs(weekly_menu_id)
        if sync_result['success']:
            results = sync_result['results']
            current_app.logger.info(f"同步周菜单到工作日志成功: 更新={results['updated_logs']}, 创建={results['created_logs']}, 跳过={results['skipped_logs']}")
            flash(f"周菜单同步成功（更新{results['updated_logs']}条，创建{results['created_logs']}条，跳过{results['skipped_logs']}条）", 'success')
        else:
            current_app.logger.error(f"同步周菜单到工作日志失败: {sync_result['message']}")
            flash(f"周菜单同步失败: {sync_result['message']}", 'danger')
    except Exception as e:
        current_app.logger.error(f"同步周菜单到工作日志时出错: {str(e)}")
        flash(f"周菜单同步出错: {str(e)}", 'danger')

    return redirect(url_for('menu_sync.index'))


@menu_sync_bp.route('/menu-sync/daily/<int:menu_plan_id>', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def sync_menu_plan(menu_plan_id):
    """同步日菜单数据到工作日志"""
    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限同步该日菜单', 'danger')
        return redirect(url_for('menu_sync.index'))

    # 同步数据
    try:
        sync_result = MenuSyncService.sync_menu_plan_to_daily_log(menu_plan_id)
        if sync_result['success']:
            current_app.logger.info(f"同步日菜单到工作日志成功: {sync_result['message']}")
            flash(f"日菜单同步成功: {sync_result['message']}", 'success')
        else:
            current_app.logger.error(f"同步日菜单到工作日志失败: {sync_result['message']}")
            flash(f"日菜单同步失败: {sync_result['message']}", 'danger')
    except Exception as e:
        current_app.logger.error(f"同步日菜单到工作日志时出错: {str(e)}")
        flash(f"日菜单同步出错: {str(e)}", 'danger')

    return redirect(url_for('menu_sync.index'))


@menu_sync_bp.route('/menu-sync/area/<int:area_id>', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def sync_area(area_id):
    """同步指定区域的所有菜单数据到工作日志"""
    # 检查用户是否有权限
    if not current_user.can_access_area_by_id(area_id):
        flash('您没有权限同步该区域的菜单', 'danger')
        return redirect(url_for('menu_sync.index'))

    # 获取日期范围
    start_date_str = request.form.get('start_date')
    end_date_str = request.form.get('end_date')

    if not start_date_str or not end_date_str:
        flash('请提供开始日期和结束日期', 'danger')
        return redirect(url_for('menu_sync.index'))

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    except ValueError:
        flash('日期格式无效，请使用YYYY-MM-DD格式', 'danger')
        return redirect(url_for('menu_sync.index'))

    # 获取指定日期范围内的周菜单
    weekly_menus = WeeklyMenu.query.filter(
        WeeklyMenu.area_id == area_id,
        WeeklyMenu.week_start <= end_date,
        WeeklyMenu.week_end >= start_date,
        WeeklyMenu.status == '已发布'
    ).all()

    # 获取指定日期范围内的日菜单
    menu_plans = MenuPlan.query.filter(
        MenuPlan.area_id == area_id,
        MenuPlan.plan_date >= start_date,
        MenuPlan.plan_date <= end_date,
        MenuPlan.status.in_(['已发布', '已执行'])
    ).all()

    # 同步数据
    weekly_sync_results = []
    daily_sync_results = []

    # 同步周菜单
    for weekly_menu in weekly_menus:
        try:
            sync_result = MenuSyncService.sync_weekly_menu_to_daily_logs(weekly_menu.id)
            if sync_result['success']:
                results = sync_result['results']
                weekly_sync_results.append({
                    'id': weekly_menu.id,
                    'week_start': weekly_menu.week_start.strftime('%Y-%m-%d'),
                    'week_end': weekly_menu.week_end.strftime('%Y-%m-%d'),
                    'updated': results['updated_logs'],
                    'created': results['created_logs'],
                    'skipped': results['skipped_logs'],
                    'success': True
                })
            else:
                weekly_sync_results.append({
                    'id': weekly_menu.id,
                    'week_start': weekly_menu.week_start.strftime('%Y-%m-%d'),
                    'week_end': weekly_menu.week_end.strftime('%Y-%m-%d'),
                    'message': sync_result['message'],
                    'success': False
                })
        except Exception as e:
            weekly_sync_results.append({
                'id': weekly_menu.id,
                'week_start': weekly_menu.week_start.strftime('%Y-%m-%d'),
                'week_end': weekly_menu.week_end.strftime('%Y-%m-%d'),
                'message': str(e),
                'success': False
            })

    # 同步日菜单
    for menu_plan in menu_plans:
        try:
            sync_result = MenuSyncService.sync_menu_plan_to_daily_log(menu_plan.id)
            daily_sync_results.append({
                'id': menu_plan.id,
                'plan_date': menu_plan.plan_date.strftime('%Y-%m-%d'),
                'meal_type': menu_plan.meal_type,
                'message': sync_result['message'] if 'message' in sync_result else '',
                'success': sync_result['success']
            })
        except Exception as e:
            daily_sync_results.append({
                'id': menu_plan.id,
                'plan_date': menu_plan.plan_date.strftime('%Y-%m-%d'),
                'meal_type': menu_plan.meal_type,
                'message': str(e),
                'success': False
            })

    # 统计结果
    total_updated = sum([r['updated'] for r in weekly_sync_results if 'updated' in r])
    total_created = sum([r['created'] for r in weekly_sync_results if 'created' in r])
    total_skipped = sum([r['skipped'] for r in weekly_sync_results if 'skipped' in r])
    total_success = sum([1 for r in weekly_sync_results + daily_sync_results if r['success']])
    total_failed = sum([1 for r in weekly_sync_results + daily_sync_results if not r['success']])

    flash(f"同步完成：成功{total_success}个，失败{total_failed}个，更新{total_updated}条，创建{total_created}条，跳过{total_skipped}条", 'success' if total_failed == 0 else 'warning')
    return redirect(url_for('menu_sync.index'))
