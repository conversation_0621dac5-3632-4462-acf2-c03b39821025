from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.models import SupplierCategory, Supplier
from app.forms.supplier import SupplierCategoryForm
from app.utils.log_activity import log_activity
from datetime import datetime

supplier_category_bp = Blueprint('supplier_category', __name__)

@supplier_category_bp.route('/')
@login_required
def index():
    """供应商分类列表页面"""
    categories = SupplierCategory.query.order_by(SupplierCategory.id).all()
    return render_template('supplier/category_index.html', categories=categories, title='供应商分类管理', now=datetime.now())

@supplier_category_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """添加供应商分类"""
    form = SupplierCategoryForm()

    if form.validate_on_submit():
        category = SupplierCategory(
            name=form.name.data,
            description=form.description.data
        )
        db.session.add(category)

        # 添加审计日志
        log_activity(
            action='create',
            resource_type='SupplierCategory',
            resource_id=category.id,
            details={
                'name': category.name,
                'description': category.description
            }
        )

        db.session.commit()
        flash('供应商分类添加成功！', 'success')
        return redirect(url_for('supplier_category.index'))

    return render_template('supplier/category_form.html', form=form, title='添加供应商分类', now=datetime.now())

@supplier_category_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑供应商分类"""
    category = SupplierCategory.query.get_or_404(id)
    form = SupplierCategoryForm(obj=category)

    if form.validate_on_submit():
        old_data = {
            'name': category.name,
            'description': category.description
        }

        category.name = form.name.data
        category.description = form.description.data

        # 添加审计日志
        log_activity(
            action='update',
            resource_type='SupplierCategory',
            resource_id=category.id,
            details={
                'old': old_data,
                'new': {
                    'name': category.name,
                    'description': category.description
                }
            }
        )

        db.session.commit()
        flash('供应商分类更新成功！', 'success')
        return redirect(url_for('supplier_category.index'))

    return render_template('supplier/category_form.html', form=form, category=category, title='编辑供应商分类', now=datetime.now())

@supplier_category_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除供应商分类"""
    category = SupplierCategory.query.get_or_404(id)

    # 检查是否有关联的供应商
    if Supplier.query.filter_by(category_id=id).first():
        return jsonify({'success': 0, 'message': '该分类下有关联的供应商，不能删除！'})

    # 添加审计日志
    log_activity(
        action='delete',
        resource_type='SupplierCategory',
        resource_id=category.id,
        details={
            'name': category.name,
            'description': category.description
        }
    )

    db.session.delete(category)
    db.session.commit()

    return jsonify({'success': 1, 'message': '供应商分类删除成功！'})

@supplier_category_bp.route('/api')
@login_required
def api_list():
    """供应商分类API"""
    categories = SupplierCategory.query.all()
    return jsonify([{
        'id': c.id,
        'name': c.name,
        'description': c.description
    } for c in categories])
