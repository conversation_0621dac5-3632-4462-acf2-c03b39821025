from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import FoodSample, MenuPlan, MenuRecipe, Recipe, AdministrativeArea, User
from app import db
from datetime import datetime, date, timedelta
import json
import uuid
import os

food_sample_bp = Blueprint('food_sample', __name__)

@food_sample_bp.route('/food-sample')
@login_required
def index():
    """留样记录列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    meal_type = request.args.get('meal_type', '')

    # 构建查询
    query = FoodSample.query.filter(FoodSample.area_id.in_(area_ids))

    # 应用过滤条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    if status:
        query = query.filter_by(status=status)
    if meal_type:
        query = query.filter_by(meal_type=meal_type)
    if start_date:
        query = query.filter(FoodSample.meal_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(FoodSample.meal_date <= datetime.strptime(end_date, '%Y-%m-%d'))

    # 按留样开始时间降序排序
    query = query.order_by(FoodSample.start_time.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    food_samples = pagination.items

    # 获取区域信息，用于过滤
    areas = AdministrativeArea.query.filter(AdministrativeArea.id.in_(area_ids)).all()

    return render_template('food_sample/index.html',
                          food_samples=food_samples,
                          pagination=pagination,
                          areas=areas,
                          area_id=area_id,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          meal_type=meal_type)

@food_sample_bp.route('/food-sample/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建留样记录"""
    if request.method == 'POST':
        # 获取表单数据
        recipe_id = request.form.get('recipe_id', type=int)
        area_id = request.form.get('area_id', type=int)
        menu_plan_id = request.form.get('menu_plan_id', type=int)
        meal_date = request.form.get('meal_date')
        meal_type = request.form.get('meal_type')
        sample_quantity = request.form.get('sample_quantity', type=float)
        sample_unit = request.form.get('sample_unit')
        storage_location = request.form.get('storage_location')
        storage_temperature = request.form.get('storage_temperature')
        start_time = request.form.get('start_time')

        # 验证数据
        if not recipe_id or not area_id or not meal_date or not meal_type or not storage_location or not start_time:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('food_sample.create'))

        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限操作该区域', 'danger')
            return redirect(url_for('food_sample.index'))

        # 处理图片上传
        sample_image = ''
        if 'sample_image' in request.files and request.files['sample_image'].filename:
            file = request.files['sample_image']
            filename = str(uuid.uuid4()) + os.path.splitext(file.filename)[1]
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'food_samples', filename)

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=1)

            file.save(file_path)
            sample_image = 'uploads/food_samples/' + filename

        # 计算留样结束时间（通常为48小时后）
        start_time_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
        end_time_dt = start_time_dt + timedelta(hours=48)

        # 生成留样编号
        date_code = datetime.strptime(meal_date, '%Y-%m-%d').strftime('%Y%m%d')
        meal_code = '01' if meal_type == '早餐' else ('02' if meal_type == '午餐' else '03')
        sample_number = f"LS-{date_code}-{meal_code}-{recipe_id}"

        # 创建留样记录
        food_sample = FoodSample(
            sample_number=sample_number,
            recipe_id=recipe_id,
            area_id=area_id,
            menu_plan_id=menu_plan_id,
            meal_date=datetime.strptime(meal_date, '%Y-%m-%d').date(),
            meal_type=meal_type,
            sample_image=sample_image,
            sample_quantity=sample_quantity,
            sample_unit=sample_unit,
            storage_location=storage_location,
            storage_temperature=storage_temperature,
            start_time=start_time_dt,
            end_time=end_time_dt,
            operator_id=current_user.id,
            status='已留样'
        )

        db.session.add(food_sample)
        db.session.commit()
        flash('留样记录创建成功', 'success')
        return redirect(url_for('food_sample.index'))

    # GET请求，显示创建表单
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取所有启用的食谱
    recipes = Recipe.query.filter_by(status=1).all()

    # 获取最近的菜单计划
    today = date.today()
    menu_plans = MenuPlan.query.filter(
        MenuPlan.area_id.in_([area.id for area in accessible_areas]),
        MenuPlan.plan_date >= today - timedelta(days=7),
        MenuPlan.plan_date <= today,
        MenuPlan.status.in_(['已发布', '已执行'])
    ).order_by(MenuPlan.plan_date.desc()).all()

    return render_template('food_sample/create.html',
                          areas=accessible_areas,
                          recipes=recipes,
                          menu_plans=menu_plans)

@food_sample_bp.route('/food-sample/<int:id>')
@login_required
def view(id):
    """查看留样记录详情"""
    food_sample = FoodSample.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(food_sample.area_id):
        flash('您没有权限查看该留样记录', 'danger')
        return redirect(url_for('food_sample.index'))

    return render_template('food_sample/view.html', food_sample=food_sample)

@food_sample_bp.route('/food-sample/<int:id>/destroy', methods=['POST'])
@login_required
def destroy(id):
    """销毁留样"""
    food_sample = FoodSample.query.get_or_404(id)

    # 检查用户是否有权限销毁
    if not current_user.can_access_area_by_id(food_sample.area_id):
        flash('您没有权限销毁该留样', 'danger')
        return redirect(url_for('food_sample.index'))

    # 只有已留样状态的记录可以销毁
    if food_sample.status != '已留样':
        flash('只有已留样状态的记录可以销毁', 'danger')
        return redirect(url_for('food_sample.view', id=id))

    # 更新状态
    food_sample.status = '已销毁'
    food_sample.destruction_time = datetime.now()
    food_sample.destruction_operator_id = current_user.id

    db.session.commit()
    flash('留样已销毁', 'success')
    return redirect(url_for('food_sample.view', id=id))

@food_sample_bp.route('/food-sample/menu-plan/<int:menu_plan_id>')
@login_required
def by_menu_plan(menu_plan_id):
    """根据菜单计划查看留样记录"""
    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限查看该菜单计划的留样记录', 'danger')
        return redirect(url_for('food_sample.index'))

    # 获取该菜单计划的所有留样记录
    food_samples = FoodSample.query.filter_by(menu_plan_id=menu_plan_id).all()

    return render_template('food_sample/by_menu_plan.html',
                          menu_plan=menu_plan,
                          food_samples=food_samples)

@food_sample_bp.route('/food-sample/print/<int:id>')
@login_required
def print_sample(id):
    """打印留样记录"""
    food_sample = FoodSample.query.get_or_404(id)

    # 检查用户是否有权限打印
    if not current_user.can_access_area_by_id(food_sample.area_id):
        flash('您没有权限打印该留样记录', 'danger')
        return redirect(url_for('food_sample.index'))

    # 获取项目名称
    from app.models_system import SystemSetting
    project_name = SystemSetting.get_value('project_name', '初中毕业生学生去向管理系统')
    return render_template('food_sample/print.html', food_sample=food_sample, project_name=project_name)

@food_sample_bp.route('/food-sample/print-daily')
@login_required
def print_daily():
    """打印当日留样记录"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    meal_date = request.args.get('meal_date', date.today().strftime('%Y-%m-%d'))

    # 构建查询
    query = FoodSample.query

    # 应用过滤条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    else:
        query = query.filter(FoodSample.area_id.in_(area_ids))

    query = query.filter(FoodSample.meal_date == datetime.strptime(meal_date, '%Y-%m-%d'))

    # 按餐次排序
    food_samples = query.order_by(FoodSample.meal_type).all()

    # 获取区域信息
    area = None
    if area_id:
        area = AdministrativeArea.query.get(area_id)

    # 获取项目名称
    from app.models_system import SystemSetting
    project_name = SystemSetting.get_value('project_name', '初中毕业生学生去向管理系统')

    return render_template('food_sample/print_daily.html',
                          food_samples=food_samples,
                          meal_date=meal_date,
                          area=area,
                          project_name=project_name)
