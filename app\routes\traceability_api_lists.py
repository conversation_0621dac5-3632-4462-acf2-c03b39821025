from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required, current_user
from app.models import (
    Ingredient, Supplier, AdministrativeArea, User, AuditLog,
    StockIn, StockInItem, StockOut, StockOutItem, Inventory,
    ConsumptionPlan, ConsumptionDetail, MenuPlan, MenuRecipe, Recipe
)
from app import db
from datetime import datetime, date, timedelta
from sqlalchemy import func, text

traceability_api_lists_bp = Blueprint('traceability_api_lists', __name__)

@traceability_api_lists_bp.route('/api/menu-plans', methods=['GET'])
@login_required
def get_menu_plans():
    """获取菜单计划列表"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取菜单计划列表
    menu_plans = MenuPlan.query.filter(
        MenuPlan.area_id.in_(area_ids)
    ).order_by(MenuPlan.plan_date.desc()).limit(50).all()
    
    # 构建结果
    result = []
    for plan in menu_plans:
        result.append({
            'id': plan.id,
            'text': f'菜单计划 #{plan.id} ({plan.plan_date.strftime("%Y-%m-%d")} {plan.meal_type})'
        })
    
    return jsonify(result)

@traceability_api_lists_bp.route('/api/consumption-plans', methods=['GET'])
@login_required
def get_consumption_plans():
    """获取消耗计划列表"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取消耗计划列表
    # 由于消耗计划可能没有直接关联区域，我们通过菜单计划关联
    consumption_plans = db.session.query(ConsumptionPlan).join(
        MenuPlan, ConsumptionPlan.menu_plan_id == MenuPlan.id
    ).filter(
        MenuPlan.area_id.in_(area_ids)
    ).order_by(ConsumptionPlan.created_at.desc()).limit(50).all()
    
    # 构建结果
    result = []
    for plan in consumption_plans:
        menu_plan = plan.menu_plan
        date_str = ""
        meal_type = ""
        if menu_plan:
            date_str = menu_plan.plan_date.strftime("%Y-%m-%d")
            meal_type = menu_plan.meal_type
        else:
            # 如果没有关联菜单计划，尝试从消耗计划本身获取日期和餐次
            if hasattr(plan, 'consumption_date') and plan.consumption_date:
                date_str = plan.consumption_date.strftime("%Y-%m-%d")
            if hasattr(plan, 'meal_type') and plan.meal_type:
                meal_type = plan.meal_type
        
        result.append({
            'id': plan.id,
            'text': f'消耗计划 #{plan.id} ({date_str} {meal_type})'
        })
    
    return jsonify(result)

@traceability_api_lists_bp.route('/api/stock-outs', methods=['GET'])
@login_required
def get_stock_outs():
    """获取出库单列表"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取出库单列表
    stock_outs = db.session.query(StockOut).join(
        Warehouse, StockOut.warehouse_id == Warehouse.id
    ).filter(
        Warehouse.area_id.in_(area_ids)
    ).order_by(StockOut.stock_out_date.desc()).limit(50).all()
    
    # 构建结果
    result = []
    for stock_out in stock_outs:
        result.append({
            'id': stock_out.id,
            'text': f'出库单 #{stock_out.stock_out_number}'
        })
    
    return jsonify(result)

@traceability_api_lists_bp.route('/api/ingredients', methods=['GET'])
@login_required
def get_ingredients():
    """获取食材列表"""
    # 获取食材列表
    ingredients = Ingredient.query.order_by(Ingredient.name).all()
    
    # 构建结果
    result = []
    for ingredient in ingredients:
        category_name = ingredient.category.name if ingredient.category else ""
        result.append({
            'id': ingredient.id,
            'text': f'{ingredient.name} ({category_name})'
        })
    
    return jsonify(result)
