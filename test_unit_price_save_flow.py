#!/usr/bin/env python3
"""
测试单价保存完整流程的脚本
"""

import requests
import sys
import json
from bs4 import BeautifulSoup

def test_unit_price_save_flow():
    """测试单价保存的完整流程"""
    print("🧪 测试单价保存完整流程...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76  # 测试用的入库单ID
    
    try:
        # 第一步：获取批次编辑器页面
        editor_url = f"{base_url}/stock-in/{stock_in_id}/batch-editor-simplified"
        print(f"📤 1. 获取批次编辑器页面: {editor_url}")
        
        response = requests.get(editor_url, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ 无法访问批次编辑器页面，状态码: {response.status_code}")
            return False
        
        print("✅ 批次编辑器页面访问成功")
        
        # 解析HTML内容
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找表单
        form = soup.find('form', {'id': 'batchEditForm'})
        if not form:
            print("❌ 未找到批次编辑表单")
            return False
        
        # 获取CSRF token
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        if not csrf_token:
            print("❌ 未找到CSRF token")
            return False
        
        csrf_value = csrf_token.get('value')
        print(f"✅ 获取到CSRF token: {csrf_value[:10]}...")
        
        # 查找复选框和对应的输入框
        checkboxes = form.find_all('input', {'class': 'batch-checkbox'})
        print(f"📋 找到 {len(checkboxes)} 个批次项目")
        
        if len(checkboxes) == 0:
            print("⚠️ 没有找到可编辑的批次项目")
            return True
        
        # 选择第一个项目进行测试
        first_checkbox = checkboxes[0]
        item_id = first_checkbox.get('value')
        print(f"📋 选择项目ID: {item_id} 进行测试")
        
        # 查找对应的输入框
        unit_price_input = form.find('input', {'name': f'unit_price_{item_id}'})
        if not unit_price_input:
            print(f"❌ 未找到单价输入框: unit_price_{item_id}")
            return False
        
        current_unit_price = unit_price_input.get('value', '0')
        print(f"📋 当前单价: {current_unit_price}")
        
        # 设置新的单价
        new_unit_price = "19999.99"
        print(f"📋 设置新单价: {new_unit_price}")
        
        # 第二步：模拟表单提交
        save_url = f"{base_url}/stock-in/{stock_in_id}/save-batch-edit-simplified"
        print(f"📤 2. 提交保存请求: {save_url}")
        
        # 构建表单数据
        form_data = {
            'csrf_token': csrf_value,
            'selected_items[]': [item_id],
            f'supplier_id_{item_id}': '1',  # 假设供应商ID为1
            f'storage_location_id_{item_id}': '1',  # 假设存储位置ID为1
            f'quantity_{item_id}': '10.0',
            f'unit_price_{item_id}': new_unit_price,  # 新的单价
            f'production_date_{item_id}': '2025-05-25',
            f'expiry_date_{item_id}': '2025-06-24',
        }
        
        print("📋 提交的表单数据:")
        for key, value in form_data.items():
            if 'unit_price' in key:
                print(f"  ✅ {key}: {value} (单价字段)")
            else:
                print(f"  📝 {key}: {value}")
        
        # 发送POST请求
        headers = {
            'X-Requested-With': 'XMLHttpRequest',  # 标识为AJAX请求
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        save_response = requests.post(save_url, data=form_data, headers=headers, timeout=30)
        
        print(f"📋 保存响应状态码: {save_response.status_code}")
        
        if save_response.status_code == 200:
            try:
                response_json = save_response.json()
                print(f"📋 保存响应内容: {response_json}")
                
                if response_json.get('success'):
                    print("✅ 保存成功")
                    
                    # 第三步：验证保存结果
                    print("📤 3. 验证保存结果...")
                    
                    # 重新获取批次编辑器页面，检查单价是否已更新
                    verify_response = requests.get(editor_url, timeout=30)
                    if verify_response.status_code == 200:
                        verify_soup = BeautifulSoup(verify_response.text, 'html.parser')
                        verify_form = verify_soup.find('form', {'id': 'batchEditForm'})
                        
                        if verify_form:
                            verify_unit_price_input = verify_form.find('input', {'name': f'unit_price_{item_id}'})
                            if verify_unit_price_input:
                                updated_unit_price = verify_unit_price_input.get('value', '0')
                                print(f"📋 验证后的单价: {updated_unit_price}")
                                
                                if updated_unit_price == new_unit_price:
                                    print("🎉 单价保存成功！")
                                    return True
                                else:
                                    print(f"❌ 单价保存失败，期望: {new_unit_price}，实际: {updated_unit_price}")
                                    return False
                            else:
                                print("❌ 验证时未找到单价输入框")
                                return False
                        else:
                            print("❌ 验证时未找到表单")
                            return False
                    else:
                        print("❌ 验证时无法访问页面")
                        return False
                else:
                    print(f"❌ 保存失败: {response_json.get('message', '未知错误')}")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ 保存响应不是有效的JSON格式")
                print(f"响应内容: {save_response.text[:200]}...")
                return False
        else:
            print(f"❌ 保存请求失败，状态码: {save_response.status_code}")
            print(f"响应内容: {save_response.text[:200]}...")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code in [200, 302]:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def provide_manual_test_steps():
    """提供手动测试步骤"""
    print("\n📋 手动测试步骤:")
    print("="*60)
    
    print("\n1. 🌐 打开浏览器访问:")
    print("   http://127.0.0.1:5000/stock-in/76/batch-editor-simplified")
    
    print("\n2. 🔧 打开浏览器开发者工具 (F12):")
    print("   - 切换到 Network 标签页")
    print("   - 切换到 Console 标签页")
    
    print("\n3. ✅ 勾选一个批次项目:")
    print("   - 点击复选框，确保行变为绿色")
    print("   - 输入框应该变为可编辑状态")
    
    print("\n4. 💰 修改单价:")
    print("   - 在单价输入框中输入: 19999.99")
    print("   - 确保其他必填字段也有值（供应商、存储位置、日期等）")
    
    print("\n5. 💾 点击保存按钮:")
    print("   - 观察 Network 标签页中的请求")
    print("   - 查看 Console 中的调试信息")
    print("   - 确认请求包含 unit_price_XX 字段")
    
    print("\n6. 🔍 检查保存结果:")
    print("   - 页面应该跳转到详情页面")
    print("   - 或者重新访问编辑器页面")
    print("   - 确认单价值已更新")
    
    print("\n💡 关键检查点:")
    print("   ✅ 复选框必须勾选")
    print("   ✅ 单价输入框必须有值")
    print("   ✅ 表单提交时包含 unit_price 字段")
    print("   ✅ 服务器返回成功响应")
    print("   ✅ 数据库中的值已更新")

def main():
    """主函数"""
    print("🚀 开始测试单价保存完整流程\n")
    
    # 检查服务器连接
    if not test_server_connection():
        print("\n❌ 无法连接到服务器，请先启动Flask应用")
        return False
    
    print()
    
    # 运行完整流程测试
    success = test_unit_price_save_flow()
    
    # 输出测试结果
    print(f"\n{'='*60}")
    print("📊 测试结果")
    print('='*60)
    
    if success:
        print("🎉 单价保存流程测试通过！")
        print("\n✅ 确认:")
        print("  - 批次编辑器页面正常访问")
        print("  - 表单数据正确提交")
        print("  - 单价字段成功保存")
        print("  - 数据库更新正确")
    else:
        print("❌ 单价保存流程测试失败！")
        print("\n⚠️ 可能的问题:")
        print("  - 表单提交时单价字段丢失")
        print("  - 服务器端处理逻辑有问题")
        print("  - 数据库更新失败")
        print("  - 前端JavaScript逻辑错误")
    
    # 提供手动测试步骤
    provide_manual_test_steps()
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
