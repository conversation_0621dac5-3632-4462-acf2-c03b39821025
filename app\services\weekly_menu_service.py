"""
周菜单服务模块
提供周菜单相关的业务逻辑处理
"""

from flask import current_app
from app import db
from app.models import WeeklyMenu, WeeklyMenuRecipe, Recipe, AdministrativeArea
from datetime import datetime, date, timedelta
from sqlalchemy import text
import json

class WeeklyMenuError(Exception):
    """周菜单操作异常"""
    pass

class WeeklyMenuService:
    """周菜单服务类"""

    @staticmethod
    def get_menu(area_id, week_start):
        """
        获取指定区域和周次的菜单

        参数:
            area_id: 区域ID
            week_start: 周开始日期，可以是日期对象或字符串

        返回:
            WeeklyMenu对象，如果不存在则返回None
        """
        try:
            current_app.logger.info(f"获取周菜单: area_id={area_id}, week_start={week_start}, 类型={type(week_start)}")

            # 如果week_start是字符串，转换为日期对象
            if isinstance(week_start, str):
                try:
                    week_start_date = datetime.strptime(week_start, '%Y-%m-%d').date()
                    current_app.logger.info(f"转换后的日期对象: {week_start_date}, 类型={type(week_start_date)}")
                except ValueError as e:
                    current_app.logger.error(f"日期格式转换失败: {str(e)}")
                    raise WeeklyMenuError(f"日期格式错误: {week_start}, 应为YYYY-MM-DD格式")
            elif isinstance(week_start, datetime):
                week_start_date = week_start.date()
                current_app.logger.info(f"从datetime转换为date: {week_start_date}")
            else:
                week_start_date = week_start

            # 将日期转换为字符串格式，避免精度问题
            week_start_str = week_start_date.strftime('%Y-%m-%d') if hasattr(week_start_date, 'strftime') else str(week_start_date)
            current_app.logger.info(f"使用日期字符串: {week_start_str}")

            # 使用原始SQL查询，避免SQLAlchemy的参数绑定问题
            try:
                from sqlalchemy import text

                sql = text("""
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                """)

                current_app.logger.info(f"执行SQL: {sql}")
                current_app.logger.info(f"SQL参数: area_id={area_id}, week_start_str={week_start_str}")

                result = db.session.execute(sql, {'area_id': area_id, 'week_start_str': week_start_str})
                row = result.fetchone()

                if row:
                    current_app.logger.info(f"SQL查询成功，找到菜单: id={row.id}")

                    # 使用ORM获取完整对象
                    menu = WeeklyMenu.query.get(row.id)
                    if menu:
                        current_app.logger.info(f"通过ID获取完整菜单对象成功: id={menu.id}")
                        return menu
                    else:
                        current_app.logger.warning(f"通过ID获取完整菜单对象失败: id={row.id}")

                        # 手动构建WeeklyMenu对象
                        menu = WeeklyMenu(
                            id=row.id,
                            area_id=row.area_id,
                            week_start=row.week_start,
                            week_end=row.week_end,
                            status=row.status,
                            created_by=row.created_by
                        )
                        menu.created_at = row.created_at
                        menu.updated_at = row.updated_at

                        current_app.logger.info(f"手动构建菜单对象成功: id={menu.id}")
                        return menu
                else:
                    current_app.logger.info(f"SQL查询未找到菜单: area_id={area_id}, week_start_str={week_start_str}")
                    return None

            except Exception as sql_error:
                current_app.logger.error(f"原始SQL查询失败: {str(sql_error)}")
                current_app.logger.error(f"错误类型: {type(sql_error).__name__}")

                # 如果原始SQL失败，尝试使用ORM方式
                current_app.logger.info("尝试使用ORM方式查询")

                try:
                    # 使用字符串比较来避免日期精度问题
                    from sqlalchemy import func

                    menu = WeeklyMenu.query.filter(
                        WeeklyMenu.area_id == area_id,
                        func.convert(db.String, WeeklyMenu.week_start, 120).like(f"{week_start_str}%")
                    ).first()

                    if menu:
                        current_app.logger.info(f"ORM查询找到菜单: id={menu.id}")
                    else:
                        current_app.logger.info("ORM查询未找到菜单")

                    return menu
                except Exception as orm_error:
                    current_app.logger.error(f"ORM查询也失败: {str(orm_error)}")
                    raise

        except Exception as e:
            current_app.logger.error(f"获取周菜单失败: {str(e)}")
            raise WeeklyMenuError(f"获取周菜单失败: {str(e)}")

    @staticmethod
    def create_menu(area_id, week_start, created_by):
        """
        创建新的周菜单

        参数:
            area_id: 区域ID
            week_start: 周开始日期，可以是日期对象或字符串
            created_by: 创建者ID

        返回:
            新创建的菜单ID
        """
        try:
            current_app.logger.info(f"开始创建周菜单: area_id={area_id}, week_start={week_start}, created_by={created_by}")

            # 如果week_start是字符串，转换为日期对象
            if isinstance(week_start, str):
                try:
                    week_start_date = datetime.strptime(week_start, '%Y-%m-%d').date()
                    current_app.logger.info(f"转换后的日期对象: {week_start_date}, 类型={type(week_start_date)}")
                except ValueError as e:
                    current_app.logger.error(f"日期格式转换失败: {str(e)}")
                    raise WeeklyMenuError(f"日期格式错误: {week_start}, 应为YYYY-MM-DD格式")
            elif isinstance(week_start, datetime):
                week_start_date = week_start.date()
                current_app.logger.info(f"从datetime转换为date: {week_start_date}")
            else:
                week_start_date = week_start

            # 计算周结束日期
            week_end_date = week_start_date + timedelta(days=6)
            current_app.logger.info(f"计算的周结束日期: {week_end_date}")

            # 检查是否已存在该周的菜单
            current_app.logger.info(f"检查是否已存在该周的菜单: area_id={area_id}, week_start={week_start_date}")
            existing_menu = WeeklyMenuService.get_menu(area_id, week_start_date)
            if existing_menu:
                current_app.logger.info(f"已存在该周的菜单: id={existing_menu.id}")
                return existing_menu.id

            # 将日期转换为字符串格式，避免精度问题
            week_start_str = week_start_date.strftime('%Y-%m-%d')
            week_end_str = week_end_date.strftime('%Y-%m-%d')
            current_app.logger.info(f"使用日期字符串: week_start_str={week_start_str}, week_end_str={week_end_str}")

            # 使用原始SQL创建菜单
            current_app.logger.info("准备执行SQL创建菜单")
            insert_sql = text("""
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
            """)

            # 准备参数
            params = {
                'area_id': area_id,
                'week_start_str': week_start_str,
                'week_end_str': week_end_str,
                'status': '计划中',
                'created_by': created_by
            }
            current_app.logger.info(f"SQL参数: {params}")

            try:
                # 记录SQL语句
                current_app.logger.info(f"执行SQL: {insert_sql}")

                # 执行SQL
                result = db.session.execute(insert_sql, params)
                weekly_menu_id = result.scalar()

                if weekly_menu_id:
                    current_app.logger.info(f"SQL执行成功，获取到ID: {weekly_menu_id}")
                else:
                    current_app.logger.warning("SQL执行成功但未返回ID")

                # 提交事务前检查连接状态
                current_app.logger.info("检查数据库连接状态")
                try:
                    db.session.execute(text("SELECT 1"))
                    current_app.logger.info("数据库连接正常")
                except Exception as conn_error:
                    current_app.logger.error(f"数据库连接异常: {str(conn_error)}")
                    raise

                # 提交事务
                db.session.commit()
                current_app.logger.info(f"事务提交成功")

                # 验证菜单是否真的创建成功
                verify_menu = WeeklyMenu.query.get(weekly_menu_id)
                if verify_menu:
                    current_app.logger.info(f"验证成功: 菜单已创建 ID={weekly_menu_id}")
                else:
                    current_app.logger.warning(f"验证失败: 无法查询到刚创建的菜单 ID={weekly_menu_id}")

                return weekly_menu_id
            except Exception as sql_error:
                db.session.rollback()
                current_app.logger.error(f"SQL执行或提交失败: {str(sql_error)}")
                current_app.logger.error(f"错误类型: {type(sql_error).__name__}")

                # 尝试使用ORM方式创建
                current_app.logger.info("尝试使用ORM方式创建菜单")
                try:
                    # 创建菜单对象，使用字符串日期并在数据库中转换
                    new_menu = WeeklyMenu(
                        area_id=area_id,
                        week_start=datetime.strptime(week_start_str, '%Y-%m-%d').replace(microsecond=0),
                        week_end=datetime.strptime(week_end_str, '%Y-%m-%d').replace(microsecond=0),
                        status='计划中',
                        created_by=created_by
                    )

                    # 记录菜单对象属性
                    current_app.logger.info(f"菜单对象属性: area_id={new_menu.area_id}, week_start={new_menu.week_start}, week_end={new_menu.week_end}, status={new_menu.status}, created_by={new_menu.created_by}")

                    # 添加到会话
                    db.session.add(new_menu)
                    current_app.logger.info("菜单对象已添加到会话")

                    # 提交事务
                    db.session.commit()
                    current_app.logger.info(f"ORM创建成功，ID: {new_menu.id}")

                    # 验证菜单是否真的创建成功
                    verify_menu = WeeklyMenu.query.get(new_menu.id)
                    if verify_menu:
                        current_app.logger.info(f"ORM验证成功: 菜单已创建 ID={new_menu.id}")
                    else:
                        current_app.logger.warning(f"ORM验证失败: 无法查询到刚创建的菜单 ID={new_menu.id}")

                    return new_menu.id
                except Exception as orm_error:
                    db.session.rollback()
                    current_app.logger.error(f"ORM创建也失败: {str(orm_error)}")
                    current_app.logger.error(f"ORM错误类型: {type(orm_error).__name__}")
                    raise WeeklyMenuError(f"创建周菜单失败: {str(orm_error)}")
        except Exception as e:
            db.session.rollback()
            current_app.logger.exception(f"创建周菜单过程中发生异常: {str(e)}")
            raise WeeklyMenuError(f"创建周菜单失败: {str(e)}")

    @staticmethod
    def save_menu(menu_id, menu_data):
        """
        保存周菜单数据

        参数:
            menu_id: 菜单ID
            menu_data: 菜单数据，格式为 {日期: {餐次: [菜品列表]}}

        返回:
            保存的菜单ID
        """
        try:
            # 获取菜单
            menu = WeeklyMenu.query.get(menu_id)
            if not menu:
                raise WeeklyMenuError("菜单不存在")

            # 开始事务
            db.session.begin_nested()

            # 导入副表模型
            from app.weekly_menu_recipes_temp import WeeklyMenuRecipesTemp

            # 获取现有的副表数据，用于补全主表中的recipe_id
            current_app.logger.info(f"获取副表数据用于补全主表: weekly_menu_id={menu_id}")
            temp_recipes_query = text("""
            SELECT day_of_week, meal_type, recipe_id, recipe_name
            FROM weekly_menu_recipes_temp
            WHERE weekly_menu_id = :weekly_menu_id
            """)

            temp_recipes_result = db.session.execute(temp_recipes_query, {'weekly_menu_id': menu_id})
            temp_recipes_map = {}

            # 构建副表数据映射，用于快速查找
            recipe_count = 0
            for row in temp_recipes_result:
                day_key = str(row.day_of_week)
                meal_key = row.meal_type
                recipe_name = row.recipe_name
                recipe_id = row.recipe_id

                if day_key not in temp_recipes_map:
                    temp_recipes_map[day_key] = {}
                if meal_key not in temp_recipes_map[day_key]:
                    temp_recipes_map[day_key][meal_key] = {}

                # 使用菜品名称作为键，存储recipe_id
                temp_recipes_map[day_key][meal_key][recipe_name] = recipe_id
                recipe_count += 1
                current_app.logger.info(f"副表数据: 日期={day_key}, 餐次={meal_key}, 菜品={recipe_name}, ID={recipe_id}")

            current_app.logger.info(f"副表数据映射构建完成: {len(temp_recipes_map)} 天, {recipe_count} 个菜品")

            # 补全主表数据中的recipe_id
            for date_str, meals in menu_data.items():
                # 计算星期几
                try:
                    day_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    day_of_week = day_date.weekday() + 1  # 0-6 转为 1-7
                    day_key = str(day_of_week)
                except ValueError:
                    current_app.logger.warning(f"跳过无效日期: {date_str}")
                    continue

                for meal_type, recipes in meals.items():
                    for i, recipe_data in enumerate(recipes):
                        recipe_name = recipe_data.get('recipe_name') or recipe_data.get('name')

                        # 检查recipe_id是否为空
                        if recipe_data.get('recipe_id') is None or recipe_data.get('recipe_id') == '' or recipe_data.get('recipe_id') == 'null' or recipe_data.get('recipe_id') == 'undefined':
                            current_app.logger.info(f"发现recipe_id为空的记录: 日期={day_key}, 餐次={meal_type}, 菜品={recipe_name}")

                            # 检查副表中是否有对应的菜品
                            if (day_key in temp_recipes_map and
                                meal_type in temp_recipes_map[day_key] and
                                recipe_name in temp_recipes_map[day_key][meal_type]):

                                # 从副表中获取recipe_id
                                recipe_id = temp_recipes_map[day_key][meal_type][recipe_name]

                                # 更新主表数据
                                recipe_data['recipe_id'] = recipe_id
                                current_app.logger.info(f"从副表补全recipe_id: 日期={day_key}, 餐次={meal_type}, 菜品={recipe_name}, ID={recipe_id}")
                            else:
                                current_app.logger.warning(f"副表中未找到对应记录，无法补全: 日期={day_key}, 餐次={meal_type}, 菜品={recipe_name}")

            # 统计补全情况
            total_recipes = 0
            empty_recipes = 0
            filled_recipes = 0

            for date_str, meals in menu_data.items():
                for meal_type, recipes in meals.items():
                    total_recipes += len(recipes)
                    for recipe in recipes:
                        if recipe.get('recipe_id') is None or recipe.get('recipe_id') == '' or recipe.get('recipe_id') == 'null' or recipe.get('recipe_id') == 'undefined':
                            empty_recipes += 1
                        else:
                            filled_recipes += 1

            current_app.logger.info(f"主表数据补全完成，准备保存: 总菜品数={total_recipes}, 已补全={filled_recipes}, 未补全={empty_recipes}")

            # 删除现有的菜单食谱（主表）
            delete_sql = text("""
            DELETE FROM weekly_menu_recipes
            WHERE weekly_menu_id = :weekly_menu_id
            """)

            db.session.execute(delete_sql, {'weekly_menu_id': menu_id})
            current_app.logger.info(f"删除现有菜单食谱(主表): weekly_menu_id={menu_id}")

            # 删除现有的菜单食谱（副表）
            delete_temp_sql = text("""
            DELETE FROM weekly_menu_recipes_temp
            WHERE weekly_menu_id = :weekly_menu_id
            """)

            db.session.execute(delete_temp_sql, {'weekly_menu_id': menu_id})
            current_app.logger.info(f"删除现有菜单食谱(副表): weekly_menu_id={menu_id}")

            # 添加新的菜单食谱
            for date_str, meals in menu_data.items():
                # 计算星期几
                try:
                    day_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    day_of_week = day_date.weekday() + 1  # 0-6 转为 1-7
                except ValueError:
                    current_app.logger.warning(f"跳过无效日期: {date_str}")
                    continue

                for meal_type, recipes in meals.items():
                    for recipe_data in recipes:
                        recipe_id = recipe_data.get('recipe_id')
                        recipe_name = recipe_data.get('recipe_name') or recipe_data.get('name')
                        is_custom = recipe_data.get('is_custom', False)

                        if not recipe_name:
                            continue

                        # 使用原始SQL插入菜单食谱关联（主表）
                        insert_recipe_sql = text("""
                        INSERT INTO weekly_menu_recipes
                        (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name)
                        VALUES
                        (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name)
                        """)

                        db.session.execute(
                            insert_recipe_sql,
                            {
                                'weekly_menu_id': menu_id,
                                'day_of_week': day_of_week,
                                'meal_type': meal_type,
                                'recipe_id': recipe_id,
                                'recipe_name': recipe_name
                            }
                        )

                        # 使用原始SQL插入菜单食谱关联（副表）
                        insert_temp_recipe_sql = text("""
                        INSERT INTO weekly_menu_recipes_temp
                        (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name, is_custom, temp_data)
                        VALUES
                        (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name, :is_custom, :temp_data)
                        """)

                        db.session.execute(
                            insert_temp_recipe_sql,
                            {
                                'weekly_menu_id': menu_id,
                                'day_of_week': day_of_week,
                                'meal_type': meal_type,
                                'recipe_id': recipe_id,
                                'recipe_name': recipe_name,
                                'is_custom': 1 if is_custom else 0,
                                'temp_data': json.dumps(recipe_data) if recipe_data else None
                            }
                        )

            # 提交事务
            db.session.commit()
            current_app.logger.info(f"保存周菜单成功(主表和副表): id={menu_id}")

            return menu_id
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"保存周菜单失败: {str(e)}")
            raise WeeklyMenuError(f"保存周菜单失败: {str(e)}")

    @staticmethod
    def publish_menu(menu_id):
        """
        发布周菜单

        参数:
            menu_id: 菜单ID

        返回:
            发布的菜单ID
        """
        try:
            # 获取菜单
            menu = WeeklyMenu.query.get(menu_id)
            if not menu:
                raise WeeklyMenuError("菜单不存在")

            # 检查菜单状态
            if menu.status == '已发布':
                return menu_id

            # 更新菜单状态
            menu.status = '已发布'
            menu.updated_at = datetime.now().replace(microsecond=0)

            db.session.commit()
            current_app.logger.info(f"发布周菜单成功: id={menu_id}")

            return menu_id
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"发布周菜单失败: {str(e)}")
            raise WeeklyMenuError(f"发布周菜单失败: {str(e)}")

    @staticmethod
    def unpublish_menu(menu_id):
        """
        取消发布周菜单

        参数:
            menu_id: 菜单ID

        返回:
            取消发布的菜单ID
        """
        try:
            # 获取菜单
            menu = WeeklyMenu.query.get(menu_id)
            if not menu:
                raise WeeklyMenuError("菜单不存在")

            # 检查菜单状态
            if menu.status != '已发布':
                return menu_id

            # 更新菜单状态
            menu.status = '计划中'
            menu.updated_at = datetime.now().replace(microsecond=0)

            db.session.commit()
            current_app.logger.info(f"取消发布周菜单成功: id={menu_id}")

            return menu_id
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"取消发布周菜单失败: {str(e)}")
            raise WeeklyMenuError(f"取消发布周菜单失败: {str(e)}")

    @staticmethod
    def copy_menu(source_menu_id, target_week_start, area_id):
        """
        复制周菜单

        参数:
            source_menu_id: 源菜单ID
            target_week_start: 目标周开始日期
            area_id: 区域ID

        返回:
            新创建的菜单ID
        """
        try:
            # 获取源菜单
            source_menu = WeeklyMenu.query.get(source_menu_id)
            if not source_menu:
                raise WeeklyMenuError("源菜单不存在")

            # 如果target_week_start是字符串，转换为日期对象
            if isinstance(target_week_start, str):
                try:
                    target_week_start_date = datetime.strptime(target_week_start, '%Y-%m-%d').date()
                    current_app.logger.info(f"转换后的目标日期对象: {target_week_start_date}, 类型={type(target_week_start_date)}")
                except ValueError as e:
                    current_app.logger.error(f"目标日期格式转换失败: {str(e)}")
                    raise WeeklyMenuError(f"目标日期格式错误: {target_week_start}, 应为YYYY-MM-DD格式")
            elif isinstance(target_week_start, datetime):
                target_week_start_date = target_week_start.date()
                current_app.logger.info(f"从datetime转换为date: {target_week_start_date}")
            else:
                target_week_start_date = target_week_start

            # 计算目标周结束日期
            target_week_end_date = target_week_start_date + timedelta(days=6)
            current_app.logger.info(f"计算的目标周结束日期: {target_week_end_date}")

            # 将日期转换为字符串格式，避免精度问题
            target_week_start_str = target_week_start_date.strftime('%Y-%m-%d')
            target_week_end_str = target_week_end_date.strftime('%Y-%m-%d')
            current_app.logger.info(f"使用目标日期字符串: target_week_start_str={target_week_start_str}, target_week_end_str={target_week_end_str}")

            # 检查是否已存在目标周的菜单
            existing_menu = WeeklyMenuService.get_menu(area_id, target_week_start_date)
            if existing_menu:
                current_app.logger.info(f"已存在目标周的菜单: id={existing_menu.id}")

                # 删除现有的菜单食谱
                delete_sql = text("""
                DELETE FROM weekly_menu_recipes
                WHERE weekly_menu_id = :weekly_menu_id
                """)

                db.session.execute(delete_sql, {'weekly_menu_id': existing_menu.id})
                current_app.logger.info(f"删除现有菜单食谱: weekly_menu_id={existing_menu.id}")

                target_menu_id = existing_menu.id
            else:
                current_app.logger.info("目标周不存在菜单，创建新菜单")

                # 创建新的菜单
                insert_sql = text("""
                INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
                OUTPUT inserted.id
                VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
                """)

                current_app.logger.info(f"执行SQL: {insert_sql}")

                result = db.session.execute(
                    insert_sql,
                    {
                        'area_id': area_id,
                        'week_start_str': target_week_start_str,
                        'week_end_str': target_week_end_str,
                        'status': '计划中',
                        'created_by': source_menu.created_by
                    }
                )

                target_menu_id = result.scalar()
                current_app.logger.info(f"创建新菜单成功: id={target_menu_id}")

            # 导入副表模型
            from app.weekly_menu_recipes_temp import WeeklyMenuRecipesTemp

            # 获取源菜单的食谱（主表）
            source_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=source_menu_id).all()

            # 获取源菜单的食谱（副表）
            source_temp_recipes = WeeklyMenuRecipesTemp.query.filter_by(weekly_menu_id=source_menu_id).all()

            # 复制食谱到目标菜单（主表）
            for recipe in source_recipes:
                insert_recipe_sql = text("""
                INSERT INTO weekly_menu_recipes
                (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name)
                VALUES
                (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name)
                """)

                db.session.execute(
                    insert_recipe_sql,
                    {
                        'weekly_menu_id': target_menu_id,
                        'day_of_week': recipe.day_of_week,
                        'meal_type': recipe.meal_type,
                        'recipe_id': recipe.recipe_id,
                        'recipe_name': recipe.recipe_name
                    }
                )

            # 复制食谱到目标菜单（副表）
            for recipe in source_temp_recipes:
                insert_temp_recipe_sql = text("""
                INSERT INTO weekly_menu_recipes_temp
                (weekly_menu_id, day_of_week, meal_type, recipe_id, recipe_name, is_custom, temp_data)
                VALUES
                (:weekly_menu_id, :day_of_week, :meal_type, :recipe_id, :recipe_name, :is_custom, :temp_data)
                """)

                db.session.execute(
                    insert_temp_recipe_sql,
                    {
                        'weekly_menu_id': target_menu_id,
                        'day_of_week': recipe.day_of_week,
                        'meal_type': recipe.meal_type,
                        'recipe_id': recipe.recipe_id,
                        'recipe_name': recipe.recipe_name,
                        'is_custom': recipe.is_custom,
                        'temp_data': recipe.temp_data
                    }
                )

            db.session.commit()
            current_app.logger.info(f"复制周菜单成功: source_id={source_menu_id}, target_id={target_menu_id}")

            return target_menu_id
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"复制周菜单失败: {str(e)}")
            raise WeeklyMenuError(f"复制周菜单失败: {str(e)}")
