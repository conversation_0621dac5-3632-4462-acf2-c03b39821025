"""
重启服务器脚本
"""

import os
import sys
import subprocess
import time
import signal
import psutil

def find_flask_process():
    """查找Flask进程"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('flask' in cmd.lower() for cmd in cmdline):
                    return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return None

def kill_flask_process():
    """杀死Flask进程"""
    pid = find_flask_process()
    if pid:
        print(f"找到Flask进程，PID: {pid}，正在终止...")
        try:
            os.kill(pid, signal.SIGTERM)
            time.sleep(1)
            if psutil.pid_exists(pid):
                os.kill(pid, signal.SIGKILL)
            print("Flask进程已终止")
            return True
        except Exception as e:
            print(f"终止Flask进程失败: {str(e)}")
    else:
        print("未找到Flask进程")
    return False

def start_flask():
    """启动Flask服务器"""
    print("正在启动Flask服务器...")
    try:
        subprocess.Popen(["flask", "run"], shell=True)
        print("Flask服务器已启动")
        return True
    except Exception as e:
        print(f"启动Flask服务器失败: {str(e)}")
        return False

if __name__ == "__main__":
    kill_flask_process()
    time.sleep(1)
    start_flask()
