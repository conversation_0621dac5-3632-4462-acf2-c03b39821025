#!/usr/bin/env python3
"""
测试单价显示问题的脚本
"""

import requests
import sys
from bs4 import BeautifulSoup

def test_unit_price_display():
    """测试单价在批次编辑器中的显示"""
    print("🧪 测试单价显示问题...")
    
    base_url = "http://127.0.0.1:5000"
    stock_in_id = 76  # 测试用的入库单ID
    
    try:
        # 测试批次编辑器页面
        editor_url = f"{base_url}/stock-in/{stock_in_id}/batch-editor-simplified"
        print(f"📤 1. 测试批次编辑器页面: {editor_url}")
        
        response = requests.get(editor_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 批次编辑器页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找单价输入框
            unit_price_inputs = soup.find_all('input', {'name': lambda x: x and x.startswith('unit_price_')})
            
            print(f"📋 找到 {len(unit_price_inputs)} 个单价输入框")
            
            if len(unit_price_inputs) > 0:
                for i, input_field in enumerate(unit_price_inputs[:3]):  # 只检查前3个
                    name = input_field.get('name', '')
                    value = input_field.get('value', '')
                    item_id = name.replace('unit_price_', '') if name.startswith('unit_price_') else 'unknown'
                    
                    print(f"  输入框 {i+1}:")
                    print(f"    项目ID: {item_id}")
                    print(f"    字段名: {name}")
                    print(f"    当前值: '{value}'")
                    
                    if not value or value == 'None' or value == '':
                        print(f"    ⚠️ 单价值为空，这是问题所在")
                    else:
                        print(f"    ✅ 单价值正常")
            else:
                print("❌ 没有找到单价输入框")
                return False
                
        else:
            print(f"❌ 批次编辑器页面访问失败，状态码: {response.status_code}")
            return False
        
        # 测试详情页面
        details_url = f"{base_url}/stock-in/{stock_in_id}/details"
        print(f"\n📤 2. 测试入库单详情页面: {details_url}")
        
        details_response = requests.get(details_url, timeout=30)
        
        if details_response.status_code == 200:
            print("✅ 入库单详情页面访问成功")
            
            # 解析HTML内容
            details_soup = BeautifulSoup(details_response.text, 'html.parser')
            
            # 查找入库明细表格
            detail_table = details_soup.find('table', class_='table-bordered')
            
            if detail_table:
                print("✅ 找到入库明细表格")
                
                # 检查表头
                headers = detail_table.find('thead')
                if headers:
                    header_cells = headers.find_all('th')
                    header_texts = [th.get_text().strip() for th in header_cells]
                    
                    print(f"📋 表头内容: {header_texts}")
                    
                    if '单价' in header_texts:
                        print("✅ 表头包含单价列")
                        
                        # 查找单价列的索引
                        unit_price_index = header_texts.index('单价')
                        print(f"📋 单价列索引: {unit_price_index}")
                        
                        # 检查数据行
                        tbody = detail_table.find('tbody')
                        if tbody:
                            data_rows = tbody.find_all('tr')
                            
                            if data_rows:
                                for i, row in enumerate(data_rows[:3]):  # 只检查前3行
                                    cells = row.find_all('td')
                                    
                                    if len(cells) > unit_price_index:
                                        unit_price_cell = cells[unit_price_index]
                                        unit_price_text = unit_price_cell.get_text().strip()
                                        
                                        print(f"  数据行 {i+1}:")
                                        print(f"    单价显示: '{unit_price_text}'")
                                        
                                        if not unit_price_text or unit_price_text == '-' or unit_price_text == '':
                                            print(f"    ⚠️ 单价显示为空")
                                        else:
                                            print(f"    ✅ 单价显示正常")
                                    else:
                                        print(f"  数据行 {i+1}: 列数不足")
                            else:
                                print("ℹ️ 表格中没有数据行")
                        else:
                            print("❌ 未找到表格数据部分")
                    else:
                        print("❌ 表头不包含单价列")
                        return False
                else:
                    print("❌ 未找到表头")
                    return False
            else:
                print("❌ 未找到入库明细表格")
                return False
        else:
            print(f"❌ 入库单详情页面访问失败，状态码: {details_response.status_code}")
            return False
            
        return True
        
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code in [200, 302]:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def provide_debugging_suggestions():
    """提供调试建议"""
    print("\n💡 调试建议:")
    print("="*60)
    
    print("\n1. 🔍 检查数据库数据:")
    print("   - 在数据库中查询 stock_in_items 表")
    print("   - 确认 unit_price 字段是否有值")
    print("   - SQL: SELECT id, ingredient_id, unit_price FROM stock_in_items WHERE stock_in_id = 76")
    
    print("\n2. 🔧 检查模型定义:")
    print("   - 确认 StockInItem 模型中有 unit_price 字段定义")
    print("   - 检查字段类型是否正确 (db.Float)")
    
    print("\n3. 🌐 检查模板渲染:")
    print("   - 在批次编辑器模板中检查 {{ item.unit_price }}")
    print("   - 在详情页面模板中检查单价列的显示")
    
    print("\n4. 🐛 添加调试输出:")
    print("   - 在批次编辑器路由中添加日志输出 item.unit_price")
    print("   - 在详情页面路由中添加日志输出")
    
    print("\n5. 🔄 重启应用:")
    print("   - 重启Flask应用以确保模型更改生效")
    print("   - 清除浏览器缓存")

def main():
    """主函数"""
    print("🚀 开始测试单价显示问题\n")
    
    # 检查服务器连接
    if not test_server_connection():
        print("\n❌ 无法连接到服务器，请先启动Flask应用")
        return False
    
    print()
    
    # 运行单价显示测试
    success = test_unit_price_display()
    
    # 输出测试结果
    print(f"\n{'='*60}")
    print("📊 测试结果")
    print('='*60)
    
    if success:
        print("🎉 单价显示测试通过！")
        print("\n✅ 确认:")
        print("  - 批次编辑器页面正常访问")
        print("  - 单价输入框存在")
        print("  - 入库单详情页面正常访问")
        print("  - 单价列正确显示")
    else:
        print("❌ 单价显示测试失败！")
        print("\n⚠️ 可能的问题:")
        print("  - 数据库中单价字段为空")
        print("  - 模型定义有问题")
        print("  - 模板渲染有问题")
        print("  - 应用需要重启")
    
    # 提供调试建议
    provide_debugging_suggestions()
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
