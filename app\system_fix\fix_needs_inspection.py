from flask import Blueprint, render_template, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.models import IngredientCategory
from sqlalchemy import text

def fix_needs_inspection():
    """修复 needs_inspection 和 inspection_type 字段缺失的问题"""
    try:
        # 修改 IngredientCategory 模型，添加 needs_inspection 和 inspection_type 字段
        conn = db.engine.connect()
        
        # 检查字段是否已存在
        result = conn.execute(text("""
            SELECT COUNT(*) AS count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'ingredient_categories'
            AND COLUMN_NAME = 'needs_inspection'
        """))
        row = result.fetchone()
        
        if row and row[0] == 0:
            # 字段不存在，添加字段
            conn.execute(text("""
                ALTER TABLE ingredient_categories
                ADD needs_inspection BIT NOT NULL DEFAULT 0
            """))
            
            conn.execute(text("""
                ALTER TABLE ingredient_categories
                ADD inspection_type NVARCHAR(50) NULL
            """))
            
            conn.close()
            
            return "成功添加 needs_inspection 和 inspection_type 字段"
        else:
            return "字段已存在，无需添加"
    except Exception as e:
        return f"修复 needs_inspection 和 inspection_type 字段时出错: {str(e)}"
