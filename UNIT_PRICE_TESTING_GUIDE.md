# 单价保存功能测试指南

## 问题确认

用户反馈：在批次编辑器 (`http://127.0.0.1:5000/stock-in/76/batch-editor-simplified`) 中修改单价信息后，单价没有保存到数据库中。

## 代码检查结果

✅ **后端逻辑正常**：
- 获取单价字段：`unit_price = request.form.get(f'unit_price_{item_id}')`
- SQL更新语句包含单价：`unit_price = :unit_price`
- 执行SQL时传递单价参数：`'unit_price': unit_price`
- 调试日志：已添加详细的调试信息

✅ **前端逻辑正常**：
- 单价输入框：`name="unit_price_{{ item.id }}"`
- 复选框：`name="selected_items[]"`
- 表单提交处理：AJAX提交逻辑完整
- 禁用字段处理：已修复禁用字段不提交的问题

## 正确的测试步骤

### 1. 🌐 访问批次编辑器

```
http://127.0.0.1:5000/stock-in/76/batch-editor-simplified
```

### 2. 🔧 打开浏览器开发者工具

- 按 **F12** 打开开发者工具
- 切换到 **Network** 标签页（重要！）
- 切换到 **Console** 标签页查看调试信息

### 3. ✅ 正确的操作流程

**关键步骤**：
1. **必须先勾选复选框**：点击要编辑的行左侧的复选框
2. **确认行变为可编辑状态**：勾选后，该行应该变为绿色，输入框变为可编辑
3. **修改单价**：在单价输入框中输入新的数值（如：19999.99）
4. **确保其他必填字段有值**：
   - 供应商：必须选择
   - 存储位置：必须选择
   - 生产日期：必须有值
   - 过期日期：必须有值
   - 数量：必须有值

### 4. 💾 提交保存

- 点击 **"保存批次信息"** 按钮
- **重要**：观察Network标签页中的请求

### 5. 🔍 验证请求数据

在Network标签页中查看POST请求：
- 请求URL：`/stock-in/76/save-batch-edit-simplified`
- 请求方法：POST
- **关键检查**：在Form Data部分确认是否包含：
  ```
  selected_items[]: [项目ID]
  unit_price_[项目ID]: [新的单价值]
  ```

### 6. 📊 检查Console输出

在Console标签页中查看：
- 是否有JavaScript错误
- 是否有调试日志：
  ```
  提交前检查 - 项目ID: XX, 单价: XX
  启用禁用字段: unit_price_XX, 值: XX
  ```

### 7. 🗄️ 检查服务器日志

在运行Flask应用的终端中查看：
```
批次编辑器保存 - 入库单ID: 76
选中的项目: ['XX']
单价字段 - unit_price_XX: XX
项目 XX 详细信息:
  - 单价: XX
```

### 8. 🔄 验证保存结果

**方法1**：重新访问编辑器页面
- 刷新页面或重新访问
- 检查单价输入框中的值是否已更新

**方法2**：访问详情页面
- 访问：`http://127.0.0.1:5000/stock-in/76/details`
- 在入库明细表格中查看单价列

## 常见问题排查

### ❌ 问题1：复选框未勾选
**现象**：修改了单价但没有保存
**原因**：没有勾选复选框，该行数据不会被提交
**解决**：确保勾选要编辑的行的复选框

### ❌ 问题2：必填字段为空
**现象**：点击保存后没有反应或显示错误
**原因**：供应商、存储位置、日期等必填字段为空
**解决**：确保所有必填字段都有值

### ❌ 问题3：Network请求中没有单价字段
**现象**：Form Data中缺少`unit_price_XX`字段
**原因**：字段被禁用或JavaScript处理有问题
**解决**：检查复选框是否勾选，刷新页面重试

### ❌ 问题4：服务器返回错误
**现象**：Network请求返回4xx或5xx状态码
**原因**：服务器端处理出错
**解决**：查看服务器日志，检查错误信息

### ❌ 问题5：保存成功但值没有更新
**现象**：请求成功，但重新查看时值没有变化
**原因**：可能是浏览器缓存或数据库事务问题
**解决**：强制刷新页面（Ctrl+F5），或检查数据库

## 调试技巧

### 1. 使用浏览器调试
```javascript
// 在Console中执行，检查表单数据
var form = document.getElementById('batchEditForm');
var formData = new FormData(form);
for (var pair of formData.entries()) {
    if (pair[0].includes('unit_price')) {
        console.log(pair[0] + ': ' + pair[1]);
    }
}
```

### 2. 检查复选框状态
```javascript
// 检查哪些复选框被选中
$('.batch-checkbox:checked').each(function() {
    console.log('选中的项目ID:', $(this).val());
});
```

### 3. 检查输入框状态
```javascript
// 检查单价输入框的值和状态
$('input[name^="unit_price_"]').each(function() {
    console.log('字段名:', $(this).attr('name'), '值:', $(this).val(), '禁用:', $(this).prop('disabled'));
});
```

## 预期结果

### ✅ 正常流程
1. 勾选复选框 → 行变绿色，字段可编辑
2. 修改单价 → 输入框显示新值
3. 点击保存 → Network显示POST请求
4. 请求成功 → 页面跳转到详情页面
5. 验证结果 → 详情页面显示新的单价

### ✅ 调试信息
- **前端Console**：显示提交前检查和字段启用的日志
- **Network请求**：Form Data包含`unit_price_XX`字段
- **服务器日志**：显示接收到的单价字段和更新信息
- **数据库**：`stock_in_items`表中的`unit_price`字段已更新

## 总结

代码逻辑检查显示前后端处理都是正确的，问题很可能出现在：

1. **操作流程**：用户没有按正确流程操作（未勾选复选框）
2. **数据验证**：必填字段为空导致提交失败
3. **浏览器问题**：缓存或JavaScript执行问题
4. **网络问题**：请求没有正确发送到服务器

**建议按照上述测试步骤进行详细的手动测试，重点关注Network请求和服务器日志，这样可以准确定位问题所在。**
