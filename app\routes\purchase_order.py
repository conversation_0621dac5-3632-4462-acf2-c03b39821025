from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, make_response, abort, send_file
from flask_login import login_required, current_user
from app import db
from app.models import (
    PurchaseOrder, PurchaseOrderItem, Supplier, SupplierProduct,
    Ingredient, Recipe, RecipeIngredient, WeeklyMenu, WeeklyMenuRecipe,
    AdministrativeArea, Inventory, StockIn, StockInItem, SupplierSchoolRelation, MenuPlan,
    User
)
from app.utils.decorators import check_permission
from app.utils.pdf_generator import generate_purchase_order_pdf, generate_supplier_order_pdf
from app.forms.purchase_order import PurchaseOrderForm, PurchaseOrderFilterForm, MenuPurchaseOrderForm
from datetime import datetime, date, timedelta
import json
import uuid
import os
from sqlalchemy import func, text, Numeric, DateTime, String, Integer, Date
from decimal import Decimal
from sqlalchemy.types import Numeric
from sqlalchemy import bindparam
from sqlalchemy.orm import joinedload

# 辅助函数：格式化日期时间
def format_datetime(dt, format_str):
    """格式化日期时间对象为字符串"""
    if dt is None:
        return ''
    if isinstance(dt, str):
        try:
            # 尝试将字符串转换为datetime对象
            dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                # 尝试另一种常见格式
                dt = datetime.strptime(dt, '%Y-%m-%d')
            except ValueError:
                # 如果无法解析，直接返回原字符串
                return dt
    try:
        return dt.strftime(format_str)
    except Exception:
        return str(dt)

purchase_order_bp = Blueprint('purchase_order', __name__, url_prefix='/purchase-order')

def standardize_unit(unit):
    """单位标准化函数"""
    # 单位标准化映射
    unit_map = {
        '克': 'kg',
        '千克': 'kg',
        'kg': 'kg',
        'g': 'kg',
        '公斤': 'kg',
        '斤': 'kg',
        '两': 'kg',

        '毫升': 'ml',
        '升': 'ml',
        'ml': 'ml',
        'l': 'ml',

        '瓶': '瓶',
        '罐': '瓶',
        '盒': '盒',
        '包': '包',
        '袋': '包',
        '个': '个',
        '只': '个',
        '条': '个',
        '根': '个'
    }

    # 如果有映射则返回标准单位，否则返回原单位
    return unit_map.get(unit, unit)

@purchase_order_bp.route('/')
@login_required
@check_permission('purchase_order', 'view')
def index():
    """采购订单列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    supplier_id = request.args.get('supplier_id', type=int)

    # 构建查询
    query = PurchaseOrder.query.filter(PurchaseOrder.area_id.in_(area_ids))

    # 应用筛选条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    if status:
        query = query.filter_by(status=status)
    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)
    if start_date:
        query = query.filter(PurchaseOrder.order_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(PurchaseOrder.order_date <= datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1))

    # 排序并分页
    orders = query.order_by(PurchaseOrder.order_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取每个订单是否已经创建了入库单
    order_ids = [order.id for order in orders.items]
    stock_in_orders = db.session.query(StockIn.purchase_order_id).filter(
        StockIn.purchase_order_id.in_(order_ids)
    ).all()

    # 创建一个集合，包含所有已创建入库单的采购订单ID
    stock_in_order_ids = {order_id for (order_id,) in stock_in_orders}

    # 获取所有区域和供应商，用于筛选
    areas = accessible_areas
    suppliers = Supplier.query.all()

    return render_template('purchase_order/index.html',
                          title='采购订单列表',
                          orders=orders,
                          areas=areas,
                          suppliers=suppliers,
                          area_id=area_id,
                          supplier_id=supplier_id,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          stock_in_order_ids=stock_in_order_ids)

@purchase_order_bp.route('/create-from-menu')
@login_required
@check_permission('purchase_order', 'create')
def create_from_menu():
    """从周菜单创建采购订单页面"""
    # 获取参数
    weekly_menu_id = request.args.get('weekly_menu_id', type=int)
    area_id = request.args.get('area_id', type=int)

    # 如果提供了weekly_menu_id，直接获取对应的菜单
    if weekly_menu_id:
        weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)
        area_id = weekly_menu.area_id
        area = AdministrativeArea.query.get_or_404(area_id)
        week_start = weekly_menu.week_start
        week_end = weekly_menu.week_end
    else:
        # 如果没有提供weekly_menu_id，则需要area_id
        if not area_id:
            # 如果既没有weekly_menu_id也没有area_id，使用当前用户的区域
            area_id = current_user.area_id
            if not area_id:
                flash('请指定区域或菜单ID', 'error')
                return redirect(url_for('purchase_order.index'))

        area = AdministrativeArea.query.get_or_404(area_id)

        # 获取最新的周菜单（按week_start降序排序，取第一个）
        weekly_menu = WeeklyMenu.query.filter_by(
            area_id=area_id
        ).order_by(WeeklyMenu.week_start.desc()).first()

        # 如果没有找到菜单，返回错误信息
        if not weekly_menu:
            flash('未找到该区域的菜单数据', 'error')
            return redirect(url_for('purchase_order.index'))

        week_start = weekly_menu.week_start
        week_end = weekly_menu.week_end

    # 获取周菜单数据
    week_data = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

    for i in range(7):
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')

        # 获取该日期的菜单
        day_recipes = WeeklyMenuRecipe.query.filter_by(
            weekly_menu_id=weekly_menu.id,
            day_of_week=i + 1
        ).all()

        # 按餐次分类食谱
        meals = {
            '早餐': [],
            '午餐': [],
            '晚餐': []
        }

        for menu_recipe in day_recipes:
            if menu_recipe.recipe:
                recipe_data = {
                    'id': menu_recipe.recipe.id,
                    'name': menu_recipe.recipe.name,
                    'category': menu_recipe.recipe.category,
                    'ingredients': [
                        {
                            'id': ri.ingredient_id,
                            'name': ri.ingredient.name,
                            'quantity': ri.quantity,
                            'unit': ri.unit
                        }
                        for ri in menu_recipe.recipe.ingredients
                    ]
                }
                meals[menu_recipe.meal_type].append(recipe_data)

        week_data[date_str] = {
            'weekday': weekdays[i],
            'day_of_week': i + 1,
            'meals': meals
        }

    return render_template('purchase_order/create_from_menu.html',
                         area=area,
                         week_start=week_start.strftime('%Y-%m-%d'),
                         week_end=week_end.strftime('%Y-%m-%d'),
                         week_data=week_data)

@purchase_order_bp.route('/get-ingredients', methods=['POST'])
@login_required
@check_permission('purchase_order', 'create')
def get_ingredients():
    """获取食材清单"""
    data = request.get_json()

    if not data or 'area_id' not in data or 'selected_days' not in data:
        return jsonify({
            'success': False,
            'message': '参数错误'
        })

    area_id = data['area_id']
    selected_days = data['selected_days']

    try:
        # 获取区域信息
        area = AdministrativeArea.query.get_or_404(area_id)

        # 收集所有选中日期的食谱ID和对应的食材信息
        ingredients_data = {}

        for day in selected_days:
            date = day['date']
            for meal in day['meals']:
                meal_type = meal['type']
                for recipe in meal['recipes']:
                    recipe_id = recipe['id']
                    # 获取食谱的食材信息
                    recipe_obj = Recipe.query.get(recipe_id)
                    if recipe_obj:
                        for ri in recipe_obj.ingredients:
                            ingredient = ri.ingredient
                            # 排除调味品类
                            if ingredient.category == '调味品':
                                continue

                            key = (ingredient.id, ri.unit)

                            if key not in ingredients_data:
                                ingredients_data[key] = {
                                    'id': ingredient.id,
                                    'name': ingredient.name,
                                    'category': ingredient.category,
                                    'total_quantity': 0,
                                    'unit': ri.unit,
                                    'suppliers': []  # 供应商列表
                                }

                            # 累加数量
                            ingredients_data[key]['total_quantity'] += ri.quantity

        # 获取每个食材的供应商信息
        for key, ingredient_data in ingredients_data.items():
            ingredient_id = ingredient_data['id']

            # 获取可用的供应商
            supplier_products = db.session.query(
                SupplierProduct,
                Supplier
            ).join(
                Supplier,
                SupplierProduct.supplier_id == Supplier.id
            ).join(
                SupplierSchoolRelation,
                Supplier.id == SupplierSchoolRelation.supplier_id
            ).filter(
                SupplierProduct.ingredient_id == ingredient_id,
                SupplierProduct.is_available == 1,
                Supplier.status == 1,
                SupplierSchoolRelation.area_id == area_id,
                SupplierSchoolRelation.status == 1
            ).all()

            # 添加供应商信息
            for supplier_product, supplier in supplier_products:
                supplier_data = {
                    'id': supplier.id,
                    'name': supplier.name,
                    'product_id': supplier_product.id,
                    'specification': supplier_product.specification or ''
                }
                ingredient_data['suppliers'].append(supplier_data)

        # 转换为列表
        ingredients_list = list(ingredients_data.values())

        # 按类别分组
        grouped_ingredients = {}
        for ingredient in ingredients_list:
            category = ingredient['category']
            if category not in grouped_ingredients:
                grouped_ingredients[category] = []
            grouped_ingredients[category].append(ingredient)

        return jsonify({
            'success': True,
            'data': {
                'ingredients': grouped_ingredients
            }
        })

    except Exception as e:
        current_app.logger.error(f'获取食材清单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取食材清单失败'
        })

@purchase_order_bp.route('/create-order', methods=['POST'])
@login_required
@check_permission('purchase_order', 'create')
def create_order():
    """创建采购订单"""
    # 从请求中获取JSON数据
    json_data = request.get_json()
    if not json_data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        })

    try:
        # 生成订单编号和时间
        order_number = generate_order_number()
        now = datetime.now().replace(microsecond=0)
        delivery_date = now + timedelta(days=1)

        # 确定供应商ID - 使用0表示自购订单
        supplier_id = 0
        ingredients = json_data.get('ingredients', [])
        if ingredients:
            # 检查第一个有供应商的食材
            for item in ingredients:
                if item.get('product_id'):
                    supplier_product = SupplierProduct.query.get(item.get('product_id'))
                    if supplier_product:
                        supplier_id = supplier_product.supplier_id
                        break

        # 准备订单数据
        order_data = {
            'order_number': order_number,
            'supplier_id': supplier_id,  # 使用0表示自购订单，确保不为NULL
            'total_amount': Decimal('0.00'),
            'order_date': now,
            'status': '待确认',  # 设置为待确认状态，遵循正常的业务流程
            'delivery_date': delivery_date,
            'created_by': current_user.id,
            'created_at': now,
            'requisition_id': 0,  # 使用0代替NULL
            'area_id': json_data.get('area_id'),
            'expected_delivery_date': delivery_date.date(),
            'payment_terms': '',  # 使用空字符串代替NULL
            'delivery_terms': '',  # 使用空字符串代替NULL
            'approved_by': None,  # 初始状态未审核
            'updated_at': now,
            'notes': json_data.get('notes', '')  # 使用用户提供的备注
        }

        # 创建采购订单的 SQL 语句
        insert_order_stmt = text("""
            INSERT INTO purchase_orders (
                order_number, supplier_id, total_amount, order_date, status,
                delivery_date, created_by, created_at, requisition_id, area_id,
                expected_delivery_date, payment_terms, delivery_terms,
                approved_by, updated_at, notes
            )
            OUTPUT inserted.id
            VALUES (
                :order_number, :supplier_id, :total_amount, :order_date, :status,
                :delivery_date, :created_by, :created_at, :requisition_id, :area_id,
                :expected_delivery_date, :payment_terms, :delivery_terms,
                :approved_by, :updated_at, :notes
            )
        """).bindparams(
            bindparam('order_number', type_=String),
            bindparam('supplier_id', type_=Integer),
            bindparam('total_amount', type_=Numeric(18, 2)),
            bindparam('order_date', type_=DateTime),
            bindparam('status', type_=String),
            bindparam('delivery_date', type_=DateTime),
            bindparam('created_by', type_=Integer),
            bindparam('created_at', type_=DateTime),
            bindparam('requisition_id', type_=Integer),
            bindparam('area_id', type_=Integer),
            bindparam('expected_delivery_date', type_=Date),
            bindparam('payment_terms', type_=String),
            bindparam('delivery_terms', type_=String),
            bindparam('approved_by', type_=Integer),
            bindparam('updated_at', type_=DateTime),
            bindparam('notes', type_=String)
        )

        # 执行订单插入并获取ID
        result = db.session.execute(insert_order_stmt, order_data)
        order_id = result.scalar()

        # 创建订单明细的 SQL 语句
        insert_item_stmt = text("""
            INSERT INTO purchase_order_items (
                order_id, ingredient_id, product_id, quantity, unit,
                unit_price, total_price, created_at, updated_at
            )
            VALUES (
                :order_id, :ingredient_id, :product_id, :quantity, :unit,
                :unit_price, :total_price, :created_at, :updated_at
            )
        """).bindparams(
            bindparam('order_id', type_=Integer),
            bindparam('ingredient_id', type_=Integer),
            bindparam('product_id', type_=Integer),
            bindparam('quantity', type_=Numeric(10, 2)),
            bindparam('unit', type_=String),
            bindparam('unit_price', type_=Numeric(10, 2)),
            bindparam('total_price', type_=Numeric(10, 2)),
            bindparam('created_at', type_=DateTime),
            bindparam('updated_at', type_=DateTime)
        )

        # 处理订单明细
        total_amount = Decimal('0.00')

        for item_data in ingredients:
            # 获取食材信息
            ingredient = Ingredient.query.get(item_data.get('id'))
            if not ingredient:
                continue

            # 如果选择了供应商，获取供应商产品信息
            supplier_product = None
            if item_data.get('product_id'):
                supplier_product = SupplierProduct.query.get(item_data.get('product_id'))

            # 使用 Decimal 进行精确计算
            try:
                quantity = Decimal(str(item_data.get('purchase_quantity', '0')))
                unit_price = Decimal(str(supplier_product.price)) if supplier_product else Decimal('0.00')
            except (TypeError, ValueError):
                quantity = Decimal('0')
                unit_price = Decimal('0.00')

            # 计算总价
            total_price = quantity * unit_price

            # 准备订单项数据
            item_data = {
                'order_id': order_id,
                'ingredient_id': item_data.get('id'),
                'product_id': item_data.get('product_id') or 0,  # 使用0代替NULL
                'quantity': quantity,
                'unit': item_data.get('unit', ''),  # 使用传入的单位
                'unit_price': unit_price,
                'total_price': total_price,
                'created_at': now,
                'updated_at': now
            }

            # 执行订单项插入
            db.session.execute(insert_item_stmt, item_data)
            total_amount += total_price

        # 更新订单总金额
        update_total_stmt = text("""
            UPDATE purchase_orders
            SET total_amount = :total_amount
            WHERE id = :order_id
        """).bindparams(
            bindparam('total_amount', type_=Numeric(18, 2)),
            bindparam('order_id', type_=Integer)
        )

        db.session.execute(update_total_stmt, {
            'total_amount': total_amount,
            'order_id': order_id
        })

        # 不再自动创建入库记录，遵循正常的业务流程
        # 入库流程将在采购订单确认、送货和验收后单独处理

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '采购订单创建成功，等待确认',
            'redirect_url': url_for('purchase_order.view', id=order_id)
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建采购订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '创建采购订单失败，请检查数据是否正确'
        })

def generate_order_number():
    """生成订单编号"""
    now = datetime.now()
    date_str = now.strftime('%Y%m%d')
    time_str = now.strftime('%H%M%S')
    random_str = str(int(now.timestamp() * 1000))[-3:]
    return f'PO{date_str}{time_str}{random_str}'

@purchase_order_bp.route('/list-json')
@login_required
def list_json():
    """获取采购订单列表（JSON格式）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    status = request.args.get('status', '')

    # 构建查询
    query = PurchaseOrder.query.filter(PurchaseOrder.area_id.in_(area_ids))

    # 应用筛选条件
    if status:
        query = query.filter_by(status=status)
    else:
        # 默认只显示待确认和已确认状态的订单
        query = query.filter(PurchaseOrder.status.in_(['待确认', '已确认']))

    # 排序并获取结果
    orders = query.order_by(PurchaseOrder.order_date.desc()).all()

    # 获取每个订单是否已经创建了入库单
    order_ids = [order.id for order in orders]
    stock_in_orders = db.session.query(StockIn.purchase_order_id).filter(
        StockIn.purchase_order_id.in_(order_ids)
    ).all()

    # 创建一个集合，包含所有已创建入库单的采购订单ID
    stock_in_order_ids = {order_id for (order_id,) in stock_in_orders}

    # 构建响应数据
    result = []
    for order in orders:
        # 跳过已经创建入库单的订单
        if order.id in stock_in_order_ids:
            continue

        order_data = {
            'id': order.id,
            'order_number': order.order_number,
            'created_at': format_datetime(order.created_at, '%Y-%m-%d'),
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'status': order.status,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'has_stock_in': order.id in stock_in_order_ids
        }
        result.append(order_data)

    return jsonify({
        'success': True,
        'data': result
    })

@purchase_order_bp.route('/<int:id>')
@login_required
@check_permission('purchase_order', 'view')
def view(id):
    """查看采购订单详情"""
    # 使用no_autoflush避免自动刷新
    with db.session.no_autoflush:
        order = PurchaseOrder.query.get_or_404(id)

        # 检查用户是否有权限查看
        if not current_user.can_access_area_by_id(order.area_id):
            flash('您没有权限查看该采购订单', 'danger')
            return redirect(url_for('purchase_order.index'))

        # 检查是否已经创建了入库单
        has_stock_in = db.session.query(StockIn).filter_by(purchase_order_id=id).first() is not None

        # 如果订单状态为"已入库"，也标记为已创建入库单
        if order.status == '已入库':
            has_stock_in = True

        # 创建一个临时对象用于显示，不修改数据库中的对象
        display_order = {
            'id': order.id,
            'order_number': order.order_number,
            'supplier_id': order.supplier_id,
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'requisition_id': order.requisition_id,
            'area_id': order.area_id,
            'area_name': order.area.name if order.area else None,
            'total_amount': float(order.total_amount),
            'status': order.status,
            'created_by': order.created_by,
            'creator_name': order.creator.real_name or order.creator.username,
            'approved_by': order.approved_by,
            'approver_name': order.approver.real_name or order.approver.username if order.approver else None,
            'notes': order.notes,
            'order_items': [item.to_dict() for item in order.order_items],
            'has_stock_in': has_stock_in
        }

        # 添加状态显示文本
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'delivered': '已送达',
            'cancelled': '已取消',
            # 添加中文状态值的映射
            '待确认': '待确认',
            '已确认': '已确认',
            '已送达': '已送达',
            '准备入库': '准备入库',
            '已入库': '已入库',
            '已取消': '已取消'
        }
        # 直接存储状态显示文本，而不是使用lambda函数
        display_order['status_display'] = status_map.get(display_order['status'], display_order['status'])

        # 处理日期时间字段
        if isinstance(order.order_date, str):
            try:
                display_order['order_date'] = datetime.strptime(order.order_date, '%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                display_order['order_date'] = datetime.now().replace(microsecond=0)
        else:
            display_order['order_date'] = order.order_date

        if isinstance(order.delivery_date, str):
            try:
                display_order['delivery_date'] = datetime.strptime(order.delivery_date, '%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                display_order['delivery_date'] = None
        else:
            display_order['delivery_date'] = order.delivery_date

        if isinstance(order.expected_delivery_date, str):
            try:
                display_order['expected_delivery_date'] = datetime.strptime(order.expected_delivery_date, '%Y-%m-%d').date()
            except (ValueError, TypeError):
                display_order['expected_delivery_date'] = None
        else:
            display_order['expected_delivery_date'] = order.expected_delivery_date

        # 格式化日期时间字段
        display_order['order_date_formatted'] = format_datetime(display_order['order_date'], '%Y-%m-%d %H:%M:%S') if display_order['order_date'] else ''
        display_order['delivery_date_formatted'] = format_datetime(display_order['delivery_date'], '%Y-%m-%d %H:%M:%S') if display_order['delivery_date'] else ''
        display_order['expected_delivery_date_formatted'] = format_datetime(display_order['expected_delivery_date'], '%Y-%m-%d') if display_order['expected_delivery_date'] else ''
        display_order['created_at'] = format_datetime(order.created_at, '%Y-%m-%d %H:%M:%S') if order.created_at else ''
        display_order['updated_at'] = format_datetime(order.updated_at, '%Y-%m-%d %H:%M:%S') if order.updated_at else ''

    return render_template('purchase_order/view.html', order=display_order)

@purchase_order_bp.route('/create-form', methods=['GET', 'POST'])
@login_required
@check_permission('purchase_order', 'create')
def create_form():
    """使用表单创建采购订单"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_choices = [(area.id, area.name) for area in accessible_areas]

    # 获取所有供应商
    suppliers = Supplier.query.filter_by(status=1).all()
    supplier_choices = [(0, '自购')] + [(s.id, s.name) for s in suppliers]

    # 创建表单
    form = PurchaseOrderForm()
    form.area_id.choices = area_choices
    form.supplier_id.choices = supplier_choices

    # 获取所有食材
    ingredients = Ingredient.query.filter_by(status=1).all()

    # 处理表单提交
    if form.validate_on_submit():
        try:
            # 生成订单编号
            batch_number = form.batch_number.data
            if batch_number:
                order_number = f"{batch_number}-{form.supplier_id.data}-{uuid.uuid4().hex[:6]}"
            else:
                order_number = f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"

            # 计算总金额
            total_amount = sum(float(item.total_price.data or 0) for item in form.items)

            # 创建采购订单
            order = PurchaseOrder(
                order_number=order_number,
                supplier_id=form.supplier_id.data if form.supplier_id.data != 0 else None,  # 如果是自购，设置为None
                area_id=form.area_id.data,
                total_amount=total_amount,
                order_date=form.order_date.data,
                expected_delivery_date=form.expected_delivery_date.data,
                status='待确认',  # 设置为待确认状态，遵循正常的业务流程
                created_by=current_user.id,
                approved_by=None,  # 初始状态未审核
                notes=form.notes.data
            )
            db.session.add(order)
            db.session.flush()  # 获取ID

            # 添加订单明细
            for item_form in form.items:
                ingredient_id = int(item_form.ingredient_id.data)
                quantity = float(item_form.quantity.data)
                unit_price = float(item_form.unit_price.data)

                # 标准化单位
                unit = item_form.unit.data
                standard_unit = standardize_unit(unit)

                # 查找对应的产品ID
                product_id = None
                if form.supplier_id.data != 0:  # 非自购
                    product = SupplierProduct.query.filter_by(
                        supplier_id=form.supplier_id.data,
                        ingredient_id=ingredient_id
                    ).first()
                    if product:
                        product_id = product.id

                order_item = PurchaseOrderItem(
                    order_id=order.id,
                    product_id=product_id,
                    ingredient_id=ingredient_id,
                    quantity=quantity,
                    unit=standard_unit,
                    unit_price=unit_price,
                    total_price=quantity * unit_price,
                    notes=item_form.notes.data
                )
                db.session.add(order_item)

            # 不再自动创建入库记录，遵循正常的业务流程
            # 入库流程将在采购订单确认、送货和验收后单独处理

            db.session.commit()

            flash(f'采购订单 {order_number} 创建成功，等待确认', 'success')
            return redirect(url_for('purchase_order.view', id=order.id))

        except Exception as e:
            db.session.rollback()
            flash(f'创建采购订单失败: {str(e)}', 'danger')

    # 如果是GET请求或表单验证失败，显示创建页面
    return render_template('purchase_order/create_form.html',
                          form=form,
                          ingredients=ingredients,
                          title='创建采购订单')

@purchase_order_bp.route('/<int:id>/confirm', methods=['POST'])
@login_required
@check_permission('purchase_order', 'approve')
def confirm_order(id):
    """确认采购订单"""
    order = PurchaseOrder.query.get_or_404(id)

    # 学校管理员和系统管理员可以确认任何状态的订单
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        if order.status != '待确认':
            return jsonify({
                'success': False,
                'message': '只能确认待确认状态的订单'
            })

    try:
        # 使用中文状态
        order.status = '已确认'
        order.confirmed_at = datetime.now()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '订单已确认'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'确认订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '确认订单失败'
        })

@purchase_order_bp.route('/<int:id>/cancel', methods=['POST'])
@login_required
@check_permission('purchase_order', 'approve')
def cancel_order(id):
    """取消采购订单"""
    data = request.get_json()

    if not data or 'reason' not in data:
        return jsonify({
            'success': False,
            'message': '请提供取消原因'
        })

    order = PurchaseOrder.query.get_or_404(id)

    # 学校管理员和系统管理员可以取消任何状态的订单
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        if order.status != '待确认':
            return jsonify({
                'success': False,
                'message': '只能取消待确认状态的订单'
            })

    try:
        # 使用中文状态
        order.status = '已取消'
        order.cancelled_at = datetime.now()
        order.cancel_reason = data['reason']
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '订单已取消'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'取消订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '取消订单失败'
        })

@purchase_order_bp.route('/<int:id>/deliver', methods=['POST'])
@login_required
@check_permission('purchase_order', 'execute')
def deliver_order(id):
    """标记订单送达并准备入库"""
    data = request.get_json()
    notes = data.get('notes', '')

    order = PurchaseOrder.query.get_or_404(id)

    # 学校管理员和系统管理员可以标记任何状态的订单为准备入库
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        if order.status != '已确认':
            return jsonify({
                'success': False,
                'message': '只能标记已确认状态的订单为准备入库'
            })

    try:
        # 使用中文状态
        order.status = '准备入库'  # 修改为准备入库状态
        order.delivered_at = datetime.now()
        order.delivery_notes = notes
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '订单已标记为准备入库，请进行验收检查'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'标记订单准备入库失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '标记订单准备入库失败'
        })

@purchase_order_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@check_permission('purchase_order', 'delete')
def delete_order(id):
    """删除采购订单"""
    order = PurchaseOrder.query.get_or_404(id)

    # 检查用户权限
    if not current_user.can_access_area_by_id(order.area_id):
        return jsonify({
            'success': False,
            'message': '您没有权限删除该采购订单'
        })

    # 学校管理员和系统管理员可以删除任何状态的订单
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        # 只允许删除待确认或已取消状态的订单
        if order.status not in ['待确认', '已取消']:
            return jsonify({
                'success': False,
                'message': '只能删除待确认或已取消状态的订单'
            })

    try:
        # 保存订单信息用于审计日志
        order_info = {
            'id': order.id,
            'order_number': order.order_number,
            'supplier_id': order.supplier_id,
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'area_id': order.area_id,
            'area_name': order.area.name if order.area else None,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'status': order.status,
            'order_date': order.order_date
        }

        # 先删除订单明细
        PurchaseOrderItem.query.filter_by(order_id=id).delete()

        # 再删除订单
        db.session.delete(order)

        # 记录审计日志
        from app.utils import log_activity
        log_activity(
            action='delete',
            resource_type='PurchaseOrder',
            resource_id=id,
            area_id=order_info['area_id'],
            details=order_info
        )

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '采购订单删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除采购订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除采购订单失败: {str(e)}'
        })

@purchase_order_bp.route('/<int:id>/json')
@login_required
def get_order_json(id):
    """获取采购订单JSON数据"""
    try:
        # 获取采购订单
        order = PurchaseOrder.query.get_or_404(id)

        # 简化权限检查，允许所有登录用户查看
        if not current_user.is_admin() and not current_user.can_access_area_by_id(order.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该采购订单'
            }), 403

        # 获取订单项目
        items = []
        for item in order.items:
            item_data = {
                'id': item.id,
                'ingredient_id': item.ingredient_id,
                'ingredient_name': item.ingredient.name if item.ingredient else '未知食材',
                'quantity': float(item.quantity) if item.quantity else 0,
                'unit': item.unit,
                'unit_price': float(item.unit_price) if item.unit_price else 0,
                'total_price': float(item.quantity * item.unit_price) if item.quantity and item.unit_price else 0
            }
            items.append(item_data)

        # 构建订单数据
        order_data = {
            'id': order.id,
            'order_number': order.order_number,
            'order_date': order.order_date.strftime('%Y-%m-%d') if order.order_date else None,
            'delivery_date': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else None,
            'status': order.status,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'notes': order.notes,
            'supplier_id': order.supplier_id,
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'area_id': order.area_id,
            'area_name': order.area.name if order.area else '未知区域',
            'creator_id': order.creator_id,
            'creator_name': order.creator.username if order.creator else '未知用户',
            'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else None,
            'items': items
        }

        return jsonify({
            'success': True,
            'data': order_data
        })

    except Exception as e:
        current_app.logger.error(f"获取采购订单JSON数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取采购订单JSON数据失败: {str(e)}'
        }), 500

@purchase_order_bp.route('/print/<int:order_id>')
@login_required
@check_permission('purchase_order', 'view')
def print_order(order_id):
    """打印采购订单"""
    try:
        # 获取订单
        order = PurchaseOrder.query.get_or_404(order_id)

        # 检查权限
        if not current_user.can_access_area(order.area):
            flash('您没有权限查看该区域的订单', 'danger')
            return redirect(url_for('purchase_order.index'))

        # 获取订单项
        order_items = PurchaseOrderItem.query.filter_by(order_id=order_id).all()

        # 获取关联信息
        area = AdministrativeArea.query.get(order.area_id)
        creator = User.query.get(order.created_by) if order.created_by else None
        approver = User.query.get(order.approved_by) if order.approved_by else None
        supplier = Supplier.query.get(order.supplier_id) if order.supplier_id else None

        # 添加显示信息
        order.area_name = area.name if area else current_user.area.name
        order.creator_name = creator.real_name if creator and creator.real_name else (creator.username if creator else '-')
        order.approver_name = approver.real_name if approver and approver.real_name else (approver.username if approver else '-')
        order.supplier_name = supplier.name if supplier else '自购'

        # 添加状态显示文本
        order.status_display = order.get_status_display()

        # 处理订单项
        for item in order_items:
            # 获取食材名称
            ingredient = Ingredient.query.get(item.ingredient_id)
            item.ingredient_name = ingredient.name if ingredient else f'未知食材({item.ingredient_id})'

            # 获取供应商名称
            if item.product_id:
                product = SupplierProduct.query.get(item.product_id)
                if product and product.supplier:
                    item.supplier_name = product.supplier.name
                else:
                    item.supplier_name = '自购'
            else:
                item.supplier_name = '自购'

        # 添加订单项到订单对象
        order.order_items = order_items

        return render_template('purchase_order/print.html',
                              order=order,
                              current_time=datetime.now())

    except Exception as e:
        current_app.logger.error(f"打印采购订单出错: {str(e)}")
        flash(f'打印订单失败: {str(e)}', 'danger')
        return redirect(url_for('purchase_order.index'))

