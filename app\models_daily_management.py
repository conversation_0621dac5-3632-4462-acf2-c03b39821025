from app import db
from datetime import datetime
from sqlalchemy import Float, Text, ForeignKey, Boolean
from sqlalchemy.dialects.mssql import DATETIME2
from sqlalchemy.orm import relationship
import json

class DailyLog(db.Model):
    """食堂日常工作日志"""
    __tablename__ = 'daily_logs'

    id = db.Column(db.Integer, primary_key=True)
    log_date = db.Column(DATETIME2(precision=1), nullable=False, index=True)
    weather = db.Column(db.String(50))
    manager = db.Column(db.String(100))
    student_count = db.Column(db.Integer, default=0)
    teacher_count = db.Column(db.Integer, default=0)
    other_count = db.Column(db.Integer, default=0)
    breakfast_menu = db.Column(Text)
    lunch_menu = db.Column(Text)
    dinner_menu = db.Column(Text)
    food_waste = db.Column(Float)  # 食物浪费量(kg)
    special_events = db.Column(Text)  # 特殊事件概述
    operation_summary = db.Column(Text)  # 运营总结
    area_id = db.Column(db.Integer, ForeignKey('administrative_areas.id'))
    created_by = db.Column(db.Integer, ForeignKey('users.id'))
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关联关系
    inspection_records = relationship("InspectionRecord", back_populates="daily_log", cascade="all, delete-orphan")
    dining_companions = relationship("DiningCompanion", back_populates="daily_log", cascade="all, delete-orphan")
    training_records = relationship("CanteenTrainingRecord", back_populates="daily_log", cascade="all, delete-orphan")
    special_events_records = relationship("SpecialEvent", back_populates="daily_log", cascade="all, delete-orphan")
    issues = relationship("Issue", back_populates="daily_log", cascade="all, delete-orphan")

    def __repr__(self):
        return f'<DailyLog {self.log_date}>'


class InspectionRecord(db.Model):
    """检查记录"""
    __tablename__ = 'inspection_records'

    id = db.Column(db.Integer, primary_key=True)
    daily_log_id = db.Column(db.Integer, ForeignKey('daily_logs.id'), nullable=False)
    inspection_type = db.Column(db.String(10), nullable=False)  # 'morning', 'noon', 'evening'
    inspection_item = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(10), default='normal')  # 'normal', 'abnormal'
    description = db.Column(Text)
    photo_path = db.Column(db.String(255))  # 照片路径
    inspector_id = db.Column(db.Integer, ForeignKey('users.id'))
    reference_type = db.Column(db.String(20))  # 引用类型，如 'purchase_order'
    reference_id = db.Column(db.Integer)  # 引用ID
    inspection_time = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关联关系
    daily_log = relationship("DailyLog", back_populates="inspection_records")

    # 使用显式查询而不是relationship来获取照片
    @property
    def photos(self):
        from app.models_daily_management import Photo
        return Photo.query.filter_by(reference_type='inspection', reference_id=self.id).all()

    def __repr__(self):
        return f'<InspectionRecord {self.inspection_type} {self.inspection_item}>'


class DiningCompanion(db.Model):
    """陪餐记录"""
    __tablename__ = 'dining_companions'

    id = db.Column(db.Integer, primary_key=True)
    daily_log_id = db.Column(db.Integer, ForeignKey('daily_logs.id'), nullable=False)
    companion_name = db.Column(db.String(100), nullable=False)
    companion_role = db.Column(db.String(100), nullable=False)  # 校长/主任/教师等
    meal_type = db.Column(db.String(10), nullable=False)  # 'breakfast', 'lunch', 'dinner'
    dining_time = db.Column(DATETIME2(precision=1), nullable=False)
    taste_rating = db.Column(db.Integer)  # 1-5星评价
    hygiene_rating = db.Column(db.Integer)  # 1-5星评价
    service_rating = db.Column(db.Integer)  # 1-5星评价
    comments = db.Column(Text)
    suggestions = db.Column(Text)
    photo_paths = db.Column(db.String(255))  # 存储多个照片路径，用分号分隔
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关联关系
    daily_log = relationship("DailyLog", back_populates="dining_companions")

    def __repr__(self):
        return f'<DiningCompanion {self.companion_name} {self.meal_type}>'


class CanteenTrainingRecord(db.Model):
    """食堂培训记录"""
    __tablename__ = 'canteen_training_records'

    id = db.Column(db.Integer, primary_key=True)
    daily_log_id = db.Column(db.Integer, ForeignKey('daily_logs.id'), nullable=False)
    training_topic = db.Column(db.String(200), nullable=False)
    trainer = db.Column(db.String(100), nullable=False)
    training_time = db.Column(DATETIME2(precision=1), nullable=False)
    location = db.Column(db.String(100))
    duration = db.Column(db.Integer)  # 培训时长(分钟)
    attendees_count = db.Column(db.Integer)  # 参训人数
    content_summary = db.Column(Text)
    effectiveness_evaluation = db.Column(Text)
    photo_paths = db.Column(db.String(255))  # 存储多个照片路径，用分号分隔
    created_by = db.Column(db.Integer, ForeignKey('users.id'))
    area_id = db.Column(db.Integer, ForeignKey('administrative_areas.id'))
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关联关系
    daily_log = relationship("DailyLog", back_populates="training_records")

    def __repr__(self):
        return f'<CanteenTrainingRecord {self.training_topic}>'


class SpecialEvent(db.Model):
    """特殊事件"""
    __tablename__ = 'special_events'

    id = db.Column(db.Integer, primary_key=True)
    daily_log_id = db.Column(db.Integer, ForeignKey('daily_logs.id'), nullable=False)
    event_type = db.Column(db.String(100), nullable=False)  # 突发事件/参观访问/检查督导等
    event_time = db.Column(DATETIME2(precision=1), nullable=False)
    description = db.Column(Text, nullable=False)
    participants = db.Column(Text)
    handling_measures = db.Column(Text)
    event_summary = db.Column(Text)
    photo_paths = db.Column(db.String(255))  # 存储多个照片路径，用分号分隔
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关联关系
    daily_log = relationship("DailyLog", back_populates="special_events_records")

    def __repr__(self):
        return f'<SpecialEvent {self.event_type}>'


class Issue(db.Model):
    """问题记录"""
    __tablename__ = 'issues'

    id = db.Column(db.Integer, primary_key=True)
    daily_log_id = db.Column(db.Integer, ForeignKey('daily_logs.id'), nullable=False)
    issue_type = db.Column(db.String(100), nullable=False)
    description = db.Column(Text, nullable=False)
    status = db.Column(db.String(10), default='pending')  # 'pending', 'fixing', 'fixed'
    found_time = db.Column(DATETIME2(precision=1), nullable=False)
    fixed_time = db.Column(DATETIME2(precision=1))
    responsible_person = db.Column(db.String(100))
    verification_result = db.Column(Text)
    photo_paths = db.Column(db.String(255))  # 存储多个照片路径，用分号分隔
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关联关系
    daily_log = relationship("DailyLog", back_populates="issues")

    def __repr__(self):
        return f'<Issue {self.issue_type}>'


class Photo(db.Model):
    """照片"""
    __tablename__ = 'photos'
    __table_args__ = (
        db.Index('idx_photos_reference', 'reference_type', 'reference_id'),
    )

    id = db.Column(db.Integer, primary_key=True)
    reference_id = db.Column(db.Integer, nullable=False)
    reference_type = db.Column(db.String(20), nullable=False)  # 'inspection', 'companion', 'training', 'event', 'issue'
    file_name = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    description = db.Column(db.String(255))
    rating = db.Column(db.Integer, default=3)  # 1-5星评分
    upload_time = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def __repr__(self):
        return f'<Photo {self.file_name}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'reference_id': self.reference_id,
            'reference_type': self.reference_type,
            'file_name': self.file_name,
            'file_path': self.file_path,
            'description': self.description,
            'rating': self.rating,
            'upload_time': self.upload_time.strftime('%Y-%m-%d %H:%M:%S') if self.upload_time else None
        }


class InspectionTemplate(db.Model):
    """检查项目模板"""
    __tablename__ = 'inspection_templates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.String(255))
    category = db.Column(db.String(50), nullable=False)  # 模板分类：卫生检查、食品安全、设备设施、人员管理等
    items = db.Column(Text, nullable=False)  # 存储JSON格式的检查项目列表
    is_default = db.Column(Boolean, default=False)  # 是否为默认模板
    created_by = db.Column(db.Integer, ForeignKey('users.id'))
    area_id = db.Column(db.Integer, ForeignKey('administrative_areas.id'))
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def __repr__(self):
        return f'<InspectionTemplate {self.name}>'

    def get_items(self):
        """获取检查项目列表"""
        try:
            return json.loads(self.items)
        except:
            return []

    def set_items(self, items_list):
        """设置检查项目列表"""
        self.items = json.dumps(items_list, ensure_ascii=False)

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'items': self.get_items(),
            'is_default': self.is_default,
            'created_by': self.created_by,
            'area_id': self.area_id,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
